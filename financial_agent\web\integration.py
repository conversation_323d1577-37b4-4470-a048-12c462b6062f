"""
Integration module to connect the web interface with the trading system.
"""
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging
import json

from fastapi import WebSocket

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.agents.analysis_agent import AnalysisAgent
from financial_agent.agents.strategy_agent import StrategyAgent, TradeSignal
from financial_agent.agents.execution_agent import ExecutionAgent
from financial_agent.agents.risk_agent import RiskManagementAgent, RiskAssessment
from financial_agent.agents.base_agent import AgentResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingSystem:
    """Main class to manage the trading system and web interface integration."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the trading system with configuration."""
        self.config = config
        self.running = False
        self.data_agent: Optional[DataCollectionAgent] = None
        self.analysis_agent: Optional[AnalysisAgent] = None
        self.strategy_agent: Optional[StrategyAgent] = None
        self.execution_agent: Optional[ExecutionAgent] = None
        self.risk_agent: Optional[RiskManagementAgent] = None
        self.connected_clients = set()
        self.portfolio = self._init_portfolio()
        self.risk_metrics = {}
        self.trade_history = []
        
    def _init_portfolio(self) -> Dict[str, Any]:
        """Initialize the portfolio with default values."""
        return {
            'current_value': self.config.get('initial_portfolio', 100000.0),
            'peak_value': self.config.get('initial_portfolio', 100000.0),
            'cash': self.config.get('initial_portfolio', 100000.0),
            'positions': {},
            'performance': {
                'daily': 0.0,
                'weekly': 0.0,
                'monthly': 0.0,
                'ytd': 0.0,
                'all_time': 0.0
            },
            'last_updated': datetime.utcnow().isoformat(),
            'sector_exposure': {},
            'daily_pnl': {}
        }
    
    async def initialize_agents(self):
        """Initialize all trading agents."""
        logger.info("Initializing trading agents...")
        
        # Initialize Data Collection Agent
        self.data_agent = DataCollectionAgent(config=self.config.get('data_agent', {}))
        await self.data_agent.initialize()
        
        # Initialize Analysis Agent
        self.analysis_agent = AnalysisAgent(config=self.config.get('analysis_agent', {}))
        
        # Initialize Strategy Agent
        self.strategy_agent = StrategyAgent(config=self.config.get('strategy_agent', {}))
        
        # Initialize Execution Agent
        self.execution_agent = ExecutionAgent(config=self.config.get('execution_agent', {}))
        
        # Initialize Risk Management Agent
        self.risk_agent = RiskManagementAgent(config=self.config.get('risk_agent', {}))
        
        logger.info("All trading agents initialized successfully")
    
    async def start(self):
        """Start the trading system."""
        if self.running:
            logger.warning("Trading system is already running")
            return
        
        logger.info("Starting trading system...")
        self.running = True
        
        try:
            # Initialize all agents
            await self.initialize_agents()
            
            # Start the main trading loop
            asyncio.create_task(self.trading_loop())
            
            # Start the data update loop for the web interface
            asyncio.create_task(self.update_web_interface_loop())
            
            logger.info("Trading system started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start trading system: {e}")
            self.running = False
            raise
    
    async def stop(self):
        """Stop the trading system."""
        logger.info("Stopping trading system...")
        self.running = False
        
        # Stop all agents
        if self.data_agent:
            await self.data_agent.stop()
        
        logger.info("Trading system stopped")
    
    async def trading_loop(self):
        """Main trading loop."""
        logger.info("Starting trading loop")
        
        while self.running:
            try:
                # 1. Fetch market data
                symbols = self.config.get('watchlist', ['SPY', 'QQQ', 'IWM', 'DIA'])
                timeframe = self.config.get('timeframe', '1d')
                
                # Fetch OHLCV data for all symbols
                ohlcv_data = {}
                for symbol in symbols:
                    data = await self.data_agent.fetch_ohlcv(
                        symbol=symbol,
                        timeframe=timeframe,
                        limit=100
                    )
                    if data and data.success:
                        ohlcv_data[symbol] = data.data
                
                if not ohlcv_data:
                    logger.warning("No OHLCV data received")
                    await asyncio.sleep(60)  # Wait before retrying
                    continue
                
                # 2. Analyze data
                analysis_results = {}
                for symbol, data in ohlcv_data.items():
                    result = await self.analysis_agent.analyze(data)
                    if result and result.success:
                        analysis_results[symbol] = result.data
                
                # 3. Generate trading signals
                signals = []
                for symbol, analysis in analysis_results.items():
                    signal = await self.strategy_agent.generate_signal(
                        symbol=symbol,
                        analysis=analysis,
                        current_price=ohlcv_data[symbol][-1]['close'] if ohlcv_data.get(symbol) else None
                    )
                    if signal and signal.success and signal.data:
                        signals.append(signal.data)
                
                # 4. Apply risk management
                approved_signals = []
                for signal in signals:
                    risk_assessment = await self.risk_agent.assess_risk(signal, self.portfolio)
                    if risk_assessment and risk_assessment.success and risk_assessment.data.get('approved', False):
                        approved_signals.append(signal)
                
                # 5. Execute approved trades
                for signal in approved_signals:
                    execution = await self.execution_agent.execute_trade(signal)
                    if execution and execution.success:
                        await self.update_portfolio(execution.data)
                        self.trade_history.append({
                            'timestamp': datetime.utcnow().isoformat(),
                            'symbol': signal.symbol,
                            'side': signal.side,
                            'quantity': signal.quantity,
                            'price': signal.price,
                            'value': signal.quantity * signal.price,
                            'status': 'filled'
                        })
                
                # Update performance metrics
                await self.update_performance_metrics()
                
                # Sleep until next iteration
                await asyncio.sleep(300)  # 5 minutes between iterations
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}", exc_info=True)
                await asyncio.sleep(60)  # Wait before retrying
    
    async def update_portfolio(self, trade_data: Dict[str, Any]):
        """Update portfolio with trade execution data."""
        symbol = trade_data['symbol']
        side = trade_data['side']
        quantity = trade_data['quantity']
        price = trade_data['price']
        
        # Calculate trade value
        trade_value = quantity * price
        
        if side.lower() == 'buy':
            # Update cash
            self.portfolio['cash'] -= trade_value
            
            # Update position
            if symbol in self.portfolio['positions']:
                position = self.portfolio['positions'][symbol]
                total_quantity = position['quantity'] + quantity
                position['avg_price'] = (
                    (position['quantity'] * position['avg_price'] + trade_value) / 
                    total_quantity
                )
                position['quantity'] = total_quantity
            else:
                self.portfolio['positions'][symbol] = {
                    'symbol': symbol,
                    'quantity': quantity,
                    'avg_price': price,
                    'current_price': price,
                    'sector': trade_data.get('sector', 'Unknown'),
                    'beta': trade_data.get('beta', 1.0)
                }
            
            # Update sector exposure
            sector = trade_data.get('sector', 'Unknown')
            if sector not in self.portfolio['sector_exposure']:
                self.portfolio['sector_exposure'][sector] = 0.0
            self.portfolio['sector_exposure'][sector] += trade_value
            
        elif side.lower() == 'sell':
            # Update cash
            self.portfolio['cash'] += trade_value
            
            # Update position
            if symbol in self.portfolio['positions']:
                position = self.portfolio['positions'][symbol]
                if quantity >= position['quantity']:
                    # Close the entire position
                    del self.portfolio['positions'][symbol]
                    
                    # Update sector exposure
                    sector = position.get('sector', 'Unknown')
                    if sector in self.portfolio['sector_exposure']:
                        self.portfolio['sector_exposure'][sector] -= (
                            position['quantity'] * position['avg_price']
                        )
                        if self.portfolio['sector_exposure'][sector] <= 0:
                            del self.portfolio['sector_exposure'][sector]
                else:
                    # Reduce position size
                    position['quantity'] -= quantity
                    
                    # Update sector exposure
                    sector = position.get('sector', 'Unknown')
                    if sector in self.portfolio['sector_exposure']:
                        self.portfolio['sector_exposure'][sector] -= trade_value
                        if self.portfolio['sector_exposure'][sector] <= 0:
                            del self.portfolio['sector_exposure'][sector]
        
        # Update portfolio value
        self.update_portfolio_value()
        
        # Update last updated timestamp
        self.portfolio['last_updated'] = datetime.utcnow().isoformat()
    
    def update_portfolio_value(self):
        """Update the total portfolio value based on current positions."""
        total_value = self.portfolio['cash']
        
        # Calculate value of all positions
        for symbol, position in self.portfolio['positions'].items():
            position_value = position['quantity'] * position['current_price']
            position['value'] = position_value
            position['pnl'] = position_value - (position['quantity'] * position['avg_price'])
            position['pnl_pct'] = (
                (position['current_price'] / position['avg_price'] - 1) * 100 
                if position['avg_price'] > 0 else 0
            )
            total_value += position_value
        
        # Update portfolio values
        self.portfolio['current_value'] = total_value
        self.portfolio['peak_value'] = max(self.portfolio['peak_value'], total_value)
    
    async def update_performance_metrics(self):
        """Update performance metrics for the portfolio."""
        # In a real implementation, this would calculate actual performance metrics
        # For now, we'll use placeholder values
        today = datetime.utcnow().date().isoformat()
        
        # Update daily P&L
        if 'daily_pnl' not in self.portfolio:
            self.portfolio['daily_pnl'] = {}
        
        # Calculate today's P&L
        prev_value = self.portfolio.get('prev_value', self.portfolio['current_value'])
        daily_pnl = self.portfolio['current_value'] - prev_value
        self.portfolio['daily_pnl'][today] = daily_pnl
        
        # Update performance metrics
        self.portfolio['performance'] = {
            'daily': daily_pnl / prev_value if prev_value > 0 else 0,
            'weekly': (self.portfolio['current_value'] / prev_value - 1) * 0.5,  # Placeholder
            'monthly': (self.portfolio['current_value'] / prev_value - 1) * 2.0,  # Placeholder
            'ytd': (self.portfolio['current_value'] / self.config.get('initial_portfolio', 100000.0) - 1) * 1.5,  # Placeholder
            'all_time': (self.portfolio['current_value'] / self.config.get('initial_portfolio', 100000.0) - 1)  # Since inception
        }
        
        # Save current value for next calculation
        self.portfolio['prev_value'] = self.portfolio['current_value']
        
        # Update risk metrics
        await self.update_risk_metrics()
    
    async def update_risk_metrics(self):
        """Update risk metrics for the portfolio."""
        if not self.risk_agent:
            return
        
        # Calculate drawdown
        peak_value = self.portfolio['peak_value']
        current_value = self.portfolio['current_value']
        drawdown = (peak_value - current_value) / peak_value if peak_value > 0 else 0
        
        # Calculate volatility (placeholder)
        volatility = 0.15  # Placeholder
        
        # Calculate beta (market correlation)
        beta = 1.0  # Placeholder
        
        # Calculate alpha (excess return)
        market_return = 0.08  # Placeholder
        alpha = (self.portfolio['performance']['all_time'] - market_return) * 100
        
        # Update risk metrics
        self.risk_metrics = {
            'volatility': volatility,
            'sharpe_ratio': (self.portfolio['performance']['all_time'] - 0.02) / volatility if volatility > 0 else 0,
            'max_drawdown': drawdown,
            'beta': beta,
            'alpha': alpha,
            'value_at_risk': current_value * volatility * 2.33,  # 99% VaR
            'last_updated': datetime.utcnow().isoformat()
        }
    
    async def update_web_interface_loop(self):
        """Periodically update the web interface with latest data."""
        logger.info("Starting web interface update loop")
        
        while self.running:
            try:
                # Update portfolio values with current market prices
                await self.update_market_prices()
                
                # Prepare data for web interface
                data = {
                    'portfolio': self.portfolio,
                    'risk': self.risk_metrics,
                    'trades': self.trade_history[-50:],  # Last 50 trades
                    'timestamp': datetime.utcnow().isoformat()
                }
                
                # Broadcast to all connected clients
                await self.broadcast_update(data)
                
                # Wait before next update
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in web interface update loop: {e}", exc_info=True)
                await asyncio.sleep(5)  # Wait before retrying
    
    async def update_market_prices(self):
        """Update current market prices for all positions."""
        if not self.data_agent or not self.portfolio['positions']:
            return
        
        # Get unique symbols from positions
        symbols = list(self.portfolio['positions'].keys())
        
        # Fetch latest prices
        for symbol in symbols:
            try:
                # In a real implementation, we would fetch the latest price
                # For now, we'll simulate small price changes
                position = self.portfolio['positions'][symbol]
                price_change = (0.5 - (hash(f"{symbol}{datetime.utcnow().minute}") % 100) / 100) * 0.02
                position['current_price'] = max(0.01, position['current_price'] * (1 + price_change))
                
            except Exception as e:
                logger.error(f"Error updating price for {symbol}: {e}")
        
        # Update portfolio value with new prices
        self.update_portfolio_value()
    
    # WebSocket connection management
    async def connect_client(self, websocket: WebSocket):
        """Register a new WebSocket client."""
        self.connected_clients.add(websocket)
        logger.info(f"New client connected. Total clients: {len(self.connected_clients)}")
        
        # Send current state to the new client
        try:
            data = {
                'portfolio': self.portfolio,
                'risk': self.risk_metrics,
                'trades': self.trade_history[-50:],
                'timestamp': datetime.utcnow().isoformat()
            }
            await websocket.send_json({
                'type': 'snapshot',
                'data': data
            })
        except Exception as e:
            logger.error(f"Error sending initial data to client: {e}")
    
    def disconnect_client(self, websocket: WebSocket):
        """Unregister a WebSocket client."""
        if websocket in self.connected_clients:
            self.connected_clients.remove(websocket)
            logger.info(f"Client disconnected. Remaining clients: {len(self.connected_clients)}")
    
    async def broadcast_update(self, data: Dict[str, Any]):
        """Broadcast update to all connected clients."""
        if not self.connected_clients:
            return
        
        message = json.dumps({
            'type': 'update',
            'data': data
        })
        
        # Send to all connected clients
        disconnected = set()
        for websocket in self.connected_clients:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error sending to client: {e}")
                disconnected.add(websocket)
        
        # Clean up disconnected clients
        for websocket in disconnected:
            self.disconnect_client(websocket)
    
    # API methods for web interface
    async def get_portfolio(self) -> Dict[str, Any]:
        """Get current portfolio data."""
        return self.portfolio
    
    async def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics."""
        return self.risk_metrics
    
    async def get_trades(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent trades."""
        return self.trade_history[-limit:]
    
    async def get_performance(self) -> Dict[str, Any]:
        """Get performance metrics."""
        return {
            'portfolio': self.portfolio,
            'risk': self.risk_metrics
        }
