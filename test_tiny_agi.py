#!/usr/bin/env python3
"""
Test Tiny AGI Implementation
Demonstrate realistic autonomous capabilities
"""

import sys
import time
from tiny_agi_implementation import TinyAGI

def test_tiny_agi_basic():
    """Test basic Tiny AGI functionality"""
    
    print("🧪 TESTING TINY AGI - BASIC FUNCTIONALITY")
    print("=" * 60)
    
    # Initialize Tiny AGI
    print("1. Initializing Tiny AGI...")
    tiny_agi = TinyAGI()
    
    # Test reasoning
    print("\n2. Testing autonomous reasoning...")
    reasoning_result = tiny_agi.autonomous_reasoning(
        "How can we improve compression efficiency?",
        "compression_research"
    )
    print(f"   🧠 Reasoning Result: {reasoning_result}")
    
    # Test safety checking
    print("\n3. Testing safety constraints...")
    safe_action = "optimize compression algorithm parameters"
    unsafe_action = "os.system('rm -rf /')"
    
    safe_score = tiny_agi.check_safety_constraints(safe_action)
    unsafe_score = tiny_agi.check_safety_constraints(unsafe_action)
    
    print(f"   ✅ Safe action score: {safe_score:.2f}")
    print(f"   ❌ Unsafe action score: {unsafe_score:.2f}")
    
    # Test improvement identification
    print("\n4. Testing improvement identification...")
    improvement = tiny_agi.identify_improvement_opportunity()
    print(f"   🎯 Identified improvement: {improvement['area']}")
    print(f"   📋 Opportunity: {improvement['opportunity']}")
    
    # Test knowledge storage
    print("\n5. Testing knowledge storage...")
    tiny_agi.store_knowledge(
        "compression", 
        "test_technique", 
        "1-bit quantization with outlier preservation", 
        0.9
    )
    print("   💾 Knowledge stored successfully")
    
    print("\n✅ BASIC FUNCTIONALITY TEST COMPLETE")
    return True

def test_tiny_agi_single_cycle():
    """Test single autonomous cycle"""
    
    print("\n🔄 TESTING TINY AGI - SINGLE AUTONOMOUS CYCLE")
    print("=" * 60)
    
    # Initialize Tiny AGI
    tiny_agi = TinyAGI()
    
    # Run single cycle
    print("Running autonomous cycle...")
    result = tiny_agi.run_single_cycle()
    
    print(f"\n📊 CYCLE RESULTS:")
    print(f"   🔄 Cycle: {result.get('cycle', 'N/A')}")
    print(f"   ✅ Success: {result.get('success', False)}")
    print(f"   🎯 Goal: {result.get('goal', 'N/A')}")
    print(f"   ⚡ Action: {result.get('action', 'N/A')}")
    print(f"   📈 Performance: {result.get('performance_improvement', 0):.1%}")
    print(f"   🔒 Safety Score: {result.get('safety_score', 0):.2f}")
    
    if result.get('success', False):
        print("\n✅ SINGLE CYCLE TEST PASSED")
    else:
        print(f"\n❌ SINGLE CYCLE TEST FAILED: {result.get('reason', 'Unknown')}")
    
    return result.get('success', False)

def test_tiny_agi_multi_cycle():
    """Test multiple autonomous cycles"""
    
    print("\n🚀 TESTING TINY AGI - MULTIPLE AUTONOMOUS CYCLES")
    print("=" * 60)
    
    # Initialize Tiny AGI
    tiny_agi = TinyAGI()
    
    # Run 3 cycles for testing
    print("Running 3 autonomous cycles...")
    results = tiny_agi.run_autonomous_cycles(max_cycles=3)
    
    # Analyze results
    successful_cycles = sum(1 for r in results if r.get('success', False))
    success_rate = successful_cycles / len(results) if results else 0
    
    print(f"\n📊 MULTI-CYCLE RESULTS:")
    print(f"   🔄 Total Cycles: {len(results)}")
    print(f"   ✅ Successful: {successful_cycles}")
    print(f"   📈 Success Rate: {success_rate:.1%}")
    
    if success_rate >= 0.6:  # 60% success rate threshold
        print("\n✅ MULTI-CYCLE TEST PASSED")
        return True
    else:
        print("\n❌ MULTI-CYCLE TEST FAILED")
        return False

def demonstrate_tiny_agi_capabilities():
    """Demonstrate what Tiny AGI can actually do"""
    
    print("\n🎯 TINY AGI CAPABILITY DEMONSTRATION")
    print("=" * 60)
    
    tiny_agi = TinyAGI()
    
    print("🔍 WHAT TINY AGI CAN DO:")
    print()
    
    # 1. Autonomous reasoning about compression
    print("1. 🧠 AUTONOMOUS REASONING:")
    reasoning_examples = [
        "What are the trade-offs between compression ratio and quality?",
        "How can we optimize memory usage during inference?",
        "What compression techniques work best for transformer models?"
    ]
    
    for i, prompt in enumerate(reasoning_examples, 1):
        print(f"   Question {i}: {prompt}")
        response = tiny_agi.autonomous_reasoning(prompt, "compression_research")
        print(f"   Response: {response[:100]}...")
        print()
    
    # 2. Safety constraint checking
    print("2. 🔒 SAFETY CONSTRAINT CHECKING:")
    test_actions = [
        ("Optimize compression parameters", True),
        ("Delete system files", False),
        ("Run compression benchmarks", True),
        ("Access network without permission", False)
    ]
    
    for action, expected_safe in test_actions:
        safety_score = tiny_agi.check_safety_constraints(action)
        is_safe = safety_score >= tiny_agi.safety_score_threshold
        status = "✅ SAFE" if is_safe else "❌ UNSAFE"
        print(f"   {action}: {status} (Score: {safety_score:.2f})")
    
    print()
    
    # 3. Improvement identification
    print("3. 🎯 IMPROVEMENT IDENTIFICATION:")
    improvement = tiny_agi.identify_improvement_opportunity()
    print(f"   Area: {improvement['area']}")
    print(f"   Opportunity: {improvement['opportunity']}")
    print(f"   Priority: {improvement['priority']}")
    print(f"   Expected Impact: {improvement['expected_impact']:.1%}")
    
    print()
    
    # 4. Knowledge management
    print("4. 💾 KNOWLEDGE MANAGEMENT:")
    knowledge_examples = [
        ("compression", "best_ratio", "32x achieved with 1-bit quantization", 0.95),
        ("optimization", "memory_target", "400MB RAM for 7B models", 0.90),
        ("quality", "preservation_rate", "99.5% quality retention possible", 0.85)
    ]
    
    for domain, key, value, confidence in knowledge_examples:
        tiny_agi.store_knowledge(domain, key, value, confidence)
        print(f"   Stored: {domain}.{key} = {value} (confidence: {confidence:.1%})")
    
    print()
    print("✅ CAPABILITY DEMONSTRATION COMPLETE")

def main():
    """Main test function"""
    
    print("🤖 TINY AGI TESTING SUITE")
    print("=" * 70)
    print("🎯 Testing realistic autonomous intelligence capabilities")
    print("🧠 Based on Loop Singular Bit compression system")
    print("🔒 Operating within safety constraints")
    print()
    
    test_results = []
    
    try:
        # Test 1: Basic functionality
        result1 = test_tiny_agi_basic()
        test_results.append(("Basic Functionality", result1))
        
        # Test 2: Single cycle
        result2 = test_tiny_agi_single_cycle()
        test_results.append(("Single Cycle", result2))
        
        # Test 3: Multiple cycles
        result3 = test_tiny_agi_multi_cycle()
        test_results.append(("Multiple Cycles", result3))
        
        # Demonstration
        demonstrate_tiny_agi_capabilities()
        
    except Exception as e:
        print(f"\n❌ TEST SUITE FAILED: {e}")
        return False
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 40)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - TINY AGI IS WORKING!")
    else:
        print("⚠️ SOME TESTS FAILED - CHECK IMPLEMENTATION")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
