#!/usr/bin/env python3
"""
🔬 VANILLA MODEL REAL TEST
=========================

GOAL: Get 100% REAL measurements for vanilla Mistral 7B:
1. Actual memory usage during loading
2. Real inference speed
3. Actual model size and parameters
4. Real performance metrics

NO ESTIMATES - ONLY MEASURED DATA
"""

import os
import torch
import time
import psutil
import json
import gc
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoConfig
from datetime import datetime

def get_memory_mb():
    """Get current memory usage in MB"""
    return psutil.Process().memory_info().rss / (1024**2)

def test_vanilla_model_step_by_step():
    """Test vanilla model loading step by step with memory monitoring"""
    
    print("🔬 VANILLA MISTRAL 7B REAL TEST")
    print("=" * 50)
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    # Initial memory
    initial_memory = get_memory_mb()
    print(f"📊 Initial memory: {initial_memory:.1f}MB")
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'initial_memory_mb': initial_memory,
        'steps': []
    }
    
    try:
        # Step 1: Load tokenizer
        print("\n📥 Step 1: Loading tokenizer...")
        start_time = time.time()
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        tokenizer_time = time.time() - start_time
        tokenizer_memory = get_memory_mb()
        
        step1 = {
            'step': 'tokenizer_loading',
            'time_seconds': tokenizer_time,
            'memory_mb': tokenizer_memory,
            'memory_increase_mb': tokenizer_memory - initial_memory
        }
        results['steps'].append(step1)
        print(f"   ✅ Tokenizer loaded: {tokenizer_time:.2f}s, {tokenizer_memory:.1f}MB")
        
        # Step 2: Load config
        print("\n📥 Step 2: Loading config...")
        start_time = time.time()
        config = AutoConfig.from_pretrained(model_path)
        
        config_time = time.time() - start_time
        config_memory = get_memory_mb()
        
        step2 = {
            'step': 'config_loading',
            'time_seconds': config_time,
            'memory_mb': config_memory,
            'memory_increase_mb': config_memory - tokenizer_memory,
            'model_info': {
                'hidden_size': config.hidden_size,
                'num_layers': config.num_hidden_layers,
                'vocab_size': config.vocab_size,
                'model_type': config.model_type
            }
        }
        results['steps'].append(step2)
        print(f"   ✅ Config loaded: {config_time:.2f}s, {config_memory:.1f}MB")
        print(f"   📋 Model info: {config.num_hidden_layers} layers, {config.hidden_size} hidden size")
        
        # Step 3: Attempt to load model with different strategies
        print("\n📥 Step 3: Loading model (trying different strategies)...")
        
        # Strategy 1: Try with low memory usage
        try:
            print("   🔄 Trying low_cpu_mem_usage=True...")
            start_time = time.time()
            start_memory = get_memory_mb()
            
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                low_cpu_mem_usage=True,
                trust_remote_code=True
            )
            
            load_time = time.time() - start_time
            load_memory = get_memory_mb()
            
            # Get model statistics
            total_params = sum(p.numel() for p in model.parameters())
            model_size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024**2)
            
            step3 = {
                'step': 'model_loading',
                'success': True,
                'strategy': 'low_cpu_mem_usage',
                'time_seconds': load_time,
                'memory_before_mb': start_memory,
                'memory_after_mb': load_memory,
                'memory_increase_mb': load_memory - start_memory,
                'total_parameters': total_params,
                'model_size_mb': model_size_mb,
                'dtype': str(model.dtype) if hasattr(model, 'dtype') else 'mixed'
            }
            results['steps'].append(step3)
            
            print(f"   ✅ Model loaded successfully!")
            print(f"      Time: {load_time:.2f}s")
            print(f"      Memory increase: {load_memory - start_memory:.1f}MB")
            print(f"      Total memory: {load_memory:.1f}MB")
            print(f"      Parameters: {total_params:,}")
            print(f"      Model size: {model_size_mb:.1f}MB")
            
            # Step 4: Test inference
            print("\n🚀 Step 4: Testing inference...")
            
            test_prompts = ["The future of AI is", "Technology will"]
            inference_results = []
            
            for i, prompt in enumerate(test_prompts):
                print(f"   🧠 Test {i+1}: '{prompt}'")
                
                # Tokenize
                inputs = tokenizer.encode(prompt, return_tensors="pt")
                if hasattr(model, 'device'):
                    inputs = inputs.to(model.device)
                
                # Measure inference
                start_time = time.time()
                start_memory = get_memory_mb()
                
                with torch.no_grad():
                    outputs = model.generate(
                        inputs,
                        max_new_tokens=5,
                        do_sample=False,
                        pad_token_id=tokenizer.eos_token_id,
                        use_cache=True
                    )
                
                inference_time = time.time() - start_time
                peak_memory = get_memory_mb()
                
                # Decode
                generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
                tokens_generated = len(outputs[0]) - len(inputs[0])
                
                inference_result = {
                    'prompt': prompt,
                    'generated_text': generated_text,
                    'tokens_generated': tokens_generated,
                    'inference_time_seconds': inference_time,
                    'tokens_per_second': tokens_generated / inference_time if inference_time > 0 else 0,
                    'memory_before_mb': start_memory,
                    'memory_peak_mb': peak_memory,
                    'memory_increase_mb': peak_memory - start_memory
                }
                
                inference_results.append(inference_result)
                print(f"      Generated: {tokens_generated} tokens in {inference_time:.2f}s")
                print(f"      Speed: {inference_result['tokens_per_second']:.1f} tokens/sec")
                print(f"      Memory: {peak_memory:.1f}MB")
            
            # Calculate averages
            avg_speed = sum(r['tokens_per_second'] for r in inference_results) / len(inference_results)
            avg_memory = sum(r['memory_peak_mb'] for r in inference_results) / len(inference_results)
            
            step4 = {
                'step': 'inference_testing',
                'success': True,
                'individual_tests': inference_results,
                'averages': {
                    'tokens_per_second': avg_speed,
                    'memory_peak_mb': avg_memory,
                    'inference_time_seconds': sum(r['inference_time_seconds'] for r in inference_results) / len(inference_results)
                }
            }
            results['steps'].append(step4)
            
            print(f"\n   📊 Average performance:")
            print(f"      Speed: {avg_speed:.1f} tokens/sec")
            print(f"      Memory: {avg_memory:.1f}MB")
            
            # Clean up
            del model
            gc.collect()
            
        except Exception as e:
            print(f"   ❌ Model loading failed: {e}")
            step3 = {
                'step': 'model_loading',
                'success': False,
                'error': str(e),
                'strategy': 'low_cpu_mem_usage'
            }
            results['steps'].append(step3)
        
        # Final memory check
        final_memory = get_memory_mb()
        results['final_memory_mb'] = final_memory
        results['total_memory_increase_mb'] = final_memory - initial_memory
        
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Initial memory: {initial_memory:.1f}MB")
        print(f"   Final memory: {final_memory:.1f}MB")
        print(f"   Total increase: {final_memory - initial_memory:.1f}MB")
        
        # Save results
        results_file = f"vanilla_model_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        return results
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        results['error'] = str(e)
        return results

def main():
    """Run vanilla model test"""
    
    print("🔬🔬🔬 VANILLA MISTRAL 7B REAL MEASUREMENTS 🔬🔬🔬")
    print("=" * 60)
    print("GETTING 100% REAL DATA FOR VANILLA MODEL")
    print()
    
    results = test_vanilla_model_step_by_step()
    
    print(f"\n🎯 TEST COMPLETE - ALL DATA IS REAL AND MEASURED")
    
    return results

if __name__ == "__main__":
    results = main()
