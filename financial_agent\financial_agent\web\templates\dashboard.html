<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Financial Agent</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        .sidebar {
            min-height: calc(100vh - 4rem);
        }
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .nav-link {
            transition: all 0.2s ease;
        }
        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-900">Financial Agent Dashboard</h1>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-500" id="last-updated">Loading...</span>
                <button class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                </button>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <div class="bg-indigo-800 text-white w-64 sidebar p-4">
            <nav>
                <div class="space-y-1">
                    <a href="#" class="nav-link flex items-center px-4 py-3 rounded-md text-sm font-medium text-white bg-indigo-900">
                        <i class="fas fa-tachometer-alt w-5 mr-3 text-center"></i>
                        Dashboard
                    </a>
                    <a href="#" class="nav-link flex items-center px-4 py-3 rounded-md text-sm font-medium text-indigo-100 hover:bg-indigo-700">
                        <i class="fas fa-chart-line w-5 mr-3 text-center"></i>
                        Performance
                    </a>
                    <a href="#" class="nav-link flex items-center px-4 py-3 rounded-md text-sm font-medium text-indigo-100 hover:bg-indigo-700">
                        <i class="fas fa-exchange-alt w-5 mr-3 text-center"></i>
                        Trades
                    </a>
                    <a href="#" class="nav-link flex items-center px-4 py-3 rounded-md text-sm font-medium text-indigo-100 hover:bg-indigo-700">
                        <i class="fas fa-shield-alt w-5 mr-3 text-center"></i>
                        Risk Management
                    </a>
                    <a href="#" class="nav-link flex items-center px-4 py-3 rounded-md text-sm font-medium text-indigo-100 hover:bg-indigo-700">
                        <i class="fas fa-cog w-5 mr-3 text-center"></i>
                        Settings
                    </a>
                </div>
                
                <div class="mt-8">
                    <h3 class="px-4 text-xs font-semibold text-indigo-300 uppercase tracking-wider">Portfolio</h3>
                    <div class="mt-2 space-y-1">
                        <a href="#" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-indigo-100 hover:bg-indigo-700 rounded-md">
                            <span class="w-2 h-2 mr-3 rounded-full bg-green-400"></span>
                            Active Trades
                        </a>
                        <a href="#" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-indigo-100 hover:bg-indigo-700 rounded-md">
                            <span class="w-2 h-2 mr-3 rounded-full bg-yellow-400"></span>
                            Watchlist
                        </a>
                        <a href="#" class="nav-link flex items-center px-4 py-2 text-sm font-medium text-indigo-100 hover:bg-indigo-700 rounded-md">
                            <span class="w-2 h-2 mr-3 rounded-full bg-blue-400"></span>
                            Historical Data
                        </a>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Portfolio Summary -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Portfolio Overview</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <!-- Total Value -->
                    <div class="bg-white p-6 rounded-lg shadow card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-indigo-100 text-indigo-600">
                                <i class="fas fa-wallet text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Value</p>
                                <p class="text-2xl font-semibold text-gray-900" id="portfolio-value">$0.00</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Daily Change -->
                    <div class="bg-white p-6 rounded-lg shadow card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-arrow-up text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Daily Change</p>
                                <div class="flex items-center">
                                    <p class="text-2xl font-semibold text-gray-900" id="daily-change">+$0.00</p>
                                    <span class="ml-2 text-sm font-medium text-green-600" id="daily-change-pct">+0.00%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- All Time Return -->
                    <div class="bg-white p-6 rounded-lg shadow card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-chart-line text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">All Time Return</p>
                                <div class="flex items-center">
                                    <p class="text-2xl font-semibold text-gray-900" id="all-time-change">+$0.00</p>
                                    <span class="ml-2 text-sm font-medium text-blue-600" id="all-time-change-pct">+0.00%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Risk Level -->
                    <div class="bg-white p-6 rounded-lg shadow card">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-shield-alt text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Risk Level</p>
                                <p class="text-2xl font-semibold text-gray-900">Medium</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Performance Chart -->
                <div class="bg-white p-6 rounded-lg shadow card">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Portfolio Performance</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium rounded bg-indigo-100 text-indigo-800">1D</button>
                            <button class="px-3 py-1 text-xs font-medium rounded hover:bg-gray-100">1W</button>
                            <button class="px-3 py-1 text-xs font-medium rounded hover:bg-gray-100">1M</button>
                            <button class="px-3 py-1 text-xs font-medium rounded hover:bg-gray-100">1Y</button>
                            <button class="px-3 py-1 text-xs font-medium rounded hover:bg-gray-100">ALL</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="performance-chart"></canvas>
                    </div>
                </div>
                
                <!-- Allocation Chart -->
                <div class="bg-white p-6 rounded-lg shadow card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Asset Allocation</h3>
                    <div class="flex flex-col md:flex-row">
                        <div class="w-full md:w-1/2 h-64">
                            <canvas id="allocation-chart"></canvas>
                        </div>
                        <div class="w-full md:w-1/2 mt-4 md:mt-0 md:pl-4">
                            <div id="allocation-legend" class="h-full flex items-center">
                                <ul class="w-full space-y-1">
                                    <li class="text-sm text-gray-500">Loading allocation data...</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Risk Metrics -->
            <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Risk Metrics</h3>
                </div>
                <div class="bg-gray-50 px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Volatility</p>
                            <p class="text-2xl font-semibold text-gray-900" id="volatility">0.00%</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Sharpe Ratio</p>
                            <p class="text-2xl font-semibold text-gray-900" id="sharpe-ratio">0.00</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Max Drawdown</p>
                            <p class="text-2xl font-semibold text-gray-900" id="max-drawdown">0.00%</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Beta</p>
                            <p class="text-2xl font-semibold text-gray-900" id="beta">0.00</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Positions -->
            <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Positions</h3>
                    <button class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-plus mr-2"></i>New Trade
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Cost</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Price</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sector</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risk</th>
                            </tr>
                        </thead>
                        <tbody id="positions-table" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">Loading positions...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Recent Trades -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Trades</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody id="trades-table" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">Loading trades...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
