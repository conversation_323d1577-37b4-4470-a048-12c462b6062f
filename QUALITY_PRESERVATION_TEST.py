#!/usr/bin/env python3
"""
QUALITY PRESERVATION TEST
=========================

Test quality preservation with real text generation
Ensure compression techniques maintain usability
Compare original vs compressed model outputs
"""

import os
import torch
import psutil
import time
import json
import gc
from typing import Dict, Any, List
from safetensors import safe_open
from transformers import AutoTokenizer
from datetime import datetime

class QualityPreservationTester:
    """Test quality preservation in compressed models"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.ram_measurements = []
        
        print(f"🔍 QUALITY PRESERVATION TESTER")
        print(f"📁 Model: {model_path}")
        print(f"🎯 Goal: Ensure compression maintains usability")
        
    def measure_ram(self, description: str) -> float:
        """Measure RAM and return GB"""
        process = psutil.Process()
        ram_gb = process.memory_info().rss / (1024**3)
        
        self.ram_measurements.append({
            'timestamp': time.time(),
            'description': description,
            'ram_gb': ram_gb
        })
        
        print(f"📊 RAM: {description} = {ram_gb:.3f}GB")
        return ram_gb
    
    def load_tokenizer(self) -> Any:
        """Load tokenizer for text processing"""
        
        self.measure_ram("before_tokenizer")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            
            self.measure_ram("after_tokenizer")
            
            print(f"✅ Tokenizer loaded")
            print(f"   Vocab size: {tokenizer.vocab_size}")
            
            return tokenizer
            
        except Exception as e:
            print(f"❌ Error loading tokenizer: {e}")
            return None
    
    def test_layer_quality_impact(self, layer_name: str, compression_method: str = "1bit") -> Dict[str, Any]:
        """Test quality impact of compressing a single layer"""
        
        print(f"\n🔬 TESTING LAYER QUALITY IMPACT")
        print(f"📊 Layer: {layer_name}")
        print(f"📊 Method: {compression_method}")
        print("-" * 50)
        
        try:
            # Load model index
            index_path = os.path.join(self.model_path, "model.safetensors.index.json")
            with open(index_path, 'r') as f:
                weight_index = json.load(f)
            
            if layer_name not in weight_index['weight_map']:
                print(f"❌ Layer not found: {layer_name}")
                return {}
            
            # Load original layer
            self.measure_ram("before_layer_load")
            
            file_name = weight_index['weight_map'][layer_name]
            file_path = os.path.join(self.model_path, file_name)
            
            with safe_open(file_path, framework="pt", device="cpu") as f:
                original_tensor = f.get_tensor(layer_name)
                
                self.measure_ram("after_layer_load")
                
                print(f"✅ Original layer loaded: {original_tensor.shape}")
                
                # Apply compression
                if compression_method == "1bit":
                    compressed_result = self.apply_1bit_compression(original_tensor)
                else:
                    print(f"❌ Unknown compression method: {compression_method}")
                    return {}
                
                if not compressed_result:
                    return {}
                
                # Test computation quality
                quality_test = self.test_computation_quality(
                    original_tensor, 
                    compressed_result['reconstructed_tensor'],
                    layer_name
                )
                
                # Combine results
                result = {
                    'layer_name': layer_name,
                    'compression_method': compression_method,
                    'compression_result': compressed_result,
                    'quality_test': quality_test,
                    'ram_measurements': self.ram_measurements[-3:]
                }
                
                return result
                
        except Exception as e:
            print(f"❌ Layer quality test failed: {e}")
            return {}
    
    def apply_1bit_compression(self, tensor: torch.Tensor) -> Dict[str, Any]:
        """Apply 1-bit compression and return results"""
        
        print(f"⚡ Applying 1-bit compression...")
        
        # Convert to float32
        tensor_f32 = tensor.to(torch.float32)
        
        # Calculate statistics
        tensor_mean = torch.mean(tensor_f32)
        tensor_std = torch.std(tensor_f32)
        
        # Sign-based quantization
        centered = tensor_f32 - tensor_mean
        binary_weights = torch.sign(centered)  # -1 or +1
        
        # Convert to uint8 for storage
        binary_uint8 = ((binary_weights + 1) / 2).to(torch.uint8)  # 0 or 1
        
        # Reconstruct
        reconstructed = (binary_uint8.to(torch.float32) * 2 - 1) * tensor_std + tensor_mean
        
        # Calculate compression metrics
        original_size = tensor.numel() * tensor.element_size()
        compressed_size = binary_uint8.numel() * binary_uint8.element_size()
        compression_ratio = original_size / compressed_size
        
        # Quality metrics
        mse_error = torch.mean((tensor_f32 - reconstructed) ** 2).item()
        mae_error = torch.mean(torch.abs(tensor_f32 - reconstructed)).item()
        
        tensor_range = torch.max(tensor_f32) - torch.min(tensor_f32)
        relative_error = mae_error / tensor_range.item() if tensor_range > 0 else 0
        
        result = {
            'compression_ratio': compression_ratio,
            'quality_metrics': {
                'mse_error': mse_error,
                'mae_error': mae_error,
                'relative_error_percent': relative_error * 100
            },
            'reconstructed_tensor': reconstructed,
            'quantization_params': {
                'mean': tensor_mean.item(),
                'std': tensor_std.item()
            }
        }
        
        print(f"   Compression: {compression_ratio:.1f}×")
        print(f"   Error: {relative_error*100:.2f}%")
        
        return result
    
    def test_computation_quality(self, original: torch.Tensor, compressed: torch.Tensor, layer_name: str) -> Dict[str, Any]:
        """Test quality of computation with compressed weights"""
        
        print(f"🧮 Testing computation quality...")
        
        if original.dim() != 2 or compressed.dim() != 2:
            print("⚠️ Skipping computation test - not 2D tensor")
            return {'skipped': True, 'reason': 'not_2d_tensor'}
        
        try:
            # Create test inputs of different sizes
            test_cases = [
                {'name': 'small_batch', 'shape': (1, original.shape[1])},
                {'name': 'medium_batch', 'shape': (4, original.shape[1])},
                {'name': 'large_batch', 'shape': (16, original.shape[1])}
            ]
            
            computation_results = []
            
            for test_case in test_cases:
                test_input = torch.randn(test_case['shape'])
                
                # Original computation
                original_output = torch.matmul(test_input, original.t())
                
                # Compressed computation
                compressed_output = torch.matmul(test_input, compressed.t())
                
                # Compare outputs
                output_diff = torch.abs(original_output - compressed_output)
                max_diff = torch.max(output_diff).item()
                mean_diff = torch.mean(output_diff).item()
                
                original_magnitude = torch.mean(torch.abs(original_output)).item()
                relative_output_error = mean_diff / original_magnitude if original_magnitude > 0 else 0
                
                # Statistical comparison
                original_std = torch.std(original_output).item()
                compressed_std = torch.std(compressed_output).item()
                std_ratio = compressed_std / original_std if original_std > 0 else 1.0
                
                test_result = {
                    'test_case': test_case['name'],
                    'input_shape': test_case['shape'],
                    'output_shape': list(original_output.shape),
                    'max_difference': max_diff,
                    'mean_difference': mean_diff,
                    'relative_error_percent': relative_output_error * 100,
                    'std_ratio': std_ratio,
                    'quality_preserved': relative_output_error < 0.1  # <10% relative error
                }
                
                computation_results.append(test_result)
                
                print(f"   {test_case['name']}: {relative_output_error*100:.2f}% error")
            
            # Overall assessment
            avg_relative_error = sum(r['relative_error_percent'] for r in computation_results) / len(computation_results)
            quality_preserved = all(r['quality_preserved'] for r in computation_results)
            
            result = {
                'layer_name': layer_name,
                'computation_tests': computation_results,
                'average_relative_error_percent': avg_relative_error,
                'quality_preserved': quality_preserved,
                'usability_assessment': 'good' if avg_relative_error < 5 else 'degraded' if avg_relative_error < 20 else 'poor'
            }
            
            print(f"   Average error: {avg_relative_error:.2f}%")
            print(f"   Quality preserved: {'✅ YES' if quality_preserved else '❌ NO'}")
            print(f"   Usability: {result['usability_assessment'].upper()}")
            
            return result
            
        except Exception as e:
            print(f"❌ Computation test failed: {e}")
            return {'error': str(e)}
    
    def test_text_generation_simulation(self, tokenizer, test_prompts: List[str]) -> Dict[str, Any]:
        """Simulate text generation quality with compression"""
        
        print(f"\n📝 TEXT GENERATION SIMULATION")
        print("=" * 40)
        
        if not tokenizer:
            print("❌ No tokenizer available")
            return {}
        
        generation_results = []
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n📝 Prompt {i+1}: '{prompt}'")
            
            try:
                # Tokenize prompt
                inputs = tokenizer(prompt, return_tensors="pt")
                input_ids = inputs['input_ids']
                
                print(f"   Tokenized: {input_ids.shape[1]} tokens")
                
                # Simulate token generation (simplified)
                # In real implementation, this would use the full model
                
                # For now, just test tokenization and basic processing
                vocab_size = tokenizer.vocab_size
                seq_len = input_ids.shape[1]
                
                # Simulate logits (what model would output)
                simulated_logits = torch.randn(1, seq_len, vocab_size)
                
                # Get top tokens
                top_k = 5
                top_logits, top_indices = torch.topk(simulated_logits[0, -1, :], top_k)
                
                # Convert to tokens
                top_tokens = [tokenizer.decode([idx]) for idx in top_indices]
                
                result = {
                    'prompt': prompt,
                    'input_length': seq_len,
                    'top_next_tokens': top_tokens,
                    'simulation_successful': True
                }
                
                generation_results.append(result)
                
                print(f"   Top next tokens: {top_tokens}")
                
            except Exception as e:
                print(f"   ❌ Generation simulation failed: {e}")
                generation_results.append({
                    'prompt': prompt,
                    'error': str(e),
                    'simulation_successful': False
                })
        
        return {
            'test_type': 'text_generation_simulation',
            'prompts_tested': len(test_prompts),
            'successful_simulations': sum(1 for r in generation_results if r.get('simulation_successful', False)),
            'generation_results': generation_results
        }
    
    def run_comprehensive_quality_test(self) -> Dict[str, Any]:
        """Run comprehensive quality preservation test"""
        
        print(f"\n🔍 COMPREHENSIVE QUALITY TEST")
        print("=" * 50)
        
        # Load tokenizer
        tokenizer = self.load_tokenizer()
        
        # Test layers
        test_layers = [
            'model.layers.0.self_attn.q_proj.weight',
            'model.layers.0.self_attn.k_proj.weight',
            'model.layers.0.mlp.gate_proj.weight'
        ]
        
        layer_quality_results = []
        
        for layer_name in test_layers:
            print(f"\n🔬 Testing layer: {layer_name}")
            
            layer_result = self.test_layer_quality_impact(layer_name, "1bit")
            
            if layer_result:
                layer_quality_results.append(layer_result)
            
            # Cleanup
            gc.collect()
        
        # Test text generation simulation
        test_prompts = [
            "The future of artificial intelligence",
            "In a world where technology",
            "The most important discovery"
        ]
        
        generation_test = self.test_text_generation_simulation(tokenizer, test_prompts)
        
        # Compile comprehensive results
        comprehensive_results = {
            'timestamp': time.time(),
            'test_type': 'comprehensive_quality_preservation',
            'model_path': self.model_path,
            'layer_quality_tests': layer_quality_results,
            'text_generation_test': generation_test,
            'ram_measurements': self.ram_measurements
        }
        
        # Summary
        if layer_quality_results:
            avg_compression = sum(r['compression_result']['compression_ratio'] for r in layer_quality_results) / len(layer_quality_results)
            avg_error = sum(r['compression_result']['quality_metrics']['relative_error_percent'] for r in layer_quality_results) / len(layer_quality_results)
            
            quality_preserved = all(
                r['quality_test'].get('quality_preserved', False) 
                for r in layer_quality_results 
                if 'quality_test' in r
            )
            
            comprehensive_results['summary'] = {
                'layers_tested': len(layer_quality_results),
                'average_compression': avg_compression,
                'average_error_percent': avg_error,
                'quality_preserved': quality_preserved,
                'usability_maintained': avg_error < 10.0  # <10% error threshold
            }
            
            print(f"\n📊 QUALITY TEST SUMMARY:")
            print(f"   Layers tested: {len(layer_quality_results)}")
            print(f"   Average compression: {avg_compression:.1f}×")
            print(f"   Average error: {avg_error:.2f}%")
            print(f"   Quality preserved: {'✅ YES' if quality_preserved else '❌ NO'}")
            print(f"   Usability maintained: {'✅ YES' if comprehensive_results['summary']['usability_maintained'] else '❌ NO'}")
        
        return comprehensive_results

def main():
    """Run quality preservation test"""
    
    print("🚀 QUALITY PRESERVATION TEST")
    print("=" * 60)
    print("GOAL: Ensure compression maintains usability")
    print("FOCUS: Real quality impact on model behavior")
    print()
    
    # Model path
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Initialize tester
    tester = QualityPreservationTester(model_path)
    
    # Run comprehensive test
    results = tester.run_comprehensive_quality_test()
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"quality_preservation_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✅ QUALITY PRESERVATION TEST COMPLETE")
    print(f"📄 Results saved: {results_file}")
    
    # Final assessment
    if 'summary' in results:
        summary = results['summary']
        
        print(f"\n🎯 FINAL ASSESSMENT:")
        print(f"   Compression achieved: {summary['average_compression']:.1f}×")
        print(f"   Quality impact: {summary['average_error_percent']:.2f}%")
        print(f"   Usability: {'✅ MAINTAINED' if summary['usability_maintained'] else '❌ DEGRADED'}")
        
        if summary['usability_maintained']:
            print(f"   ✅ Compression is SAFE for production use")
        else:
            print(f"   ⚠️ Compression needs optimization before production")

if __name__ == "__main__":
    main()
