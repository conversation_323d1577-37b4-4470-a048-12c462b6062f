#!/usr/bin/env python3
"""
Recursive Improvement Cycles - 10-Cycle Evolution Plan
- Multiple improvement cycles to accumulate gains
- Strategy refinement analysis
- Model optimization investigation
- Benchmark expansion for diverse problem types
"""

import sys
import time
import json
from pathlib import Path

# Add loop_singular_bit to path
sys.path.append(str(Path(__file__).parent.parent / 'loop_singular_bit'))

try:
    from loop_singular_bit import load_compressed_model
    from loop import LoopAGI
    from real_intelligence_benchmarks import RealIntelligenceBenchmarks
    from real_self_improvement import RealSelfImprovement
    
    print("🔄 EXECUTING RECURSIVE IMPROVEMENT PLAN: 10-CYCLE EVOLUTION")
    print("=" * 80)
    
    # Initialize Loop AGI system
    print("\n🔧 Initializing Loop AGI for Recursive Improvement...")
    loop_agi = LoopAGI()
    
    print(f"✅ System initialized with real loop_singular_bit model")
    print(f"📊 Starting recursive improvement cycles...")
    
    # Initialize systems
    benchmarks = RealIntelligenceBenchmarks(loop_agi.loop_singular_bit_model)
    self_improvement = RealSelfImprovement(loop_agi)
    
    # Track progress across cycles
    cycle_results = []
    
    # Baseline measurement
    print("\n📊 BASELINE MEASUREMENT")
    print("-" * 40)
    baseline_result = benchmarks.run_full_benchmark()
    baseline_score = baseline_result['overall_score']
    
    print(f"📊 Baseline Score: {baseline_score:.1f}% ({baseline_result['classification']})")
    
    cycle_results.append({
        'cycle': 0,
        'type': 'baseline',
        'score': baseline_score,
        'classification': baseline_result['classification'],
        'improvement': 0.0,
        'cumulative_improvement': 0.0
    })
    
    # Execute 10 improvement cycles
    print(f"\n🔄 EXECUTING 10 RECURSIVE IMPROVEMENT CYCLES")
    print("=" * 60)
    
    for cycle in range(1, 11):
        print(f"\n--- CYCLE {cycle}/10 ---")
        cycle_start_time = time.time()
        
        # Step 1: Run benchmark
        print(f"📊 Running benchmark for cycle {cycle}...")
        current_result = benchmarks.run_full_benchmark()
        current_score = current_result['overall_score']
        
        # Step 2: Calculate improvement
        previous_score = cycle_results[-1]['score']
        cycle_improvement = current_score - previous_score
        cumulative_improvement = current_score - baseline_score
        
        print(f"📈 Score: {current_score:.1f}% (Δ{cycle_improvement:+.2f})")
        print(f"📊 Classification: {current_result['classification']}")
        print(f"📈 Cumulative: {cumulative_improvement:+.2f} from baseline")
        
        # Step 3: Analyze failures and improve
        print(f"🔍 Analyzing failures and generating improvements...")
        improvement_result = self_improvement.execute_real_improvement_cycle(current_result)
        
        # Step 4: Strategy effectiveness analysis
        if cycle > 1:
            print(f"🔬 Analyzing strategy effectiveness...")
            
            # Check if strategies are working
            if cycle_improvement <= 0:
                print(f"⚠️ No improvement detected - analyzing strategy failures")
                
                # Analyze why strategies aren't working
                strategy_analysis = {
                    'cycle': cycle,
                    'improvement': cycle_improvement,
                    'strategies_applied': improvement_result.get('strategies_generated', 0),
                    'potential_issues': []
                }
                
                # Check for potential issues
                if improvement_result.get('failures_addressed', 0) == 0:
                    strategy_analysis['potential_issues'].append('No failures to address')
                
                if cycle_improvement == 0:
                    strategy_analysis['potential_issues'].append('Strategies not translating to performance')
                
                if current_result['classification'] == baseline_result['classification']:
                    strategy_analysis['potential_issues'].append('Classification unchanged')
                
                print(f"🔍 Strategy Issues Identified: {len(strategy_analysis['potential_issues'])}")
                for issue in strategy_analysis['potential_issues']:
                    print(f"   - {issue}")
            else:
                print(f"✅ Positive improvement detected: +{cycle_improvement:.2f}")
        
        # Step 5: Model optimization check
        if cycle == 5:  # Mid-cycle optimization check
            print(f"\n🔧 MID-CYCLE MODEL OPTIMIZATION CHECK")
            print("-" * 40)
            
            # Check if compression is limiting performance
            print(f"🔍 Investigating compression limitations...")
            
            # Test model response quality
            test_prompt = "Solve step by step: 2x + 5 = 17"
            model_response = loop_agi.loop_singular_bit_model.generate(test_prompt, max_length=100)
            
            print(f"📝 Model Response Quality Test:")
            print(f"   Prompt: {test_prompt}")
            print(f"   Response: {model_response[:100]}...")
            print(f"   Response Length: {len(model_response)} characters")
            
            # Analyze response quality
            quality_indicators = {
                'contains_solution': 'x = 6' in model_response or '6' in model_response,
                'shows_steps': 'step' in model_response.lower() or '-' in model_response,
                'coherent_text': len(model_response.split()) > 5,
                'relevant_content': any(word in model_response.lower() for word in ['solve', 'equation', 'x'])
            }
            
            quality_score = sum(quality_indicators.values()) / len(quality_indicators) * 100
            print(f"📊 Response Quality Score: {quality_score:.1f}%")
            
            if quality_score < 50:
                print(f"⚠️ Compression may be limiting reasoning capability")
            else:
                print(f"✅ Model quality appears adequate for reasoning")
        
        # Step 6: Benchmark expansion (cycles 7-10)
        if cycle >= 7:
            print(f"📊 EXPANDED BENCHMARK TESTING")
            print("-" * 30)
            
            # Add more diverse problem types
            expanded_problems = [
                {
                    "type": "pattern_recognition",
                    "problem": "What comes next: 2, 4, 8, 16, ?",
                    "expected": "32"
                },
                {
                    "type": "analogical_reasoning", 
                    "problem": "Cat is to kitten as dog is to ?",
                    "expected": "puppy"
                },
                {
                    "type": "spatial_reasoning",
                    "problem": "If you rotate a square 90 degrees, what shape do you get?",
                    "expected": "square"
                }
            ]
            
            expanded_correct = 0
            for problem in expanded_problems:
                try:
                    response = loop_agi.loop_singular_bit_model.generate(
                        f"Answer this {problem['type']} problem: {problem['problem']}", 
                        max_length=50
                    )
                    
                    if problem['expected'].lower() in response.lower():
                        expanded_correct += 1
                        print(f"   ✅ {problem['type']}: Correct")
                    else:
                        print(f"   ❌ {problem['type']}: Incorrect")
                        print(f"      Expected: {problem['expected']}")
                        print(f"      Got: {response[:50]}...")
                        
                except Exception as e:
                    print(f"   ⚠️ {problem['type']}: Error - {e}")
            
            expanded_score = (expanded_correct / len(expanded_problems)) * 100
            print(f"📊 Expanded Benchmark Score: {expanded_score:.1f}%")
        
        # Record cycle results
        cycle_end_time = time.time()
        cycle_duration = cycle_end_time - cycle_start_time
        
        cycle_data = {
            'cycle': cycle,
            'type': 'improvement',
            'score': current_score,
            'classification': current_result['classification'],
            'improvement': cycle_improvement,
            'cumulative_improvement': cumulative_improvement,
            'duration': cycle_duration,
            'strategies_generated': improvement_result.get('strategies_generated', 0),
            'failures_addressed': improvement_result.get('failures_addressed', 0)
        }
        
        if cycle >= 7:
            cycle_data['expanded_score'] = expanded_score
        
        cycle_results.append(cycle_data)
        
        print(f"⏱️ Cycle Duration: {cycle_duration:.1f}s")
        
        # Early termination check
        if cumulative_improvement >= 10.0:  # 10+ point improvement
            print(f"🎉 SIGNIFICANT IMPROVEMENT ACHIEVED!")
            print(f"📈 Cumulative improvement: {cumulative_improvement:.2f} points")
            print(f"🏆 Early termination - target exceeded")
            break
        
        # Progress update
        if cycle % 3 == 0:
            print(f"\n📊 PROGRESS UPDATE (Cycle {cycle})")
            print(f"   Current Score: {current_score:.1f}%")
            print(f"   Cumulative Improvement: {cumulative_improvement:+.2f}")
            print(f"   Classification: {current_result['classification']}")
            print(f"   Cycles Completed: {cycle}/10")
    
    # Final Analysis
    print(f"\n" + "="*80)
    print(f"🏆 RECURSIVE IMPROVEMENT PLAN COMPLETE")
    print(f"="*80)
    
    final_result = cycle_results[-1]
    total_improvement = final_result['cumulative_improvement']
    cycles_completed = len([c for c in cycle_results if c['type'] == 'improvement'])
    
    print(f"📊 FINAL RESULTS:")
    print(f"   Baseline Score: {baseline_score:.1f}%")
    print(f"   Final Score: {final_result['score']:.1f}%")
    print(f"   Total Improvement: {total_improvement:+.2f} points")
    print(f"   Classification Change: {baseline_result['classification']} → {final_result['classification']}")
    print(f"   Cycles Completed: {cycles_completed}/10")
    
    # Improvement analysis
    positive_cycles = len([c for c in cycle_results[1:] if c['improvement'] > 0])
    negative_cycles = len([c for c in cycle_results[1:] if c['improvement'] < 0])
    neutral_cycles = len([c for c in cycle_results[1:] if c['improvement'] == 0])
    
    print(f"\n📈 IMPROVEMENT PATTERN ANALYSIS:")
    print(f"   Positive Improvement Cycles: {positive_cycles}")
    print(f"   Negative Improvement Cycles: {negative_cycles}")
    print(f"   Neutral Cycles: {neutral_cycles}")
    print(f"   Improvement Consistency: {(positive_cycles/cycles_completed)*100:.1f}%")
    
    # Strategy effectiveness
    total_strategies = sum(c.get('strategies_generated', 0) for c in cycle_results[1:])
    total_failures = sum(c.get('failures_addressed', 0) for c in cycle_results[1:])
    
    print(f"\n🛠️ STRATEGY EFFECTIVENESS:")
    print(f"   Total Strategies Generated: {total_strategies}")
    print(f"   Total Failures Addressed: {total_failures}")
    print(f"   Improvement per Strategy: {total_improvement/max(1, total_strategies):.3f} points")
    
    # Success assessment
    if total_improvement >= 5.0:
        success_level = "🎉 HIGHLY SUCCESSFUL"
    elif total_improvement >= 2.0:
        success_level = "✅ SUCCESSFUL"
    elif total_improvement > 0:
        success_level = "📈 PARTIALLY SUCCESSFUL"
    else:
        success_level = "⚠️ NEEDS FURTHER IMPROVEMENT"
    
    print(f"\n🎯 RECURSIVE IMPROVEMENT ASSESSMENT: {success_level}")
    
    # Save results for analysis
    results_file = Path("recursive_improvement_results.json")
    with open(results_file, 'w') as f:
        json.dump({
            'baseline': baseline_result,
            'cycle_results': cycle_results,
            'total_improvement': total_improvement,
            'success_level': success_level
        }, f, indent=2)
    
    print(f"💾 Results saved to: {results_file}")
    print(f"\n🔬 All improvements measured with real benchmarks - no simulation!")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Execution failed: {e}")
    import traceback
    traceback.print_exc()
