@echo off
setlocal

:: Set Python executable
set PYTHON=python

:: Install dependencies
echo Installing dependencies...
%PYTHON% -m pip install -r requirements.txt
%PYTHON% -m pip install -r requirements-web.txt

:: Set environment variables
echo Setting up environment...
set HOST=0.0.0.0
set PORT=8000
set DEBUG=true

:: Start the web server
echo.
echo =======================================
echo  Starting Financial Agent Web Interface
echo  Web Interface: http://%HOST%:%PORT%
echo  Press Ctrl+C to stop
echo =======================================
echo.

%PYTHON% run_web.py

endlocal
