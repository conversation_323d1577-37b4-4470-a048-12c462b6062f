#!/usr/bin/env python3
"""
Real Loop Singular Bit Benchmarks
Comprehensive testing and quality assessment of the actual Loop Singular Bit system
"""

import os
import sys
import time
import json
import psutil
import tracemalloc
from datetime import datetime
from pathlib import Path

# Add loop_singular_bit to path
sys.path.append('loop_singular_bit')
from loop_singular_bit import LoopSingularBit, load_compressed_model, get_system_info

class RealLoopBenchmarks:
    """Real benchmarks for Loop Singular Bit system"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'system_info': {},
            'compression_tests': {},
            'generation_tests': {},
            'memory_tests': {},
            'performance_tests': {},
            'quality_tests': {},
            'overall_score': 0.0
        }
        
        print("🔬 REAL LOOP SINGULAR BIT BENCHMARKS")
        print("=" * 50)
        print("📊 Testing actual system performance and quality")
        print("🎯 No simulations - only real measurements")
        print()
    
    def run_all_benchmarks(self):
        """Run comprehensive benchmarks"""
        
        print("🚀 Starting comprehensive benchmark suite...")
        
        # System info
        self.test_system_info()
        
        # Compression tests
        self.test_compression_capabilities()
        
        # Memory tests
        self.test_memory_usage()
        
        # Generation tests
        self.test_text_generation()
        
        # Performance tests
        self.test_performance()
        
        # Quality tests
        self.test_quality_preservation()
        
        # Calculate overall score
        self.calculate_overall_score()
        
        # Save results
        self.save_results()
        
        return self.results
    
    def test_system_info(self):
        """Test system information and capabilities"""
        
        print("\n📊 TESTING SYSTEM INFORMATION")
        print("-" * 35)
        
        try:
            # Get system info
            info = get_system_info()
            self.results['system_info'] = info
            
            print(f"✅ Version: {info.get('version', 'Unknown')}")
            print(f"✅ Status: {info.get('status', 'Unknown')}")
            
            # Test capabilities
            capabilities = info.get('capabilities', {})
            for capability, status in capabilities.items():
                status_icon = "✅" if status else "❌"
                print(f"{status_icon} {capability}: {status}")
            
            # Test proven results
            proven = info.get('proven_results', {})
            for metric, value in proven.items():
                print(f"📈 {metric}: {value}")
            
            self.results['system_info']['test_status'] = 'PASSED'
            
        except Exception as e:
            print(f"❌ System info test failed: {e}")
            self.results['system_info']['test_status'] = 'FAILED'
            self.results['system_info']['error'] = str(e)
    
    def test_compression_capabilities(self):
        """Test real compression capabilities"""
        
        print("\n🗜️ TESTING COMPRESSION CAPABILITIES")
        print("-" * 40)
        
        try:
            # Initialize system
            system = LoopSingularBit()
            
            # Test model loading
            start_time = time.time()
            model = system.load_compressed_model("mistral-7b-v0.1")
            load_time = time.time() - start_time
            
            if model:
                model_info = model.get_info()
                
                compression_results = {
                    'model_loaded': True,
                    'load_time_seconds': load_time,
                    'compression_ratio': model_info.get('compression_ratio', 0),
                    'ram_usage_mb': model_info.get('ram_usage_mb', 0),
                    'quality_preservation': model_info.get('quality_preservation', 0),
                    'is_real_compression': model.is_real,
                    'status': model_info.get('status', 'Unknown')
                }
                
                print(f"✅ Model loaded in {load_time:.2f}s")
                print(f"✅ Compression ratio: {compression_results['compression_ratio']:.1f}×")
                print(f"✅ RAM usage: {compression_results['ram_usage_mb']:.1f}MB")
                print(f"✅ Quality preservation: {compression_results['quality_preservation']:.1f}%")
                print(f"✅ Real compression: {compression_results['is_real_compression']}")
                
                self.results['compression_tests'] = compression_results
                self.results['compression_tests']['test_status'] = 'PASSED'
                
                return model
                
            else:
                print("❌ Model loading failed")
                self.results['compression_tests'] = {
                    'model_loaded': False,
                    'test_status': 'FAILED',
                    'error': 'Model loading failed'
                }
                return None
                
        except Exception as e:
            print(f"❌ Compression test failed: {e}")
            self.results['compression_tests'] = {
                'test_status': 'FAILED',
                'error': str(e)
            }
            return None
    
    def test_memory_usage(self):
        """Test real memory usage"""
        
        print("\n💾 TESTING MEMORY USAGE")
        print("-" * 25)
        
        try:
            # Start memory tracking
            tracemalloc.start()
            process = psutil.Process()
            
            # Baseline memory
            baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
            print(f"📊 Baseline memory: {baseline_memory:.1f}MB")
            
            # Load model and measure memory
            system = LoopSingularBit()
            model = system.load_compressed_model("mistral-7b-v0.1")
            
            # Memory after loading
            loaded_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = loaded_memory - baseline_memory
            
            print(f"📊 Memory after loading: {loaded_memory:.1f}MB")
            print(f"📊 Memory increase: {memory_increase:.1f}MB")
            
            # Test generation memory
            if model:
                generation_start_memory = process.memory_info().rss / 1024 / 1024
                
                # Generate text
                output = model.generate("Test prompt for memory measurement", max_length=100)
                
                generation_end_memory = process.memory_info().rss / 1024 / 1024
                generation_memory_increase = generation_end_memory - generation_start_memory
                
                print(f"📊 Generation memory increase: {generation_memory_increase:.1f}MB")
                print(f"📊 Total memory usage: {generation_end_memory:.1f}MB")
            
            # Get tracemalloc stats
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            memory_results = {
                'baseline_memory_mb': baseline_memory,
                'loaded_memory_mb': loaded_memory,
                'memory_increase_mb': memory_increase,
                'generation_memory_increase_mb': generation_memory_increase if model else 0,
                'peak_memory_mb': peak / 1024 / 1024,
                'current_memory_mb': current / 1024 / 1024,
                'test_status': 'PASSED'
            }
            
            print(f"✅ Peak memory tracked: {peak / 1024 / 1024:.1f}MB")
            
            self.results['memory_tests'] = memory_results
            
        except Exception as e:
            print(f"❌ Memory test failed: {e}")
            self.results['memory_tests'] = {
                'test_status': 'FAILED',
                'error': str(e)
            }
    
    def test_text_generation(self):
        """Test real text generation capabilities"""
        
        print("\n🔮 TESTING TEXT GENERATION")
        print("-" * 30)
        
        try:
            # Load model
            model = load_compressed_model("mistral-7b-v0.1")
            
            if not model:
                raise Exception("Model loading failed")
            
            # Test prompts
            test_prompts = [
                "The future of artificial intelligence is",
                "Machine learning algorithms can",
                "Deep neural networks are",
                "Quantum computing will",
                "The benefits of model compression include"
            ]
            
            generation_results = {
                'prompts_tested': len(test_prompts),
                'successful_generations': 0,
                'failed_generations': 0,
                'average_generation_time': 0,
                'generations': [],
                'test_status': 'PASSED'
            }
            
            total_time = 0
            
            for i, prompt in enumerate(test_prompts):
                print(f"📝 Testing prompt {i+1}/{len(test_prompts)}: '{prompt[:30]}...'")
                
                try:
                    start_time = time.time()
                    output = model.generate(prompt, max_length=50)
                    generation_time = time.time() - start_time
                    
                    total_time += generation_time
                    generation_results['successful_generations'] += 1
                    
                    generation_data = {
                        'prompt': prompt,
                        'output': output,
                        'generation_time': generation_time,
                        'output_length': len(output),
                        'success': True
                    }
                    
                    print(f"   ✅ Generated in {generation_time:.3f}s")
                    print(f"   📝 Output: {output[:100]}...")
                    
                except Exception as e:
                    generation_results['failed_generations'] += 1
                    generation_data = {
                        'prompt': prompt,
                        'error': str(e),
                        'success': False
                    }
                    print(f"   ❌ Generation failed: {e}")
                
                generation_results['generations'].append(generation_data)
            
            # Calculate average time
            if generation_results['successful_generations'] > 0:
                generation_results['average_generation_time'] = total_time / generation_results['successful_generations']
            
            print(f"✅ Successful generations: {generation_results['successful_generations']}/{len(test_prompts)}")
            print(f"✅ Average generation time: {generation_results['average_generation_time']:.3f}s")
            
            self.results['generation_tests'] = generation_results
            
        except Exception as e:
            print(f"❌ Generation test failed: {e}")
            self.results['generation_tests'] = {
                'test_status': 'FAILED',
                'error': str(e)
            }
    
    def test_performance(self):
        """Test system performance"""
        
        print("\n⚡ TESTING PERFORMANCE")
        print("-" * 22)
        
        try:
            # Load model
            model = load_compressed_model("mistral-7b-v0.1")
            
            if not model:
                raise Exception("Model loading failed")
            
            # Performance tests
            performance_results = {
                'load_time_tests': [],
                'generation_speed_tests': [],
                'memory_efficiency_tests': [],
                'test_status': 'PASSED'
            }
            
            # Test load times (multiple runs)
            print("📊 Testing load times...")
            for i in range(3):
                start_time = time.time()
                test_model = load_compressed_model("mistral-7b-v0.1")
                load_time = time.time() - start_time
                performance_results['load_time_tests'].append(load_time)
                print(f"   Run {i+1}: {load_time:.3f}s")
            
            avg_load_time = sum(performance_results['load_time_tests']) / len(performance_results['load_time_tests'])
            print(f"✅ Average load time: {avg_load_time:.3f}s")
            
            # Test generation speeds
            print("📊 Testing generation speeds...")
            test_prompt = "Performance test prompt"
            
            for length in [10, 25, 50]:
                start_time = time.time()
                output = model.generate(test_prompt, max_length=length)
                generation_time = time.time() - start_time
                
                speed_data = {
                    'max_length': length,
                    'generation_time': generation_time,
                    'output_length': len(output),
                    'tokens_per_second': len(output.split()) / generation_time if generation_time > 0 else 0
                }
                
                performance_results['generation_speed_tests'].append(speed_data)
                print(f"   Length {length}: {generation_time:.3f}s ({speed_data['tokens_per_second']:.1f} tokens/s)")
            
            self.results['performance_tests'] = performance_results
            
        except Exception as e:
            print(f"❌ Performance test failed: {e}")
            self.results['performance_tests'] = {
                'test_status': 'FAILED',
                'error': str(e)
            }
    
    def test_quality_preservation(self):
        """Test quality preservation"""
        
        print("\n✨ TESTING QUALITY PRESERVATION")
        print("-" * 35)
        
        try:
            # Load model
            model = load_compressed_model("mistral-7b-v0.1")
            
            if not model:
                raise Exception("Model loading failed")
            
            # Quality tests
            quality_results = {
                'coherence_tests': [],
                'relevance_tests': [],
                'fluency_tests': [],
                'overall_quality_score': 0,
                'test_status': 'PASSED'
            }
            
            # Test coherence
            print("📊 Testing coherence...")
            coherence_prompts = [
                "Explain the concept of machine learning",
                "Describe the benefits of neural networks",
                "What is artificial intelligence?"
            ]
            
            for prompt in coherence_prompts:
                output = model.generate(prompt, max_length=100)
                
                # Simple coherence scoring (length, completeness)
                coherence_score = min(100, len(output) / 2)  # Simple scoring
                quality_results['coherence_tests'].append({
                    'prompt': prompt,
                    'output': output,
                    'coherence_score': coherence_score
                })
                print(f"   Prompt: '{prompt[:30]}...' Score: {coherence_score:.1f}")
            
            # Calculate overall quality
            if quality_results['coherence_tests']:
                avg_coherence = sum(t['coherence_score'] for t in quality_results['coherence_tests']) / len(quality_results['coherence_tests'])
                quality_results['overall_quality_score'] = avg_coherence
                print(f"✅ Overall quality score: {avg_coherence:.1f}/100")
            
            self.results['quality_tests'] = quality_results
            
        except Exception as e:
            print(f"❌ Quality test failed: {e}")
            self.results['quality_tests'] = {
                'test_status': 'FAILED',
                'error': str(e)
            }
    
    def calculate_overall_score(self):
        """Calculate overall system score"""
        
        print("\n📊 CALCULATING OVERALL SCORE")
        print("-" * 30)
        
        scores = []
        
        # System info score
        if self.results['system_info'].get('test_status') == 'PASSED':
            scores.append(100)
            print("✅ System info: 100/100")
        else:
            scores.append(0)
            print("❌ System info: 0/100")
        
        # Compression score
        compression = self.results.get('compression_tests', {})
        if compression.get('test_status') == 'PASSED':
            compression_score = min(100, compression.get('compression_ratio', 0) * 3)  # 32x = 96 points
            scores.append(compression_score)
            print(f"✅ Compression: {compression_score:.1f}/100")
        else:
            scores.append(0)
            print("❌ Compression: 0/100")
        
        # Memory score
        memory = self.results.get('memory_tests', {})
        if memory.get('test_status') == 'PASSED':
            # Lower memory usage = higher score
            memory_mb = memory.get('loaded_memory_mb', 1000)
            memory_score = max(0, 100 - (memory_mb / 10))  # 1000MB = 0 points, 0MB = 100 points
            scores.append(memory_score)
            print(f"✅ Memory efficiency: {memory_score:.1f}/100")
        else:
            scores.append(0)
            print("❌ Memory efficiency: 0/100")
        
        # Generation score
        generation = self.results.get('generation_tests', {})
        if generation.get('test_status') == 'PASSED':
            success_rate = generation.get('successful_generations', 0) / max(1, generation.get('prompts_tested', 1))
            generation_score = success_rate * 100
            scores.append(generation_score)
            print(f"✅ Text generation: {generation_score:.1f}/100")
        else:
            scores.append(0)
            print("❌ Text generation: 0/100")
        
        # Performance score
        performance = self.results.get('performance_tests', {})
        if performance.get('test_status') == 'PASSED':
            # Based on load time and generation speed
            load_times = performance.get('load_time_tests', [10])
            avg_load_time = sum(load_times) / len(load_times)
            performance_score = max(0, 100 - (avg_load_time * 10))  # 10s = 0 points
            scores.append(performance_score)
            print(f"✅ Performance: {performance_score:.1f}/100")
        else:
            scores.append(0)
            print("❌ Performance: 0/100")
        
        # Quality score
        quality = self.results.get('quality_tests', {})
        if quality.get('test_status') == 'PASSED':
            quality_score = quality.get('overall_quality_score', 0)
            scores.append(quality_score)
            print(f"✅ Quality: {quality_score:.1f}/100")
        else:
            scores.append(0)
            print("❌ Quality: 0/100")
        
        # Calculate overall score
        if scores:
            overall_score = sum(scores) / len(scores)
            self.results['overall_score'] = overall_score
            print(f"\n🎯 OVERALL SCORE: {overall_score:.1f}/100")
            
            # Grade
            if overall_score >= 90:
                grade = "A+ (Excellent)"
            elif overall_score >= 80:
                grade = "A (Very Good)"
            elif overall_score >= 70:
                grade = "B (Good)"
            elif overall_score >= 60:
                grade = "C (Fair)"
            else:
                grade = "D (Needs Improvement)"
            
            print(f"🏆 GRADE: {grade}")
            self.results['grade'] = grade
        else:
            self.results['overall_score'] = 0
            self.results['grade'] = "F (Failed)"
    
    def save_results(self):
        """Save benchmark results"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"loop_singular_bit_benchmarks_{timestamp}.json"
        
        try:
            with open(results_file, 'w') as f:
                json.dump(self.results, f, indent=2)
            
            print(f"\n💾 Results saved to: {results_file}")
            
        except Exception as e:
            print(f"⚠️ Failed to save results: {e}")

def main():
    """Run real Loop Singular Bit benchmarks"""
    
    print("🔬 REAL LOOP SINGULAR BIT BENCHMARKS")
    print("=" * 60)
    print("📊 Comprehensive testing of actual system performance")
    print("🎯 No simulations - only real measurements and results")
    print()
    
    # Run benchmarks
    benchmarks = RealLoopBenchmarks()
    results = benchmarks.run_all_benchmarks()
    
    # Summary
    print(f"\n🎉 BENCHMARK COMPLETE!")
    print("=" * 30)
    print(f"📊 Overall Score: {results['overall_score']:.1f}/100")
    print(f"🏆 Grade: {results['grade']}")
    print(f"⏱️ Timestamp: {results['timestamp']}")
    
    return results

if __name__ == "__main__":
    results = main()
