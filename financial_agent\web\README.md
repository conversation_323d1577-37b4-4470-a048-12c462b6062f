# Financial Agent Web Interface

A modern, real-time web interface for monitoring and managing the Financial Agent trading system.

## Features

- Real-time portfolio tracking
- Interactive performance charts
- Position and trade monitoring
- Risk metrics and analytics
- Responsive design for desktop and mobile

## Prerequisites

- Python 3.8+
- Node.js 14+ (for frontend assets, if customization is needed)

## Installation

1. Install Python dependencies:

```bash
pip install -r requirements-web.txt
```

2. (Optional) Install frontend dependencies if you need to modify the JavaScript/CSS:

```bash
cd static
npm install
```

## Running the Application

Start the web server:

```bash
uvicorn financial_agent.web.app:app --reload
```

The web interface will be available at http://localhost:8000

## Development

### Project Structure

```
web/
├── app.py                 # FastAPI application
├── requirements-web.txt    # Python dependencies
├── static/                # Static files (JS, CSS, images)
│   └── js/
│       └── dashboard.js  # Main dashboard JavaScript
└── templates/             # HTML templates
    └── dashboard.html     # Main dashboard template
```

### Environment Variables

Create a `.env` file in the project root with the following variables:

```
# Web server configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true
SECRET_KEY=your-secret-key-here
```

### API Endpoints

- `GET /` - Main dashboard
- `GET /api/portfolio` - Get portfolio data
- `GET /api/trades` - Get recent trades
- `GET /api/risk` - Get risk metrics
- `GET /api/performance` - Get performance metrics
- `WS /ws` - WebSocket for real-time updates

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
