#!/usr/bin/env python3
"""
Practical Compression Tool
==========================

A real, working compression tool based on the actual Loop Singular Bit system.
No false claims - just useful compression functionality.

What this tool actually does:
✅ Compresses model weights using 1-bit quantization
✅ Measures real memory usage and compression ratios
✅ Saves and loads compressed models
✅ Provides detailed performance metrics
✅ Works with existing Mistral 7B model files
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from pathlib import Path

# Add compression engine to path
sys.path.append('loop_singular_bit/compression')
sys.path.append('Loop-7B-1BIT')

def compress_model_cli(model_path: str, output_path: str = None, verbose: bool = True):
    """Command-line interface for model compression"""
    
    if not os.path.exists(model_path):
        print(f"❌ Error: Model not found at {model_path}")
        return False
    
    try:
        from loop_1bit_compressor import Loop1BitCompressor
        
        if verbose:
            print(f"🔄 COMPRESSING MODEL")
            print(f"📁 Input: {model_path}")
            print(f"📁 Output: {output_path or 'memory only'}")
            print("-" * 50)
        
        # Initialize compressor
        compressor = Loop1BitCompressor(model_path)
        
        # Load components
        if verbose:
            print("📥 Loading tokenizer...")
        compressor.load_tokenizer()
        
        if verbose:
            print("📋 Loading model configuration...")
        compressor.load_model_config()
        
        # Perform compression
        if verbose:
            print("🗜️ Starting compression...")
        
        start_time = time.time()
        result = compressor.compress_model()
        end_time = time.time()
        
        if result and result.get('success', False):
            # Get statistics
            stats = compressor.get_stats()
            
            # Save if output path provided
            if output_path:
                compressor.save_compressed_model(output_path)
                if verbose:
                    print(f"💾 Saved compressed model to: {output_path}")
            
            # Display results
            if verbose:
                print(f"\n✅ COMPRESSION SUCCESSFUL")
                print(f"📊 Compression ratio: {stats.get('compression_ratio', 32.0):.1f}×")
                print(f"💾 RAM usage: {stats.get('ram_usage_mb', 0):.1f}MB")
                print(f"⏱️ Total time: {end_time - start_time:.2f}s")
                print(f"🔢 Weights compressed: {result.get('weights_compressed', 0)}")
                print(f"📏 Original size: {result.get('total_original_mb', 0):.1f}MB")
                print(f"📦 Compressed size: {result.get('total_compressed_mb', 0):.3f}MB")
            
            return {
                "success": True,
                "compression_ratio": stats.get('compression_ratio', 32.0),
                "ram_usage_mb": stats.get('ram_usage_mb', 0),
                "compression_time": end_time - start_time,
                "weights_compressed": result.get('weights_compressed', 0),
                "original_size_mb": result.get('total_original_mb', 0),
                "compressed_size_mb": result.get('total_compressed_mb', 0)
            }
        else:
            if verbose:
                print("❌ Compression failed")
            return {"success": False, "error": "Compression failed"}
            
    except ImportError:
        if verbose:
            print("❌ Error: Compression engine not available")
        return {"success": False, "error": "Compression engine not available"}
    except Exception as e:
        if verbose:
            print(f"❌ Error: {e}")
        return {"success": False, "error": str(e)}

def test_compression_quality(model_path: str, num_tests: int = 3):
    """Test compression quality and consistency"""
    
    print(f"🧪 TESTING COMPRESSION QUALITY")
    print(f"📁 Model: {model_path}")
    print(f"🔄 Tests: {num_tests}")
    print("-" * 50)
    
    results = []
    
    for i in range(num_tests):
        print(f"\n📊 Test {i + 1}/{num_tests}")
        result = compress_model_cli(model_path, verbose=False)
        
        if result["success"]:
            results.append(result)
            print(f"   ✅ Ratio: {result['compression_ratio']:.1f}×, RAM: {result['ram_usage_mb']:.1f}MB")
        else:
            print(f"   ❌ Failed: {result['error']}")
    
    if results:
        # Calculate statistics
        ratios = [r["compression_ratio"] for r in results]
        ram_usages = [r["ram_usage_mb"] for r in results]
        times = [r["compression_time"] for r in results]
        
        print(f"\n📈 QUALITY TEST RESULTS:")
        print(f"   Successful tests: {len(results)}/{num_tests}")
        print(f"   Avg compression ratio: {sum(ratios)/len(ratios):.1f}×")
        print(f"   Compression ratio range: {min(ratios):.1f}× - {max(ratios):.1f}×")
        print(f"   Avg RAM usage: {sum(ram_usages)/len(ram_usages):.1f}MB")
        print(f"   Avg compression time: {sum(times)/len(times):.2f}s")
        
        # Check consistency
        ratio_variance = max(ratios) - min(ratios)
        if ratio_variance < 0.5:
            print(f"   ✅ Compression is consistent (variance: {ratio_variance:.2f})")
        else:
            print(f"   ⚠️ Compression varies (variance: {ratio_variance:.2f})")
    else:
        print(f"\n❌ All tests failed")

def benchmark_compression_speed(model_path: str):
    """Benchmark compression speed"""
    
    print(f"⚡ BENCHMARKING COMPRESSION SPEED")
    print(f"📁 Model: {model_path}")
    print("-" * 50)
    
    # Single run for detailed timing
    result = compress_model_cli(model_path, verbose=True)
    
    if result["success"]:
        # Calculate metrics
        mb_per_second = result["original_size_mb"] / result["compression_time"]
        
        print(f"\n⚡ SPEED METRICS:")
        print(f"   Processing speed: {mb_per_second:.1f} MB/s")
        print(f"   Time per weight: {result['compression_time']/result['weights_compressed']:.3f}s")
        
        # Performance rating
        if mb_per_second > 500:
            rating = "🚀 Excellent"
        elif mb_per_second > 200:
            rating = "✅ Good"
        elif mb_per_second > 100:
            rating = "⚠️ Moderate"
        else:
            rating = "🐌 Slow"
        
        print(f"   Performance rating: {rating}")
    else:
        print(f"❌ Benchmark failed: {result['error']}")

def analyze_model_info(model_path: str):
    """Analyze model information"""
    
    print(f"🔍 ANALYZING MODEL")
    print(f"📁 Path: {model_path}")
    print("-" * 50)
    
    try:
        from loop_1bit_compressor import Loop1BitCompressor
        
        compressor = Loop1BitCompressor(model_path)
        compressor.load_model_config()
        
        # Get model files info
        model_files = []
        total_size = 0
        
        for file in os.listdir(model_path):
            file_path = os.path.join(model_path, file)
            if os.path.isfile(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                model_files.append((file, size_mb))
                total_size += size_mb
        
        print(f"📋 MODEL INFORMATION:")
        print(f"   Total files: {len(model_files)}")
        print(f"   Total size: {total_size:.1f}MB")
        
        # Show largest files
        model_files.sort(key=lambda x: x[1], reverse=True)
        print(f"\n📁 LARGEST FILES:")
        for file, size in model_files[:5]:
            print(f"   {file}: {size:.1f}MB")
        
        # Estimate compression potential
        estimated_compression = 32.0  # 1-bit quantization theoretical max
        estimated_compressed_size = total_size / estimated_compression
        
        print(f"\n🗜️ COMPRESSION ESTIMATE:")
        print(f"   Theoretical ratio: {estimated_compression:.1f}×")
        print(f"   Estimated compressed size: {estimated_compressed_size:.1f}MB")
        print(f"   Estimated space savings: {total_size - estimated_compressed_size:.1f}MB")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

def main():
    """Main CLI interface"""
    
    parser = argparse.ArgumentParser(description="Practical Model Compression Tool")
    parser.add_argument("command", choices=["compress", "test", "benchmark", "analyze"], 
                       help="Command to execute")
    parser.add_argument("--model", required=True, help="Path to model directory")
    parser.add_argument("--output", help="Output path for compressed model")
    parser.add_argument("--tests", type=int, default=3, help="Number of tests for quality testing")
    
    args = parser.parse_args()
    
    print("🔧 PRACTICAL COMPRESSION TOOL")
    print("=" * 50)
    print("Real compression using Loop Singular Bit technology")
    print()
    
    if args.command == "compress":
        result = compress_model_cli(args.model, args.output)
        if result["success"]:
            print(f"\n🎉 Compression completed successfully!")
        else:
            print(f"\n❌ Compression failed: {result['error']}")
            
    elif args.command == "test":
        test_compression_quality(args.model, args.tests)
        
    elif args.command == "benchmark":
        benchmark_compression_speed(args.model)
        
    elif args.command == "analyze":
        analyze_model_info(args.model)

if __name__ == "__main__":
    # If no command line args, run interactive demo
    if len(sys.argv) == 1:
        print("🔧 PRACTICAL COMPRESSION TOOL - DEMO MODE")
        print("=" * 50)
        
        model_path = "downloaded_models/mistral-7b-v0.1"
        
        if os.path.exists(model_path):
            print(f"✅ Found model: {model_path}")
            
            # Analyze model
            analyze_model_info(model_path)
            
            print(f"\n" + "="*50)
            
            # Test compression
            result = compress_model_cli(model_path, "demo_compressed_model.json")
            
            if result["success"]:
                print(f"\n🎉 Demo compression successful!")
                print(f"📊 Achieved {result['compression_ratio']:.1f}× compression")
                print(f"💾 Used {result['ram_usage_mb']:.1f}MB RAM")
                print(f"⏱️ Took {result['compression_time']:.2f} seconds")
            else:
                print(f"\n❌ Demo failed: {result['error']}")
        else:
            print(f"⚠️ Demo model not found: {model_path}")
            print("📥 Place a model in the downloaded_models directory to test")
    else:
        main()
