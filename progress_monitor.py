#!/usr/bin/env python3
"""
PROGRESS MONITORING SYSTEM
==========================

Monitor progress of all parallel research activities:
1. GPT-J 6B download progress
2. Enhanced compression algorithm development
3. Autonomous research iterations
4. Combined system testing

Provides real-time status updates and optimization recommendations.
"""

import time
import json
import logging
from pathlib import Path
from typing import Dict, Any, List
import psutil
import os

logger = logging.getLogger(__name__)

class ProgressMonitor:
    """Monitor progress of all research activities"""
    
    def __init__(self):
        self.start_time = time.time()
        self.activities = {
            'gptj_download': {'status': 'running', 'progress': 0.0, 'eta': None},
            'phase1_compression': {'status': 'completed', 'progress': 100.0, 'eta': None},
            'phase2_compression': {'status': 'running', 'progress': 0.0, 'eta': None},
            'autonomous_research': {'status': 'running', 'progress': 0.0, 'eta': None},
            'combined_testing': {'status': 'running', 'progress': 0.0, 'eta': None}
        }
        
        logger.info(f"📊 Progress Monitor initialized")
        logger.info(f"   Tracking {len(self.activities)} parallel activities")
    
    def check_gptj_download_progress(self) -> Dict[str, Any]:
        """Check GPT-J 6B download progress"""
        
        download_path = Path("D:/Loop/downloaded_models/gpt_j_6b")
        
        if download_path.exists():
            # Check for model files
            model_files = list(download_path.glob("**/pytorch_model.bin"))
            
            if model_files:
                model_file = model_files[0]
                current_size_gb = model_file.stat().st_size / (1024**3)
                target_size_gb = 24.2
                progress = min(current_size_gb / target_size_gb, 1.0)
                
                # Estimate ETA based on progress
                elapsed_time = time.time() - self.start_time
                if progress > 0.01:  # Avoid division by zero
                    eta_seconds = (elapsed_time / progress) * (1 - progress)
                    eta_hours = eta_seconds / 3600
                else:
                    eta_hours = None
                
                return {
                    'status': 'downloading' if progress < 1.0 else 'completed',
                    'progress': progress * 100,
                    'current_size_gb': current_size_gb,
                    'target_size_gb': target_size_gb,
                    'eta_hours': eta_hours
                }
            else:
                return {
                    'status': 'starting',
                    'progress': 0.0,
                    'current_size_gb': 0.0,
                    'target_size_gb': 24.2,
                    'eta_hours': None
                }
        else:
            return {
                'status': 'not_started',
                'progress': 0.0,
                'current_size_gb': 0.0,
                'target_size_gb': 24.2,
                'eta_hours': None
            }
    
    def check_compression_results(self) -> Dict[str, Any]:
        """Check compression algorithm results"""
        
        results = {}
        
        # Phase 1 results
        phase1_file = Path("enhanced_compression_comprehensive_results.json")
        if phase1_file.exists():
            try:
                with open(phase1_file, 'r') as f:
                    phase1_data = json.load(f)
                
                # Get best configuration
                best_config = None
                best_compression = 0
                
                for config_name, config_data in phase1_data.get('detailed_results', {}).items():
                    compression_ratio = config_data['compression_results']['summary']['overall_compression_ratio']
                    if compression_ratio > best_compression:
                        best_compression = compression_ratio
                        best_config = config_name
                
                results['phase1'] = {
                    'status': 'completed',
                    'best_config': best_config,
                    'best_compression': best_compression,
                    'configurations_tested': len(phase1_data.get('detailed_results', {}))
                }
            except Exception as e:
                results['phase1'] = {'status': 'error', 'error': str(e)}
        else:
            results['phase1'] = {'status': 'not_found'}
        
        # Phase 2 results
        phase2_file = Path("enhanced_compression_phase2_results.json")
        if phase2_file.exists():
            try:
                with open(phase2_file, 'r') as f:
                    phase2_data = json.load(f)
                
                best_compression = 0
                for config_name, config_data in phase2_data.items():
                    compression_ratio = config_data['summary']['overall_compression_ratio']
                    if compression_ratio > best_compression:
                        best_compression = compression_ratio
                
                results['phase2'] = {
                    'status': 'completed',
                    'best_compression': best_compression,
                    'configurations_tested': len(phase2_data)
                }
            except Exception as e:
                results['phase2'] = {'status': 'error', 'error': str(e)}
        else:
            results['phase2'] = {'status': 'running'}
        
        # Combined results
        combined_file = Path("combined_enhanced_compression_results.json")
        if combined_file.exists():
            try:
                with open(combined_file, 'r') as f:
                    combined_data = json.load(f)
                
                best_compression = 0
                for config_name, config_data in combined_data.items():
                    compression_ratio = config_data['compression_results']['summary']['overall_compression_ratio']
                    if compression_ratio > best_compression:
                        best_compression = compression_ratio
                
                results['combined'] = {
                    'status': 'completed',
                    'best_compression': best_compression,
                    'configurations_tested': len(combined_data)
                }
            except Exception as e:
                results['combined'] = {'status': 'error', 'error': str(e)}
        else:
            results['combined'] = {'status': 'running'}
        
        return results
    
    def check_autonomous_research_progress(self) -> Dict[str, Any]:
        """Check autonomous research progress"""
        
        research_file = Path("autonomous_research_results.json")
        
        if research_file.exists():
            try:
                with open(research_file, 'r') as f:
                    research_data = json.load(f)
                
                summary = research_data.get('summary', {})
                
                return {
                    'status': 'completed',
                    'iterations_completed': summary.get('total_iterations', 0),
                    'variants_tested': summary.get('total_variants_tested', 0),
                    'research_time_minutes': summary.get('total_time_minutes', 0),
                    'best_algorithms': len(research_data.get('best_algorithms', {}))
                }
            except Exception as e:
                return {'status': 'error', 'error': str(e)}
        else:
            return {'status': 'running'}
    
    def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage"""
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available_gb = memory.available / (1024**3)
        
        # Disk usage
        disk = psutil.disk_usage('D:/')
        disk_free_gb = disk.free / (1024**3)
        disk_percent = (disk.used / disk.total) * 100
        
        # Process count
        process_count = len(psutil.pids())
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'memory_available_gb': memory_available_gb,
            'disk_free_gb': disk_free_gb,
            'disk_percent': disk_percent,
            'process_count': process_count
        }
    
    def generate_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive status report"""
        
        logger.info(f"📊 Generating status report...")
        
        # Check all activities
        gptj_progress = self.check_gptj_download_progress()
        compression_results = self.check_compression_results()
        research_progress = self.check_autonomous_research_progress()
        system_resources = self.check_system_resources()
        
        # Calculate overall progress
        total_elapsed = time.time() - self.start_time
        
        report = {
            'timestamp': time.time(),
            'total_elapsed_hours': total_elapsed / 3600,
            'gptj_download': gptj_progress,
            'compression_algorithms': compression_results,
            'autonomous_research': research_progress,
            'system_resources': system_resources,
            'recommendations': self._generate_recommendations(
                gptj_progress, compression_results, research_progress, system_resources
            )
        }
        
        return report
    
    def _generate_recommendations(self, gptj_progress: Dict, compression_results: Dict, 
                                research_progress: Dict, system_resources: Dict) -> List[str]:
        """Generate optimization recommendations"""
        
        recommendations = []
        
        # GPT-J download recommendations
        if gptj_progress['status'] == 'downloading':
            if gptj_progress.get('eta_hours', 0) > 2:
                recommendations.append("GPT-J download will take >2 hours - continue other research")
            if gptj_progress['progress'] > 50:
                recommendations.append("GPT-J download >50% complete - prepare compression pipeline")
        
        # Compression algorithm recommendations
        phase1_status = compression_results.get('phase1', {}).get('status')
        phase2_status = compression_results.get('phase2', {}).get('status')
        combined_status = compression_results.get('combined', {}).get('status')
        
        if phase1_status == 'completed' and phase2_status == 'completed':
            if combined_status != 'completed':
                recommendations.append("Phase 1 & 2 complete - run combined compression test")
        
        if phase1_status == 'completed':
            phase1_compression = compression_results['phase1'].get('best_compression', 0)
            if phase1_compression > 8:
                recommendations.append(f"Phase 1 achieved {phase1_compression:.1f}× compression - excellent!")
            elif phase1_compression > 5:
                recommendations.append(f"Phase 1 achieved {phase1_compression:.1f}× compression - good progress")
        
        # Research recommendations
        if research_progress['status'] == 'completed':
            variants_tested = research_progress.get('variants_tested', 0)
            if variants_tested > 20:
                recommendations.append("Autonomous research tested many variants - analyze best algorithms")
        
        # Resource recommendations
        if system_resources['memory_percent'] > 80:
            recommendations.append("High memory usage - consider reducing parallel processes")
        
        if system_resources['disk_free_gb'] < 50:
            recommendations.append("Low disk space - monitor GPT-J download space usage")
        
        if system_resources['cpu_percent'] > 90:
            recommendations.append("High CPU usage - system working at capacity")
        
        # Strategic recommendations
        if (phase1_status == 'completed' and 
            gptj_progress['progress'] > 80):
            recommendations.append("Ready for large-scale testing - prepare GPT-J compression pipeline")
        
        return recommendations
    
    def print_status_report(self, report: Dict[str, Any]):
        """Print formatted status report"""
        
        logger.info(f"\n📊 COMPREHENSIVE STATUS REPORT")
        logger.info(f"=" * 60)
        logger.info(f"⏱️  Total elapsed: {report['total_elapsed_hours']:.1f} hours")
        
        # GPT-J Download Status
        gptj = report['gptj_download']
        logger.info(f"\n📥 GPT-J 6B DOWNLOAD:")
        logger.info(f"   Status: {gptj['status']}")
        logger.info(f"   Progress: {gptj['progress']:.1f}%")
        logger.info(f"   Size: {gptj['current_size_gb']:.1f}GB / {gptj['target_size_gb']:.1f}GB")
        if gptj.get('eta_hours'):
            logger.info(f"   ETA: {gptj['eta_hours']:.1f} hours")
        
        # Compression Algorithms Status
        comp = report['compression_algorithms']
        logger.info(f"\n🔬 COMPRESSION ALGORITHMS:")
        
        if 'phase1' in comp:
            phase1 = comp['phase1']
            logger.info(f"   Phase 1: {phase1['status']}")
            if phase1['status'] == 'completed':
                logger.info(f"      Best compression: {phase1.get('best_compression', 0):.1f}×")
                logger.info(f"      Configurations tested: {phase1.get('configurations_tested', 0)}")
        
        if 'phase2' in comp:
            phase2 = comp['phase2']
            logger.info(f"   Phase 2: {phase2['status']}")
            if phase2['status'] == 'completed':
                logger.info(f"      Best compression: {phase2.get('best_compression', 0):.1f}×")
        
        if 'combined' in comp:
            combined = comp['combined']
            logger.info(f"   Combined: {combined['status']}")
            if combined['status'] == 'completed':
                logger.info(f"      Best compression: {combined.get('best_compression', 0):.1f}×")
        
        # Autonomous Research Status
        research = report['autonomous_research']
        logger.info(f"\n🧠 AUTONOMOUS RESEARCH:")
        logger.info(f"   Status: {research['status']}")
        if research['status'] == 'completed':
            logger.info(f"   Iterations: {research.get('iterations_completed', 0)}")
            logger.info(f"   Variants tested: {research.get('variants_tested', 0)}")
            logger.info(f"   Research time: {research.get('research_time_minutes', 0):.1f} minutes")
        
        # System Resources
        resources = report['system_resources']
        logger.info(f"\n💻 SYSTEM RESOURCES:")
        logger.info(f"   CPU: {resources['cpu_percent']:.1f}%")
        logger.info(f"   Memory: {resources['memory_percent']:.1f}% ({resources['memory_available_gb']:.1f}GB available)")
        logger.info(f"   Disk: {resources['disk_percent']:.1f}% ({resources['disk_free_gb']:.1f}GB free)")
        
        # Recommendations
        recommendations = report['recommendations']
        if recommendations:
            logger.info(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"   {i}. {rec}")
        
        logger.info(f"\n" + "=" * 60)
    
    def continuous_monitoring(self, interval_minutes: int = 5, max_duration_hours: int = 8):
        """Run continuous monitoring"""
        
        logger.info(f"🔄 Starting continuous monitoring...")
        logger.info(f"   Interval: {interval_minutes} minutes")
        logger.info(f"   Max duration: {max_duration_hours} hours")
        
        start_time = time.time()
        iteration = 0
        
        while True:
            iteration += 1
            
            # Generate and print status report
            logger.info(f"\n📊 MONITORING ITERATION {iteration}")
            report = self.generate_status_report()
            self.print_status_report(report)
            
            # Save report
            report_file = Path(f"status_report_{int(time.time())}.json")
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            # Check if we should continue
            elapsed_hours = (time.time() - start_time) / 3600
            if elapsed_hours >= max_duration_hours:
                logger.info(f"⏰ Maximum monitoring duration reached ({max_duration_hours} hours)")
                break
            
            # Check if all major activities are complete
            gptj_complete = report['gptj_download']['status'] == 'completed'
            research_complete = report['autonomous_research']['status'] == 'completed'
            
            if gptj_complete and research_complete:
                logger.info(f"🎉 All major activities completed!")
                break
            
            # Wait for next iteration
            logger.info(f"⏳ Waiting {interval_minutes} minutes for next check...")
            time.sleep(interval_minutes * 60)
        
        logger.info(f"📊 Monitoring complete after {iteration} iterations")

def run_progress_monitoring():
    """Run progress monitoring system"""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    logger.info("📊 PROGRESS MONITORING SYSTEM")
    logger.info("=" * 60)
    logger.info("🎯 Monitoring all parallel research activities")
    
    monitor = ProgressMonitor()
    
    # Generate initial status report
    logger.info(f"\n📋 INITIAL STATUS CHECK:")
    initial_report = monitor.generate_status_report()
    monitor.print_status_report(initial_report)
    
    # Start continuous monitoring
    monitor.continuous_monitoring(interval_minutes=10, max_duration_hours=6)
    
    logger.info(f"\n🎉 PROGRESS MONITORING COMPLETE!")

if __name__ == "__main__":
    run_progress_monitoring()
