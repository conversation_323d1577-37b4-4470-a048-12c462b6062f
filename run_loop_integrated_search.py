#!/usr/bin/env python3
"""
RUN LOOP INTEGRATED ARCHITECTURE SEARCH
=======================================

Main execution script for the deeply integrated Loop autonomous research system.
Combines transformer architecture search with Loop's algorithmic discovery capabilities.

Features:
- Deep Loop OpenEvolve integration
- Unified research methodology
- Cross-pollination between architectures and algorithms
- Autonomous hypothesis generation and testing
- Continuous learning and adaptation
"""

import asyncio
import argparse
import json
import logging
import time
import signal
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Import our integrated system
from loop_integrated_architecture_search import LoopIntegratedArchitectureSearch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('loop_integrated_search.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LoopIntegratedRunner:
    """Main runner for Loop integrated research system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.search_system = None
        self.is_running = False
        self.start_time = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        if self.search_system:
            self.search_system.stop_search()
        self.is_running = False
    
    async def run_integrated_research(self) -> Dict[str, Any]:
        """Run the integrated Loop research system"""
        
        logger.info("🚀 STARTING LOOP INTEGRATED AUTONOMOUS RESEARCH")
        logger.info("=" * 70)
        logger.info("🔬 Deep integration with Loop's core research capabilities")
        logger.info("🧬 Evolutionary architecture search + algorithmic discovery")
        logger.info("🤖 LLM-guided hypothesis generation and testing")
        logger.info("🔄 Cross-pollination between architectures and algorithms")
        
        self.start_time = time.time()
        self.is_running = True
        
        try:
            # Initialize integrated search system
            logger.info("🔧 Initializing Loop Integrated Research System...")
            self.search_system = LoopIntegratedArchitectureSearch(self.config)
            
            # Print configuration
            self._print_configuration()
            
            # Run integrated research
            logger.info("🧬 Beginning autonomous integrated research...")
            results = await self.search_system.run_integrated_search()
            
            # Generate final summary
            self._print_final_summary(results)
            
            return results
            
        except KeyboardInterrupt:
            logger.info("🛑 Research interrupted by user")
            return self._handle_interruption()
            
        except Exception as e:
            logger.error(f"❌ Research failed with error: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}
            
        finally:
            self.is_running = False
            if self.start_time:
                total_time = time.time() - self.start_time
                logger.info(f"⏱️ Total research time: {total_time/3600:.2f} hours")
    
    def _print_configuration(self):
        """Print research configuration"""
        
        logger.info("📋 Loop Integrated Research Configuration:")
        logger.info(f"   Max Iterations: {self.config.get('max_iterations', 100)}")
        logger.info(f"   Population Size: {self.config.get('population_size', 50)}")
        logger.info(f"   Target Compression: {self.config.get('target_compression', 50.0)}×")
        logger.info(f"   Target Accuracy: {self.config.get('target_accuracy', 0.95)}")
        logger.info(f"   Hardware Target: {self.config.get('hardware_target', '8GB consumer GPU')}")
        logger.info(f"   LLM Models: Gemini 2.0 Flash + Gemini 1.5 Pro")
        logger.info(f"   Research Focus: Architecture + Algorithm co-evolution")
        logger.info("")
    
    def _print_final_summary(self, results: Dict[str, Any]):
        """Print final research summary"""
        
        logger.info("🎉 LOOP INTEGRATED RESEARCH COMPLETED!")
        logger.info("=" * 55)
        
        if 'error' in results:
            logger.error(f"❌ Research failed: {results['error']}")
            return
        
        # Extract key metrics
        total_iterations = results.get('total_iterations', 0)
        architectures_discovered = results.get('architectures_discovered', 0)
        algorithms_discovered = results.get('algorithms_discovered', 0)
        hybrid_solutions = results.get('hybrid_solutions', [])
        insights = results.get('research_insights', [])
        
        logger.info(f"📊 Research Statistics:")
        logger.info(f"   Iterations Completed: {total_iterations}")
        logger.info(f"   Architectures Discovered: {architectures_discovered}")
        logger.info(f"   Algorithms Discovered: {algorithms_discovered}")
        logger.info(f"   Hybrid Solutions Created: {len(hybrid_solutions)}")
        logger.info(f"   Research Insights Generated: {len(insights)}")
        
        # Best solutions
        best_arch = results.get('best_architecture')
        best_alg = results.get('best_algorithm')
        
        if best_arch:
            logger.info(f"🏆 Best Architecture:")
            fitness = best_arch.get('fitness_metrics', {})
            logger.info(f"   Fitness Score: {fitness.get('fitness_score', 'N/A'):.4f}")
            
            genome = best_arch.get('genome', {})
            logger.info(f"   Architecture: {genome.get('num_layers', 'N/A')}L-{genome.get('hidden_size', 'N/A')}H-{genome.get('num_heads', 'N/A')}A")
            logger.info(f"   Compression: {1.0/genome.get('compression_ratio', 0.1):.1f}×")
            logger.info(f"   Memory Usage: {fitness.get('memory_usage', 'N/A'):.2f}GB")
        
        if best_alg:
            logger.info(f"🧮 Best Algorithm:")
            metrics = best_alg.get('metrics', {})
            logger.info(f"   Compression Ratio: {metrics.get('compression_ratio', 'N/A')}")
            logger.info(f"   Algorithm Type: {metrics.get('algorithm_type', 'Novel')}")
        
        # Hybrid solutions
        if hybrid_solutions:
            logger.info(f"🔗 Hybrid Solutions:")
            for i, hybrid in enumerate(hybrid_solutions[:3]):
                metrics = hybrid.get('metrics', {})
                logger.info(f"   Hybrid {i+1}: {metrics.get('compression_ratio', 'N/A')} compression")
        
        # Key insights
        if insights:
            logger.info(f"🔍 Key Research Insights:")
            for insight in insights[:5]:
                logger.info(f"   • {insight.get('description', 'Unknown insight')}")
        
        # Print file locations
        output_dir = self.config.get('output_dir', 'loop_architecture_search')
        logger.info(f"📄 Results Saved: {output_dir}/final_integrated_report.json")
        logger.info(f"💾 Checkpoints: {output_dir}/checkpoint_*/")
        
        logger.info("🎯 Loop integrated research completed successfully!")
    
    def _handle_interruption(self) -> Dict[str, Any]:
        """Handle research interruption"""
        
        logger.info("🔄 Handling research interruption...")
        
        if self.search_system:
            # Try to save current progress
            try:
                self.search_system._save_integrated_checkpoint()
                logger.info("💾 Progress saved successfully")
            except Exception as e:
                logger.warning(f"Failed to save progress: {e}")
        
        return {
            'status': 'interrupted',
            'message': 'Research was interrupted but progress has been saved',
            'checkpoint': f"{self.config.get('output_dir', 'loop_architecture_search')}/checkpoint_{self.search_system.iteration if self.search_system else 0}"
        }

def load_loop_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """Load Loop integrated configuration"""
    
    if config_path and Path(config_path).exists():
        logger.info(f"📁 Loading configuration from {config_path}")
        with open(config_path, 'r') as f:
            return json.load(f)
    
    # Default Loop integrated configuration
    logger.info("📋 Using default Loop integrated configuration")
    return {
        # Loop core parameters
        'max_iterations': 100,
        'population_size': 50,
        'target_compression': 50.0,
        'target_accuracy': 0.95,
        'output_dir': 'loop_integrated_search',
        
        # LLM configuration
        'gemini_api_key': 'AIzaSyAYmCiRrFwIFe9VOUd00A4r6GJVI8ZhAFE',
        'llm_models': ['gemini-2.0-flash-exp', 'gemini-1.5-pro'],
        'max_tokens': 2000,
        'temperature': 0.7,
        
        # Hardware constraints
        'hardware_target': '8GB consumer GPU',
        'target_memory_gb': 8.0,
        'target_speed_tokens_per_sec': 100.0,
        
        # Research parameters
        'hypothesis_generation_enabled': True,
        'cross_pollination_enabled': True,
        'hybrid_solution_creation': True,
        'autonomous_adaptation': True,
        
        # Architecture search parameters
        'architecture_population_size': 20,
        'algorithm_population_size': 30,
        'hybrid_generation_rate': 0.3,
        
        # Evaluation parameters
        'evaluation_timeout': 300,
        'parallel_evaluations': 5,
        'checkpoint_interval': 10
    }

def create_quick_test_config() -> Dict[str, Any]:
    """Create configuration for quick testing"""
    
    logger.info("⚡ Creating quick test configuration")
    config = load_loop_config()
    
    # Reduce for quick testing
    config.update({
        'max_iterations': 5,
        'population_size': 10,
        'architecture_population_size': 5,
        'algorithm_population_size': 5,
        'checkpoint_interval': 2,
        'output_dir': 'loop_quick_test'
    })
    
    return config

def create_research_config() -> Dict[str, Any]:
    """Create configuration for intensive research"""
    
    logger.info("🔬 Creating research configuration")
    config = load_loop_config()
    
    # Enhance for research
    config.update({
        'max_iterations': 200,
        'population_size': 100,
        'architecture_population_size': 40,
        'algorithm_population_size': 60,
        'target_compression': 100.0,
        'hybrid_generation_rate': 0.5,
        'output_dir': 'loop_research_intensive'
    })
    
    return config

def create_production_config() -> Dict[str, Any]:
    """Create configuration for production deployment"""
    
    logger.info("🚀 Creating production configuration")
    config = load_loop_config()
    
    # Optimize for production
    config.update({
        'max_iterations': 50,
        'population_size': 30,
        'target_compression': 25.0,
        'target_accuracy': 0.98,
        'evaluation_timeout': 600,
        'output_dir': 'loop_production_ready'
    })
    
    return config

async def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(
        description="Loop Integrated Autonomous Architecture Search"
    )
    parser.add_argument(
        '--config', 
        type=str, 
        help='Path to configuration JSON file'
    )
    parser.add_argument(
        '--quick-test', 
        action='store_true', 
        help='Run quick test with reduced parameters'
    )
    parser.add_argument(
        '--research', 
        action='store_true', 
        help='Run intensive research configuration'
    )
    parser.add_argument(
        '--production', 
        action='store_true', 
        help='Run production-ready configuration'
    )
    parser.add_argument(
        '--output-dir', 
        type=str, 
        default='loop_integrated_results',
        help='Output directory for results'
    )
    
    args = parser.parse_args()
    
    try:
        # Create output directory
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Load configuration
        if args.quick_test:
            config = create_quick_test_config()
        elif args.research:
            config = create_research_config()
        elif args.production:
            config = create_production_config()
        else:
            config = load_loop_config(args.config)
        
        # Add output directory to config
        config['output_dir'] = str(output_dir)
        
        # Create and run integrated research
        runner = LoopIntegratedRunner(config)
        results = await runner.run_integrated_research()
        
        # Save final results
        results_path = output_dir / 'final_integrated_results.json'
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"💾 Final results saved to {results_path}")
        
        # Exit with appropriate code
        if 'error' in results:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
