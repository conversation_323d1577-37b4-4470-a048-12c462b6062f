"""
Compare direct yfinance fetch vs DataCollectionAgent fetch.
"""
import asyncio
import logging
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import time

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('compare_fetches.log')
    ]
)
logger = logging.getLogger(__name__)

# Import the DataCollectionAgent
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))
from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

# Test parameters
TEST_SYMBOL = "AAPL"
TEST_INTERVAL = "1d"
TEST_PERIOD = "1mo"
TIMEOUT = 15  # seconds

async def direct_fetch():
    """Fetch data directly using yfinance."""
    logger.info("\n" + "="*80)
    logger.info("DIRECT YFINANCE FETCH")
    logger.info("="*80)
    
    def _fetch():
        try:
            ticker = yf.Ticker(TEST_SYMBOL)
            data = ticker.history(
                interval=TEST_INTERVAL,
                period=TEST_PERIOD,
                actions=False,
                auto_adjust=True,
                prepost=False
            )
            return data
        except Exception as e:
            logger.error(f"Error in direct fetch: {str(e)}")
            raise
    
    try:
        start_time = time.time()
        loop = asyncio.get_event_loop()
        df = await asyncio.wait_for(
            loop.run_in_executor(None, _fetch),
            timeout=TIMEOUT
        )
        elapsed = time.time() - start_time
        
        if df is None or df.empty:
            logger.warning("No data returned from direct fetch")
            return None
            
        logger.info(f"Direct fetch completed in {elapsed:.2f}s")
        logger.info(f"Fetched {len(df)} rows")
        logger.info(f"Columns: {', '.join(df.columns)}")
        logger.info(f"First row: {df.iloc[0]}")
        logger.info(f"Last row: {df.iloc[-1]}")
        
        return df
        
    except asyncio.TimeoutError:
        logger.error(f"Direct fetch timed out after {TIMEOUT} seconds")
        return None
    except Exception as e:
        logger.error(f"Error in direct fetch: {str(e)}", exc_info=True)
        return None

async def agent_fetch():
    """Fetch data using DataCollectionAgent."""
    logger.info("\n" + "="*80)
    logger.info("AGENT FETCH")
    logger.info("="*80)
    
    mock_llm = MistralWrapper()
    agent = DataCollectionAgent(llm_wrapper=mock_llm)
    
    try:
        # Start the agent
        logger.info("Starting agent...")
        start_time = time.time()
        await agent.start()
        
        # Fetch data
        logger.info(f"Fetching data for {TEST_SYMBOL}...")
        ohlcv = await agent.fetch_ohlcv(
            symbol=TEST_SYMBOL,
            interval=TEST_INTERVAL,
            period=TEST_PERIOD,
            timeout=TIMEOUT
        )
        elapsed = time.time() - start_time
        
        if ohlcv is None:
            logger.warning("No data returned from agent fetch")
            return None
            
        logger.info(f"Agent fetch completed in {elapsed:.2f}s")
        logger.info(f"Fetched {len(ohlcv.timestamp)} data points")
        logger.info(f"Date range: {ohlcv.timestamp[0]} to {ohlcv.timestamp[-1]}")
        logger.info(f"Latest close: ${ohlcv.close[-1]:.2f}")
        logger.info(f"Volume: {ohlcv.volume[-1]:,}")
        
        return ohlcv
        
    except asyncio.TimeoutError:
        logger.error(f"Agent fetch timed out after {TIMEOUT} seconds")
        return None
    except Exception as e:
        logger.error(f"Error in agent fetch: {str(e)}", exc_info=True)
        return None
    finally:
        # Stop the agent
        if agent.is_running:
            logger.info("Stopping agent...")
            await agent.stop()
            logger.info("Agent stopped.")

async def main():
    """Run the comparison."""
    logger.info("\n" + "="*80)
    logger.info("STARTING COMPARISON")
    logger.info("="*80)
    
    # Run direct fetch
    direct_result = await direct_fetch()
    
    # Add a small delay between tests
    await asyncio.sleep(2)
    
    # Run agent fetch
    agent_result = await agent_fetch()
    
    # Compare results
    logger.info("\n" + "="*80)
    logger.info("COMPARISON RESULTS")
    logger.info("="*80)
    
    if direct_result is not None and agent_result is not None:
        logger.info("Both direct and agent fetches completed successfully")
        logger.info(f"Direct rows: {len(direct_result)}")
        logger.info(f"Agent data points: {len(agent_result.timestamp)}")
    elif direct_result is not None:
        logger.info("Direct fetch succeeded but agent fetch failed")
    elif agent_result is not None:
        logger.info("Agent fetch succeeded but direct fetch failed")
    else:
        logger.error("Both direct and agent fetches failed")
    
    logger.info("\nComparison complete.")

if __name__ == "__main__":
    asyncio.run(main())
