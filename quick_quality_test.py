#!/usr/bin/env python3
"""
QUICK REAL OUTPUT QUALITY TEST
==============================

Fast test to show REAL output quality of 1-bit quantized Mistral 7B.
Show actual quality metrics - no simulation.
"""

import os
import torch
import gc
import time
from transformers import AutoTokenizer, AutoConfig
from safetensors import safe_open

def quantize_weight(weight_tensor):
    """1-bit quantize a weight tensor"""
    if weight_tensor.dtype != torch.float32:
        weight_tensor = weight_tensor.to(torch.float32)
    
    # 1-bit quantization
    scale = torch.mean(torch.abs(weight_tensor))
    signs = torch.sign(weight_tensor).to(torch.int8)
    
    # Reconstruct
    reconstructed = signs.to(torch.float32) * scale
    
    return reconstructed, scale, signs

def test_layer_quality(original, quantized):
    """Test quality between original and quantized weights"""
    
    # Basic quality metrics
    mse_error = torch.mean((original - quantized) ** 2).item()
    mae_error = torch.mean(torch.abs(original - quantized)).item()
    max_error = torch.max(torch.abs(original - quantized)).item()
    
    # Correlation
    orig_flat = original.flatten()
    quant_flat = quantized.flatten()
    correlation = torch.corrcoef(torch.stack([orig_flat, quant_flat]))[0, 1].item()
    
    # Relative error
    orig_norm = torch.norm(original).item()
    error_norm = torch.norm(original - quantized).item()
    relative_error = error_norm / orig_norm if orig_norm > 0 else 0
    
    return {
        'mse_error': mse_error,
        'mae_error': mae_error,
        'max_error': max_error,
        'correlation': correlation,
        'relative_error': relative_error
    }

def test_operation_quality(original_weight, quantized_weight, test_size=100):
    """Test quality of actual operations"""
    
    if len(original_weight.shape) != 2:
        return None
    
    # Create test input
    batch_size = 4
    input_dim = original_weight.shape[1]
    test_input = torch.randn(batch_size, test_size, input_dim)
    
    # Matrix multiplication with both weights
    original_output = torch.matmul(test_input, original_weight.T)
    quantized_output = torch.matmul(test_input, quantized_weight.T)
    
    # Calculate operation quality
    operation_mse = torch.mean((original_output - quantized_output) ** 2).item()
    operation_correlation = torch.corrcoef(
        torch.stack([original_output.flatten(), quantized_output.flatten()])
    )[0, 1].item()
    
    return {
        'operation_mse': operation_mse,
        'operation_correlation': operation_correlation,
        'input_shape': list(test_input.shape),
        'output_shape': list(original_output.shape)
    }

def main():
    """Run quick quality test"""
    
    print("🔬 QUICK REAL OUTPUT QUALITY TEST 🔬")
    print("=" * 50)
    print("⚠️  100% REAL RESULTS - NO SIMULATION")
    print("🎯 Testing actual output quality")
    print()
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    # Setup
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    config = AutoConfig.from_pretrained(model_path)
    
    print(f"✅ Model: {config.num_hidden_layers} layers, {config.hidden_size} hidden size")
    
    # Load model index
    index_path = os.path.join(model_path, "model.safetensors.index.json")
    with open(index_path, 'r') as f:
        import json
        index = json.load(f)
    
    # Test key layers
    test_layers = [
        "model.embed_tokens.weight",
        "model.layers.0.self_attn.q_proj.weight", 
        "model.layers.0.mlp.gate_proj.weight",
        "lm_head.weight"
    ]
    
    print(f"\n🔄 TESTING OUTPUT QUALITY ON {len(test_layers)} KEY LAYERS")
    print("=" * 60)
    
    all_results = []
    
    for i, layer_name in enumerate(test_layers):
        if layer_name not in index['weight_map']:
            continue
        
        print(f"\n📥 [{i+1}/{len(test_layers)}] Testing {layer_name}")
        
        file_name = index['weight_map'][layer_name]
        file_path = os.path.join(model_path, file_name)
        
        try:
            # Load weight
            with safe_open(file_path, framework="pt", device="cpu") as f:
                original_weight = f.get_tensor(layer_name)
                
                print(f"   📊 Shape: {original_weight.shape}")
                print(f"   📊 Parameters: {original_weight.numel():,}")
                
                # Quantize
                quantized_weight, scale, signs = quantize_weight(original_weight)
                
                # Test weight quality
                weight_quality = test_layer_quality(original_weight, quantized_weight)
                
                print(f"   📊 MSE Error: {weight_quality['mse_error']:.6f}")
                print(f"   📊 Correlation: {weight_quality['correlation']:.6f}")
                print(f"   📊 Relative Error: {weight_quality['relative_error']:.6f}")
                
                # Test operation quality
                operation_quality = test_operation_quality(original_weight, quantized_weight)
                
                if operation_quality:
                    print(f"   📊 Operation MSE: {operation_quality['operation_mse']:.6f}")
                    print(f"   📊 Operation Correlation: {operation_quality['operation_correlation']:.6f}")
                
                # Quality rating
                correlation = weight_quality['correlation']
                if correlation > 0.95:
                    quality_rating = "EXCELLENT"
                elif correlation > 0.9:
                    quality_rating = "VERY GOOD"
                elif correlation > 0.8:
                    quality_rating = "GOOD"
                elif correlation > 0.7:
                    quality_rating = "ACCEPTABLE"
                else:
                    quality_rating = "POOR"
                
                print(f"   🎯 Quality Rating: {quality_rating}")
                
                result = {
                    'layer_name': layer_name,
                    'shape': list(original_weight.shape),
                    'parameters': original_weight.numel(),
                    'weight_quality': weight_quality,
                    'operation_quality': operation_quality,
                    'quality_rating': quality_rating,
                    'scale': scale.item()
                }
                
                all_results.append(result)
                
                # Clean up
                del original_weight, quantized_weight
                gc.collect()
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Overall assessment
    if all_results:
        print(f"\n🎯 OVERALL OUTPUT QUALITY ASSESSMENT")
        print("=" * 50)
        
        # Calculate averages
        avg_correlation = sum(r['weight_quality']['correlation'] for r in all_results) / len(all_results)
        avg_relative_error = sum(r['weight_quality']['relative_error'] for r in all_results) / len(all_results)
        avg_mse = sum(r['weight_quality']['mse_error'] for r in all_results) / len(all_results)
        
        # Operation quality (if available)
        operation_results = [r for r in all_results if r['operation_quality']]
        if operation_results:
            avg_op_correlation = sum(r['operation_quality']['operation_correlation'] for r in operation_results) / len(operation_results)
            avg_op_mse = sum(r['operation_quality']['operation_mse'] for r in operation_results) / len(operation_results)
        else:
            avg_op_correlation = 0
            avg_op_mse = 0
        
        print(f"📊 Average Weight Correlation: {avg_correlation:.6f}")
        print(f"📊 Average Relative Error: {avg_relative_error:.6f}")
        print(f"📊 Average MSE Error: {avg_mse:.6f}")
        
        if operation_results:
            print(f"📊 Average Operation Correlation: {avg_op_correlation:.6f}")
            print(f"📊 Average Operation MSE: {avg_op_mse:.6f}")
        
        # Overall quality determination
        if avg_correlation > 0.95 and avg_relative_error < 0.05:
            overall_quality = "EXCELLENT"
            quality_description = "Virtually identical to original"
        elif avg_correlation > 0.9 and avg_relative_error < 0.1:
            overall_quality = "VERY GOOD"
            quality_description = "Minimal quality loss"
        elif avg_correlation > 0.8 and avg_relative_error < 0.2:
            overall_quality = "GOOD"
            quality_description = "Acceptable quality loss"
        elif avg_correlation > 0.7 and avg_relative_error < 0.3:
            overall_quality = "ACCEPTABLE"
            quality_description = "Noticeable but usable quality"
        else:
            overall_quality = "POOR"
            quality_description = "Significant quality degradation"
        
        print(f"\n🏆 OVERALL QUALITY: {overall_quality}")
        print(f"📝 Description: {quality_description}")
        
        # Production readiness
        production_ready = avg_correlation > 0.8 and avg_relative_error < 0.2
        print(f"🚀 Production Ready: {'✅ YES' if production_ready else '❌ NO'}")
        
        # Expected output quality
        print(f"\n📝 EXPECTED TEXT GENERATION QUALITY:")
        if avg_correlation > 0.95:
            print("   ✅ Text output will be virtually identical to original model")
            print("   ✅ No noticeable quality degradation expected")
        elif avg_correlation > 0.9:
            print("   ✅ Text output will be very close to original model")
            print("   ✅ Minimal quality differences expected")
        elif avg_correlation > 0.8:
            print("   ⚠️ Text output will be good but with some differences")
            print("   ⚠️ Slight quality reduction may be noticeable")
        else:
            print("   ❌ Text output may have noticeable quality issues")
            print("   ❌ Significant differences from original expected")
        
        # Save results
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        results_file = f"quick_quality_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump({
                'timestamp': time.time(),
                'overall_quality': overall_quality,
                'quality_description': quality_description,
                'production_ready': production_ready,
                'average_correlation': avg_correlation,
                'average_relative_error': avg_relative_error,
                'average_mse_error': avg_mse,
                'average_operation_correlation': avg_op_correlation,
                'layer_results': all_results
            }, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to {results_file}")
        
        print(f"\n🏁 REAL OUTPUT QUALITY TEST COMPLETE")
        print(f"⚠️  100% REAL MEASUREMENTS - NO SIMULATION")

if __name__ == "__main__":
    main()
