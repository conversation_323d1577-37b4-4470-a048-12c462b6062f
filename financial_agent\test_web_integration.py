#!/usr/bin/env python3
"""
Test script to verify the web interface and trading system integration.
"""
import os
import sys
import asyncio
import logging
import importlib
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.absolute())
sys.path.insert(0, project_root)
logger.info(f"Project root: {project_root}")

async def test_web_interface():
    """Test the web interface and trading system integration."""
    try:
        # Import the web app
        try:
            from financial_agent.web.app import app
            logger.info("✅ Successfully imported web app")
        except ImportError as e:
            logger.error(f"❌ Failed to import web app: {e}")
            return False
        
        # Import TradingSystem
        try:
            from financial_agent.web.integration import TradingSystem
            logger.info("✅ Successfully imported TradingSystem")
        except ImportError as e:
            logger.error(f"❌ Failed to import TradingSystem: {e}")
            return False
        
        # Create a test configuration
        config = {
            'initial_portfolio': 100000.0,
            'watchlist': ['SPY', 'QQQ', 'IWM', 'DIA'],
            'timeframe': '1d',
            'data_agent': {
                'api_key': 'demo',  # Using demo API key for testing
                'cache_ttl': 300
            },
            'analysis_agent': {
                'indicators': ['sma', 'rsi', 'macd']
            },
            'strategy_agent': {
                'strategy': 'etf_rotation',
                'rebalance_frequency': 'monthly',
                'max_positions': 3,
                'position_size': 0.2
            },
            'execution_agent': {
                'paper_trading': True,
                'slippage': 0.001,
                'commission': 0.005
            },
            'risk_agent': {
                'max_drawdown': 0.1,
                'max_position_risk': 0.02,
                'max_sector_exposure': 0.3,
                'max_leverage': 1.0,
                'daily_loss_limit': 0.05,
                'position_concentration': 0.2,
                'volatility_threshold': 0.3,
                'min_volume': 100000,
                'min_price': 5.0
            }
        }
        
        # Initialize the trading system
        try:
            trading_system = TradingSystem(config)
            logger.info("✅ Successfully created TradingSystem instance")
            
            # Test getting portfolio
            portfolio = await trading_system.get_portfolio()
            logger.info(f"✅ Portfolio: {portfolio}")
            
            # Test getting risk metrics
            risk_metrics = await trading_system.get_risk_metrics()
            logger.info(f"✅ Risk Metrics: {risk_metrics}")
            
            # Test getting trades
            trades = await trading_system.get_trades()
            logger.info(f"✅ Trades: {trades}")
            
            # Test getting performance
            performance = await trading_system.get_performance()
            logger.info(f"✅ Performance: {performance}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing TradingSystem: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("Starting web interface and trading system tests...")
    success = asyncio.run(test_web_interface())
    if success:
        logger.info("✅ All tests passed successfully!")
    else:
        logger.error("❌ Some tests failed")
    sys.exit(0 if success else 1)
