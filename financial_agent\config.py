"""
Configuration settings for the Financial Agent system.
Can be overridden by environment variables or a .env file.
"""
import os
from typing import Dict, Any, List, Optional
from pydantic import BaseSettings, Field, validator
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    APP_NAME: str = "Financial Agent System"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # Trading settings
    WATCHLIST: List[str] = Field(
        default=["SPY", "QQQ", "IWM", "DIA", "VTI", "VOO"],
        description="List of symbols to track and trade"
    )
    PAPER_TRADING: bool = True
    INITIAL_BALANCE: float = 100000.0
    CYCLE_INTERVAL_SECONDS: int = 3600  # 1 hour between cycles
    
    # LLM settings
    LLM_ENABLED: bool = False
    LLM_MODEL_NAME: str = "mistral-7b"
    LLM_TEMPERATURE: float = 0.7
    LLM_MAX_TOKENS: int = 512
    
    # Data agent settings
    DATA_CACHE_TTL: int = 300  # 5 minutes
    YFINANCE_TIMEOUT: int = 30  # seconds
    
    # Analysis agent settings
    ANALYSIS_INDICATORS: List[str] = Field(
        default=["sma", "rsi", "macd", "bollinger_bands"],
        description="List of technical indicators to calculate"
    )
    
    # Strategy agent settings
    STRATEGY_NAME: str = "etf_rotation"
    REBALANCE_FREQUENCY: str = "daily"  # daily, weekly, monthly
    MAX_POSITIONS: int = 5
    POSITION_SIZE: float = 0.2  # 20% per position
    
    # Risk management settings
    MAX_DRAWDOWN: float = 0.10  # 10%
    MAX_POSITION_RISK: float = 0.02  # 2%
    MAX_SECTOR_EXPOSURE: float = 0.30  # 30%
    MAX_LEVERAGE: float = 1.0  # No leverage
    DAILY_LOSS_LIMIT: float = 0.05  # 5%
    POSITION_CONCENTRATION: float = 0.20  # 20%
    VOLATILITY_THRESHOLD: float = 0.30  # 30%
    MIN_VOLUME: int = 100000  # shares
    MIN_PRICE: float = 5.0  # $
    
    # Execution settings
    SLIPPAGE: float = 0.001  # 0.1%
    COMMISSION: float = 0.005  # $0.005 per share
    
    # Web interface settings
    WEB_ENABLED: bool = True
    WEB_HOST: str = "0.0.0.0"
    WEB_PORT: int = 8000
    WEB_RELOAD: bool = False
    
    # API keys (loaded from environment variables)
    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    FINNHUB_API_KEY: Optional[str] = None
    
    class Config:
        env_prefix = "FINANCIAL_AGENT_"
        case_sensitive = False
        
    @validator('LOG_LEVEL')
    def validate_log_level(cls, v):
        v = v.upper()
        if v not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("Invalid log level")
        return v
    
    @validator('REBALANCE_FREQUENCY')
    def validate_rebalance_frequency(cls, v):
        v = v.lower()
        if v not in ["daily", "weekly", "monthly"]:
            raise ValueError("Rebalance frequency must be one of: daily, weekly, monthly")
        return v
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """Get configuration for a specific agent."""
        agent_config = {
            'data_agent': {
                'cache_ttl': self.DATA_CACHE_TTL,
                'timeout': self.YFINANCE_TIMEOUT,
                'api_keys': {
                    'alpha_vantage': self.ALPHA_VANTAGE_API_KEY,
                    'finnhub': self.FINNHUB_API_KEY
                }
            },
            'analysis_agent': {
                'indicators': self.ANALYSIS_INDICATORS
            },
            'strategy_agent': {
                'strategy': self.STRATEGY_NAME,
                'rebalance_frequency': self.REBALANCE_FREQUENCY,
                'max_positions': self.MAX_POSITIONS,
                'position_size': self.POSITION_SIZE
            },
            'risk_agent': {
                'max_drawdown': self.MAX_DRAWDOWN,
                'max_position_risk': self.MAX_POSITION_RISK,
                'max_sector_exposure': self.MAX_SECTOR_EXPOSURE,
                'max_leverage': self.MAX_LEVERAGE,
                'daily_loss_limit': self.DAILY_LOSS_LIMIT,
                'position_concentration': self.POSITION_CONCENTRATION,
                'volatility_threshold': self.VOLATILITY_THRESHOLD,
                'min_volume': self.MIN_VOLUME,
                'min_price': self.MIN_PRICE
            },
            'execution_agent': {
                'paper_trading': self.PAPER_TRADING,
                'initial_balance': self.INITIAL_BALANCE,
                'slippage': self.SLIPPAGE,
                'commission': self.COMMISSION
            }
        }
        
        return agent_config.get(agent_name, {})
    
    def get_web_config(self) -> Dict[str, Any]:
        """Get web interface configuration."""
        return {
            'enabled': self.WEB_ENABLED,
            'host': self.WEB_HOST,
            'port': self.WEB_PORT,
            'reload': self.WEB_RELOAD
        }
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration."""
        return {
            'enabled': self.LLM_ENABLED,
            'model_name': self.LLM_MODEL_NAME,
            'temperature': self.LLM_TEMPERATURE,
            'max_tokens': self.LLM_MAX_TOKENS
        }

# Create settings instance
settings = Settings()

# Export configuration for easy imports
config = {
    'watchlist': settings.WATCHLIST,
    'trading': {
        'paper_trading': settings.PAPER_TRADING,
        'initial_balance': settings.INITIAL_BALANCE,
        'cycle_interval_seconds': settings.CYCLE_INTERVAL_SECONDS
    },
    'llm': settings.get_llm_config(),
    'data_agent': settings.get_agent_config('data_agent'),
    'analysis_agent': settings.get_agent_config('analysis_agent'),
    'strategy_agent': settings.get_agent_config('strategy_agent'),
    'risk_agent': settings.get_agent_config('risk_agent'),
    'execution_agent': settings.get_agent_config('execution_agent'),
    'web_interface': settings.get_web_config()
}

# For backwards compatibility
__all__ = ['settings', 'config']
