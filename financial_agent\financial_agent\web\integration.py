"""
Integration module for connecting the web interface with the trading system.
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Import trading agents
from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.agents.analysis_agent import AnalysisAgent
from financial_agent.agents.strategy_agent import StrategyAgent
from financial_agent.agents.execution_agent import ExecutionAgent
from financial_agent.agents.risk_agent import RiskManagementAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingSystem:
    """
    Main trading system that coordinates between different agents and provides
    an interface for the web application.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the trading system with configuration.
        
        Args:
            config: Configuration dictionary containing settings for all agents
        """
        self.config = config
        self.initial_portfolio = config.get('initial_portfolio', 100000.0)
        self.watchlist = config.get('watchlist', [])
        self.timeframe = config.get('timeframe', '1d')
        
        # Initialize agents
        self.data_agent = DataCollectionAgent(config.get('data_agent', {}))
        self.analysis_agent = AnalysisAgent(config.get('analysis_agent', {}))
        self.strategy_agent = StrategyAgent(config.get('strategy_agent', {}))
        self.execution_agent = ExecutionAgent(config.get('execution_agent', {}))
        self.risk_agent = RiskManagementAgent(config.get('risk_agent', {}))
        
        # State
        self.portfolio = {
            'cash': self.initial_portfolio,
            'positions': {},
            'value_history': [],
            'last_updated': None
        }
        self.trades = []
        self.signals = []
        self.performance_metrics = {}
        
        # WebSocket clients
        self.clients = set()
        
        logger.info("TradingSystem initialized")
    
    async def initialize(self):
        """Initialize the trading system and all agents."""
        logger.info("Initializing trading system...")
        
        # Initialize agents
        await self.data_agent.initialize()
        await self.analysis_agent.initialize()
        await self.strategy_agent.initialize()
        await self.execution_agent.initialize()
        await self.risk_agent.initialize()
        
        # Initial data fetch
        await self.update_market_data()
        
        # Initial portfolio value
        self.portfolio['last_updated'] = datetime.utcnow().isoformat()
        self.portfolio['value_history'].append({
            'timestamp': self.portfolio['last_updated'],
            'value': self.initial_portfolio
        })
        
        logger.info("Trading system initialization complete")
    
    async def shutdown(self):
        """Shut down the trading system and all agents."""
        logger.info("Shutting down trading system...")
        
        # Shut down agents
        await self.data_agent.shutdown()
        await self.analysis_agent.shutdown()
        await self.strategy_agent.shutdown()
        await self.execution_agent.shutdown()
        await self.risk_agent.shutdown()
        
        logger.info("Trading system shutdown complete")
    
    async def update_market_data(self):
        """Fetch and update market data for all symbols in the watchlist."""
        logger.info(f"Updating market data for {len(self.watchlist)} symbols")
        
        for symbol in self.watchlist:
            try:
                # Fetch OHLCV data
                ohlcv = await self.data_agent.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=self.timeframe,
                    limit=100  # Get last 100 candles
                )
                
                if ohlcv is not None and not ohlcv.empty:
                    # Update analysis with new data
                    analysis = await self.analysis_agent.analyze(ohlcv)
                    
                    # Store the latest data
                    latest = ohlcv.iloc[-1]
                    
                    # Update portfolio position if we have it
                    if symbol in self.portfolio['positions']:
                        position = self.portfolio['positions'][symbol]
                        position['current_price'] = latest['close']
                        position['value'] = position['quantity'] * latest['close']
                        
                        # Update P&L
                        position['unrealized_pnl'] = position['value'] - (position['quantity'] * position['entry_price'])
                        position['unrealized_pnl_pct'] = position['unrealized_pnl'] / (position['quantity'] * position['entry_price'])
                        
                        logger.debug(f"Updated position for {symbol}: {position}")
                    
                    logger.debug(f"Updated market data for {symbol}")
                else:
                    logger.warning(f"No data returned for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error updating market data for {symbol}: {e}")
        
        # Update portfolio value
        await self._update_portfolio_value()
    
    async def _update_portfolio_value(self):
        """Update the total portfolio value based on current positions and cash."""
        total_value = self.portfolio['cash']
        
        # Sum up the value of all positions
        for symbol, position in self.portfolio['positions'].items():
            total_value += position.get('value', 0)
        
        # Update portfolio value history
        self.portfolio['value_history'].append({
            'timestamp': datetime.utcnow().isoformat(),
            'value': total_value
        })
        
        # Keep only the last 1000 data points
        if len(self.portfolio['value_history']) > 1000:
            self.portfolio['value_history'] = self.portfolio['value_history'][-1000:]
        
        self.portfolio['last_updated'] = datetime.utcnow().isoformat()
        
        logger.debug(f"Updated portfolio value: ${total_value:,.2f}")
    
    async def generate_signals(self):
        """Generate trading signals based on current market conditions."""
        logger.info("Generating trading signals...")
        
        signals = []
        
        for symbol in self.watchlist:
            try:
                # Get latest analysis
                analysis = await self.analysis_agent.get_latest_analysis(symbol)
                if not analysis:
                    continue
                
                # Generate signals based on analysis
                signal = await self.strategy_agent.generate_signal(symbol, analysis)
                if signal:
                    signals.append(signal)
                    logger.info(f"Generated signal for {symbol}: {signal}")
            
            except Exception as e:
                logger.error(f"Error generating signal for {symbol}: {e}")
        
        # Store signals
        self.signals.extend(signals)
        
        return signals
    
    async def execute_trades(self):
        """Execute trades based on generated signals and risk parameters."""
        if not self.signals:
            logger.info("No signals to execute")
            return []
        
        executed_trades = []
        
        for signal in self.signals[:]:  # Iterate over a copy of the list
            try:
                # Check risk parameters
                risk_ok = await self.risk_agent.check_trade_risk(signal, self.portfolio)
                if not risk_ok:
                    logger.warning(f"Trade rejected by risk manager: {signal}")
                    continue
                
                # Execute the trade
                trade = await self.execution_agent.execute_trade(signal, self.portfolio)
                if trade:
                    executed_trades.append(trade)
                    self.trades.append(trade)
                    
                    # Update portfolio
                    await self._update_portfolio_from_trade(trade)
                    
                    # Remove the executed signal
                    if signal in self.signals:
                        self.signals.remove(signal)
                    
                    logger.info(f"Executed trade: {trade}")
            
            except Exception as e:
                logger.error(f"Error executing trade for signal {signal}: {e}")
        
        return executed_trades
    
    async def _update_portfolio_from_trade(self, trade: Dict[str, Any]):
        """Update portfolio state based on an executed trade."""
        symbol = trade['symbol']
        quantity = trade['quantity']
        price = trade['price']
        
        if trade['side'] == 'buy':
            # Update cash
            self.portfolio['cash'] -= quantity * price
            
            # Add to or update position
            if symbol in self.portfolio['positions']:
                position = self.portfolio['positions'][symbol]
                total_quantity = position['quantity'] + quantity
                position['entry_price'] = (
                    (position['quantity'] * position['entry_price']) + 
                    (quantity * price)
                ) / total_quantity
                position['quantity'] = total_quantity
                position['current_price'] = price
                position['value'] = total_quantity * price
            else:
                self.portfolio['positions'][symbol] = {
                    'symbol': symbol,
                    'quantity': quantity,
                    'entry_price': price,
                    'current_price': price,
                    'value': quantity * price,
                    'unrealized_pnl': 0.0,
                    'unrealized_pnl_pct': 0.0,
                    'sector': 'Unknown',  # Would come from a data source
                    'beta': 1.0  # Would come from risk model
                }
        
        elif trade['side'] == 'sell':
            # Update cash
            self.portfolio['cash'] += quantity * price
            
            # Update or remove position
            if symbol in self.portfolio['positions']:
                position = self.portfolio['positions'][symbol]
                position['quantity'] -= quantity
                position['value'] = position['quantity'] * price
                
                # Remove position if quantity is zero
                if position['quantity'] <= 0:
                    del self.portfolio['positions'][symbol]
        
        # Update portfolio value
        await self._update_portfolio_value()
    
    # Web API methods
    
    async def get_portfolio(self) -> Dict[str, Any]:
        """Get the current portfolio state."""
        # Calculate total portfolio value
        total_value = self.portfolio['cash']
        positions_value = 0.0
        
        # Calculate position values and P&L
        for position in self.portfolio['positions'].values():
            positions_value += position['value']
            
            # Calculate P&L
            position['pnl'] = position.get('unrealized_pnl', 0.0)
            position['pnl_pct'] = position.get('unrealized_pnl_pct', 0.0)
        
        total_value += positions_value
        
        # Prepare response
        return {
            'total_value': total_value,
            'cash': self.portfolio['cash'],
            'positions_value': positions_value,
            'positions': list(self.portfolio['positions'].values()),
            'last_updated': self.portfolio['last_updated']
        }
    
    async def get_trades(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent trades."""
        return self.trades[-limit:]
    
    async def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics."""
        # This would be more sophisticated in a real system
        portfolio = await self.get_portfolio()
        
        # Calculate basic metrics
        total_value = portfolio['total_value']
        cash_weight = portfolio['cash'] / total_value if total_value > 0 else 1.0
        
        # Calculate position concentration
        position_weights = [
            pos['value'] / total_value 
            for pos in portfolio['positions'].values()
        ]
        
        # Basic risk metrics (simplified for this example)
        return {
            'total_value': total_value,
            'cash_weight': cash_weight,
            'num_positions': len(portfolio['positions']),
            'max_position_weight': max(position_weights) if position_weights else 0.0,
            'herfindahl_index': sum(w**2 for w in position_weights),  # Concentration measure
            'beta': 1.0,  # Would come from risk model
            'volatility': 0.15,  # Would be calculated from historical returns
            'value_at_risk': total_value * 0.05,  # 5% VaR (simplified)
            'last_updated': datetime.utcnow().isoformat()
        }
    
    async def get_performance(self) -> Dict[str, Any]:
        """Get performance metrics."""
        if not self.portfolio['value_history']:
            return {
                'daily_return': 0.0,
                'weekly_return': 0.0,
                'monthly_return': 0.0,
                'ytd_return': 0.0,
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'max_drawdown': 0.0,
                'last_updated': datetime.utcnow().isoformat()
            }
        
        # Convert value history to pandas Series for easier calculations
        df = pd.DataFrame(self.portfolio['value_history'])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        
        # Calculate returns
        returns = df['value'].pct_change().dropna()
        
        # Basic metrics
        daily_return = returns.iloc[-1] if len(returns) > 0 else 0.0
        weekly_return = (1 + returns).prod() ** (5/len(returns)) - 1 if len(returns) > 0 else 0.0  # Annualized
        monthly_return = (1 + returns).prod() ** (21/len(returns)) - 1 if len(returns) > 0 else 0.0  # Annualized
        
        # Calculate drawdowns
        cum_returns = (1 + returns).cumprod()
        rolling_max = cum_returns.cummax()
        drawdowns = (cum_returns - rolling_max) / rolling_max
        max_drawdown = drawdowns.min() if len(drawdowns) > 0 else 0.0
        
        # Risk-adjusted returns (simplified)
        risk_free_rate = 0.02  # 2% risk-free rate
        excess_returns = returns - risk_free_rate/252  # Daily excess returns
        
        sharpe_ratio = np.sqrt(252) * excess_returns.mean() / returns.std() if len(returns) > 1 else 0.0
        
        # Sortino ratio (only downside deviation)
        downside_returns = returns[returns < 0]
        downside_std = downside_returns.std() if len(downside_returns) > 1 else 0.0
        sortino_ratio = np.sqrt(252) * (returns.mean() - risk_free_rate/252) / downside_std if downside_std > 0 else 0.0
        
        # YTD return (simplified)
        ytd_return = (df['value'].iloc[-1] / df['value'].iloc[0]) - 1 if len(df) > 1 else 0.0
        
        return {
            'daily_return': daily_return,
            'weekly_return': weekly_return,
            'monthly_return': monthly_return,
            'ytd_return': ytd_return,
            'total_return': (df['value'].iloc[-1] / self.initial_portfolio) - 1 if len(df) > 0 else 0.0,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': max_drawdown,
            'last_updated': datetime.utcnow().isoformat()
        }
