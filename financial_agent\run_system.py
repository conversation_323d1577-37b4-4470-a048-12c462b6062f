"""
Main entry point for the Financial Agent system.
Runs all components together in a continuous loop.
"""
import asyncio
import logging
import signal
import sys
from datetime import datetime, time, timedelta
from typing import Dict, Any

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.agents.analysis_agent import AnalysisAgent
from financial_agent.agents.strategy_agent import StrategyAgent, StrategyType
from financial_agent.agents.risk_agent import RiskManagementAgent
from financial_agent.agents.execution_agent import ExecutionAgent
from financial_agent.llm.mistral_wrapper import MistralWrapper
from financial_agent.web.app import app as web_app
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('financial_agent.log')
    ]
)
logger = logging.getLogger(__name__)

class FinancialAgentSystem:
    """Main system class that coordinates all agents and components."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the financial agent system."""
        self.config = config
        self.running = False
        self.llm = None
        self.agents = {}
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)
    
    async def initialize(self):
        """Initialize all system components."""
        logger.info("Initializing Financial Agent System...")
        
        # Initialize LLM wrapper if enabled
        if self.config.get('llm', {}).get('enabled', False):
            self.llm = MistralWrapper(
                model_name=self.config['llm'].get('model_name', 'mistral-7b'),
                device_map='auto',
                load_in_4bit=True
            )
        
        # Initialize agents
        self.agents['data'] = DataCollectionAgent(
            llm_wrapper=self.llm,
            config=self.config.get('data_agent', {})
        )
        
        self.agents['analysis'] = AnalysisAgent(
            llm_wrapper=self.llm,
            config=self.config.get('analysis_agent', {})
        )
        
        self.agents['strategy'] = StrategyAgent(
            llm_wrapper=self.llm,
            config=self.config.get('strategy_agent', {})
        )
        
        self.agents['risk'] = RiskManagementAgent(
            config=self.config.get('risk_agent', {})
        )
        
        self.agents['execution'] = ExecutionAgent(
            config=self.config.get('execution_agent', {})
        )
        
        # Start all agents
        for name, agent in self.agents.items():
            logger.info(f"Starting {name} agent...")
            await agent.start()
        
        logger.info("All agents initialized successfully")
    
    async def run_cycle(self):
        """Run one complete trading cycle."""
        logger.info("Starting trading cycle...")
        
        try:
            # 1. Fetch market data
            data_response = await self.agents['data'].process({
                'request': {
                    'symbols': self.config['watchlist'],
                    'interval': '1d',
                    'period': '1mo'
                }
            })
            
            if not data_response.success:
                logger.error(f"Data collection failed: {data_response.error}")
                return
            
            # 2. Analyze the data
            analysis_response = await self.agents['analysis'].process({
                'ohlcv': data_response.data
            })
            
            if not analysis_response.success:
                logger.error(f"Analysis failed: {analysis_response.error}")
                return
            
            # 3. Generate trading signals
            signals_response = await self.agents['strategy'].process({
                'analysis': analysis_response.data,
                'portfolio': self.agents['execution'].get_portfolio_summary()
            })
            
            if not signals_response.success:
                logger.error(f"Signal generation failed: {signals_response.error}")
                return
            
            # 4. Apply risk management
            risk_response = await self.agents['risk'].process({
                'signals': signals_response.data,
                'portfolio': self.agents['execution'].get_portfolio_summary()
            })
            
            if not risk_response.success:
                logger.error(f"Risk assessment failed: {risk_response.error}")
                return
            
            # 5. Execute trades if approved
            if risk_response.data.get('is_acceptable', False):
                execution_response = await self.agents['execution'].process({
                    'signals': risk_response.data.get('approved_signals', [])
                })
                
                if not execution_response.success:
                    logger.error(f"Trade execution failed: {execution_response.error}")
                    return
                
                logger.info("Trading cycle completed successfully")
                logger.info(f"Portfolio: {self.agents['execution'].get_portfolio_summary()}")
            else:
                logger.info("No trades executed based on risk assessment")
                logger.info(f"Reason: {risk_response.data.get('reasons', ['No reason provided'])}")
                
        except Exception as e:
            logger.error(f"Error in trading cycle: {str(e)}", exc_info=True)
    
    async def run(self):
        """Run the main system loop."""
        try:
            await self.initialize()
            self.running = True
            
            # Start web interface in background if enabled
            if self.config.get('web_interface', {}).get('enabled', False):
                web_config = self.config['web_interface']
                web_server = uvicorn.Server(
                    config=uvicorn.Config(
                        app=web_app,
                        host=web_config.get('host', '0.0.0.0'),
                        port=web_config.get('port', 8000),
                        log_level='info'
                    )
                )
                asyncio.create_task(web_server.serve())
            
            # Main loop
            while self.running:
                try:
                    await self.run_cycle()
                    
                    # Wait for next cycle based on configuration
                    sleep_time = self.config.get('trading', {}).get('cycle_interval_seconds', 3600)
                    logger.info(f"Sleeping for {sleep_time} seconds...")
                    await asyncio.sleep(sleep_time)
                    
                except asyncio.CancelledError:
                    logger.info("Received shutdown signal")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {str(e)}", exc_info=True)
                    await asyncio.sleep(60)  # Wait before retrying
                    
        except Exception as e:
            logger.critical(f"Fatal error: {str(e)}", exc_info=True)
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shut down the system gracefully."""
        if not self.running:
            return
            
        logger.info("Shutting down Financial Agent System...")
        self.running = False
        
        # Stop all agents
        for name, agent in reversed(self.agents.items()):
            try:
                logger.info(f"Stopping {name} agent...")
                await agent.stop()
            except Exception as e:
                logger.error(f"Error stopping {name} agent: {str(e)}")
        
        logger.info("Shutdown complete")
    
    def handle_shutdown(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False

def load_config() -> Dict[str, Any]:
    """Load configuration from file or environment."""
    # Default configuration
    config = {
        'watchlist': ['SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'VOO'],
        'trading': {
            'cycle_interval_seconds': 3600,  # 1 hour between cycles
            'paper_trading': True
        },
        'llm': {
            'enabled': False,  # Set to True to enable LLM integration
            'model_name': 'mistral-7b',
            'temperature': 0.7,
            'max_tokens': 512
        },
        'data_agent': {
            'cache_ttl': 300,
            'api_keys': {}
        },
        'analysis_agent': {
            'indicators': ['sma', 'rsi', 'macd', 'bollinger_bands']
        },
        'strategy_agent': {
            'strategy': 'etf_rotation',
            'rebalance_frequency': 'daily',
            'max_positions': 5,
            'position_size': 0.2
        },
        'risk_agent': {
            'max_drawdown': 0.10,
            'max_position_risk': 0.02,
            'max_sector_exposure': 0.30,
            'max_leverage': 1.0
        },
        'execution_agent': {
            'paper_trading': True,
            'initial_balance': 100000.0,
            'slippage': 0.001,
            'commission': 0.005
        },
        'web_interface': {
            'enabled': True,
            'host': '0.0.0.0',
            'port': 8000
        }
    }
    
    # TODO: Load from config file or environment variables
    
    return config

async def main():
    """Main entry point."""
    try:
        # Load configuration
        config = load_config()
        
        # Create and run the system
        system = FinancialAgentSystem(config)
        await system.run()
        
    except Exception as e:
        logger.critical(f"Fatal error: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
