"""
Minimal test script to isolate yfinance data fetching issues.
"""
import asyncio
import logging
import yfinance as yf
from datetime import datetime, timedelta
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('minimal_data_fetch.log')
    ]
)
logger = logging.getLogger(__name__)

async def fetch_yfinance_data(symbol, interval, period, timeout=10):
    """Minimal function to fetch data using yfinance."""
    logger.info(f"Fetching {symbol} data with interval={interval}, period={period}")
    
    def _fetch():
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(
                interval=interval,
                period=period,
                actions=False,
                auto_adjust=True,
                prepost=False
            )
            return data
        except Exception as e:
            logger.error(f"Error in _fetch: {str(e)}")
            raise
    
    try:
        loop = asyncio.get_event_loop()
        df = await asyncio.wait_for(
            loop.run_in_executor(None, _fetch),
            timeout=timeout
        )
        
        if df is None or df.empty:
            logger.warning(f"No data returned for {symbol}")
            return None
            
        logger.info(f"Successfully fetched {len(df)} rows for {symbol}")
        return {
            'symbol': symbol,
            'interval': interval,
            'data': df
        }
        
    except asyncio.TimeoutError:
        logger.error(f"Timeout while fetching data for {symbol}")
        return None
    except Exception as e:
        logger.error(f"Error fetching data for {symbol}: {str(e)}", exc_info=True)
        return None

async def main():
    """Run the minimal test."""
    logger.info("Starting minimal data fetch test...")
    
    # Test parameters
    test_cases = [
        {"symbol": "AAPL", "interval": "1d", "period": "1mo"},
        {"symbol": "MSFT", "interval": "1d", "period": "1mo"},
        {"symbol": "GOOGL", "interval": "1d", "period": "1mo"},
    ]
    
    for test_case in test_cases:
        result = await fetch_yfinance_data(
            symbol=test_case["symbol"],
            interval=test_case["interval"],
            period=test_case["period"],
            timeout=15  # 15 second timeout
        )
        
        if result:
            df = result['data']
            logger.info(f"{test_case['symbol']} data sample:")
            logger.info(f"  First date: {df.index[0]}")
            logger.info(f"  Last date: {df.index[-1]}")
            logger.info(f"  Latest close: ${df['Close'].iloc[-1]:.2f}")
            logger.info(f"  Volume: {df['Volume'].iloc[-1]:,}")
        else:
            logger.warning(f"Failed to fetch data for {test_case['symbol']}")
        
        logger.info("-" * 80)
    
    logger.info("Test completed.")

if __name__ == "__main__":
    asyncio.run(main())
