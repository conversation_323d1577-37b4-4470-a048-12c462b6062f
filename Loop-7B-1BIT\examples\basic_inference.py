#!/usr/bin/env python3
"""
Basic Inference Example - Loop 7B 1-BIT
========================================

Simple example showing how to use Loop 7B 1-BIT for text generation
with ultra-low RAM usage (740MB vs ~29GB baseline).

This example demonstrates:
- Model compression with 1-bit quantization
- Memory-efficient text generation
- Real-time RAM monitoring
"""

import sys
import os
import psutil
import time

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loop_1bit_compressor import Loop1BitCompressor

def monitor_memory():
    """Monitor current RAM usage"""
    ram_mb = psutil.Process().memory_info().rss / (1024**2)
    return ram_mb

def basic_inference_example():
    """Basic inference example with memory monitoring"""
    
    print("🔬 Loop 7B 1-BIT Basic Inference Example")
    print("=" * 50)
    print("🎯 Target: Ultra-low RAM inference (~740MB)")
    print()
    
    # Model path (adjust as needed)
    model_path = "../downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        print("Please update the model_path variable to point to your Mistral 7B model")
        return
    
    # Monitor initial memory
    initial_ram = monitor_memory()
    print(f"💾 Initial RAM usage: {initial_ram:.1f}MB")
    
    # Initialize compressor
    print("\n🔧 Initializing Loop 1-BIT Compressor...")
    compressor = Loop1BitCompressor(model_path)
    init_ram = monitor_memory()
    print(f"💾 After initialization: {init_ram:.1f}MB (+{init_ram - initial_ram:.1f}MB)")
    
    # Load tokenizer
    print("\n📥 Loading tokenizer...")
    compressor.load_tokenizer()
    tokenizer_ram = monitor_memory()
    print(f"💾 After tokenizer: {tokenizer_ram:.1f}MB (+{tokenizer_ram - init_ram:.1f}MB)")
    
    # Compress model
    print("\n🔄 Compressing model with 1-bit quantization...")
    start_time = time.time()
    compression_result = compressor.compress_model()
    compression_time = time.time() - start_time
    compression_ram = monitor_memory()
    
    print(f"✅ Compression complete in {compression_time:.1f}s")
    print(f"💾 After compression: {compression_ram:.1f}MB (+{compression_ram - tokenizer_ram:.1f}MB)")
    print(f"📊 Compression ratio: {compression_result.get('overall_compression_ratio', 0):.1f}×")
    
    # Test text generation
    print("\n🧪 Testing text generation...")
    
    test_prompts = [
        "What is artificial intelligence?",
        "Explain quantum computing in simple terms.",
        "Write a short story about a robot."
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📝 Test {i}: {prompt}")
        
        start_time = time.time()
        start_ram = monitor_memory()
        
        response = compressor.generate(prompt, max_tokens=30)
        
        end_time = time.time()
        end_ram = monitor_memory()
        
        print(f"🤖 Response: {response}")
        print(f"⏱️ Generation time: {end_time - start_time:.2f}s")
        print(f"💾 RAM during generation: {end_ram:.1f}MB")
    
    # Final statistics
    final_ram = monitor_memory()
    total_ram_usage = final_ram - initial_ram
    
    print(f"\n📊 FINAL STATISTICS:")
    print(f"   Total RAM usage: {total_ram_usage:.1f}MB")
    print(f"   Peak RAM: {final_ram:.1f}MB")
    print(f"   Target (300MB): {'✅ ACHIEVED' if total_ram_usage <= 300 else '❌ NOT ACHIEVED'}")
    print(f"   vs Baseline (~29GB): {29000 / total_ram_usage:.1f}× reduction")
    
    # Get compressor statistics
    stats = compressor.get_stats()
    print(f"\n🔧 COMPRESSOR STATISTICS:")
    print(f"   Compression ratio: {stats['compression_ratio']:.1f}×")
    print(f"   Weights compressed: {stats['weights_compressed']}")
    print(f"   Inference time: {stats['inference_time_s']:.2f}s")

def interactive_mode():
    """Interactive mode for testing custom prompts"""
    
    print("\n🎮 Interactive Mode")
    print("=" * 30)
    print("Enter prompts to test the compressed model (type 'quit' to exit)")
    
    model_path = "../downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    # Initialize compressor
    compressor = Loop1BitCompressor(model_path)
    compressor.load_tokenizer()
    compressor.compress_model()
    
    while True:
        try:
            prompt = input("\n📝 Enter prompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not prompt:
                continue
            
            print("🤖 Generating response...")
            start_time = time.time()
            start_ram = monitor_memory()
            
            response = compressor.generate(prompt, max_tokens=50)
            
            end_time = time.time()
            end_ram = monitor_memory()
            
            print(f"\n🤖 Response: {response}")
            print(f"⏱️ Time: {end_time - start_time:.2f}s")
            print(f"💾 RAM: {end_ram:.1f}MB")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main function with menu"""
    
    print("🔬 Loop 7B 1-BIT Examples")
    print("=" * 30)
    print("1. Basic inference example")
    print("2. Interactive mode")
    print("3. Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-3): ").strip()
            
            if choice == "1":
                basic_inference_example()
            elif choice == "2":
                interactive_mode()
            elif choice == "3":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-3.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
