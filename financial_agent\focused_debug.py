"""
Focused debug script for DataCollectionAgent with controlled logging.
"""
import asyncio
import logging
import sys
import os
import time
from datetime import datetime

# Set up minimal logging to file only
log_filename = f"focused_debug_{int(time.time())}.log"
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename, mode='w', encoding='utf-8'),
    ]
)

# Disable verbose logging from other modules
logging.getLogger('yfinance').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.WARNING)

# Add the parent directory to the path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

# Configure console output
print("=" * 80)
print(f"FOCUSED DEBUG SESSION - {datetime.now()}")
print("=" * 80)
print(f"Log file: {os.path.abspath(log_filename)}")
print("-" * 80)

async def test_fetch(symbol, interval, period, timeout=30, max_retries=2):
    """Test data fetching with detailed logging."""
    logger = logging.getLogger()
    
    # Log test start
    start_time = time.time()
    logger.info("\n" + "="*60)
    logger.info(f"TEST: {symbol} - {interval} - {period}")
    logger.info("="*60)
    
    # Initialize agent
    agent = DataCollectionAgent(llm_wrapper=MistralWrapper())
    
    try:
        # Start agent
        logger.info("Starting agent...")
        await agent.start()
        
        # Fetch data
        logger.info(f"Fetching data for {symbol}...")
        ohlcv = await agent.fetch_ohlcv(
            symbol=symbol,
            interval=interval,
            period=period,
            timeout=timeout,
            max_retries=max_retries
        )
        
        # Process results
        elapsed = time.time() - start_time
        if ohlcv:
            logger.info("\nFETCH SUCCESSFUL")
            logger.info("-" * 60)
            logger.info(f"Symbol: {symbol}")
            logger.info(f"Data points: {len(ohlcv.timestamp)}")
            logger.info(f"Date range: {ohlcv.timestamp[0]} to {ohlcv.timestamp[-1]}")
            logger.info(f"Latest close: ${ohlcv.close[-1]:.2f}")
            logger.info(f"Volume: {ohlcv.volume[-1]:,}")
            logger.info(f"Time taken: {elapsed:.2f} seconds")
            return True
        else:
            logger.error("\nFETCH FAILED: No data returned")
            return False
            
    except Exception as e:
        logger.error(f"\nFETCH FAILED: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        return False
    finally:
        # Clean up
        if agent.is_running:
            await agent.stop()

async def run_tests():
    """Run test cases."""
    test_cases = [
        ("AAPL", "1d", "1mo"),
        ("MSFT", "1h", "5d"),
        ("GOOGL", "1d", "1y"),
    ]
    
    results = []
    for symbol, interval, period in test_cases:
        success = await test_fetch(symbol, interval, period)
        results.append((symbol, interval, period, success))
        print(f"Test {symbol} ({interval}, {period}): {'PASSED' if success else 'FAILED'}")
        print("-" * 80)
    
    # Print summary
    print("\nTEST SUMMARY:" + "="*70)
    for symbol, interval, period, success in results:
        print(f"{symbol:<6} {interval:<4} {period:<6} - {'PASSED' if success else 'FAILED'}")
    print("="*80)

if __name__ == "__main__":
    asyncio.run(run_tests())
