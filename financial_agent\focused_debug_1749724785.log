2025-06-12 16:09:45,807 - INFO - 
============================================================
2025-06-12 16:09:45,807 - INFO - TEST: AAPL - 1d - 1mo
2025-06-12 16:09:45,807 - INFO - ============================================================
2025-06-12 16:09:45,807 - INFO - Initialized data agent
2025-06-12 16:09:45,807 - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 16:09:45,807 - INFO - Starting agent...
2025-06-12 16:09:45,807 - INFO - Started data agent
2025-06-12 16:09:45,807 - INFO - Fetching data for AAPL...
2025-06-12 16:09:45,807 - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=AAPL, interval=1d, period=1mo, start=None, end=None, max_retries=2, retry_delay=1.0, timeout=30)
2025-06-12 16:09:45,809 - DEBUG - [DataCollectionAgent] Using period: 1mo
2025-06-12 16:09:45,809 - INFO - [DataCollectionAgent] Fetching data for AAPL (attempt 1/2)...
2025-06-12 16:09:45,809 - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:09:45
2025-06-12 16:09:45,810 - DEBUG - [DataCollectionAgent] Created yf.Ticker for AAPL
2025-06-12 16:09:45,810 - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=30s
2025-06-12 16:09:45,811 - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1d', 'period': '1mo', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:09:45,811 - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:09:45
2025-06-12 16:09:45,813 - DEBUG - ('CREATE TABLE IF NOT EXISTS "_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-06-12 16:09:45,813 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-06-12 16:09:45,847 - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-06-12 16:09:45,847 - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.88s
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Fetched 22 rows for AAPL
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 22
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Caching result with key: AAPL_1d_1mo_None_None
2025-06-12 16:09:46,693 - INFO - [DataCollectionAgent] Successfully fetched 22 data points for AAPL in 0.88s
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:09:46,693 - INFO - 
FETCH SUCCESSFUL
2025-06-12 16:09:46,693 - INFO - ------------------------------------------------------------
2025-06-12 16:09:46,693 - INFO - Symbol: AAPL
2025-06-12 16:09:46,693 - INFO - Data points: 22
2025-06-12 16:09:46,693 - INFO - Date range: 1747022400 to 1749614400
2025-06-12 16:09:46,693 - INFO - Latest close: $198.78
2025-06-12 16:09:46,693 - INFO - Volume: 60,820,200
2025-06-12 16:09:46,693 - INFO - Time taken: 0.89 seconds
2025-06-12 16:09:46,693 - INFO - Stopped data agent
2025-06-12 16:09:46,693 - INFO - 
============================================================
2025-06-12 16:09:46,693 - INFO - TEST: MSFT - 1h - 5d
2025-06-12 16:09:46,693 - INFO - ============================================================
2025-06-12 16:09:46,693 - INFO - Initialized data agent
2025-06-12 16:09:46,693 - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 16:09:46,693 - INFO - Starting agent...
2025-06-12 16:09:46,693 - INFO - Started data agent
2025-06-12 16:09:46,693 - INFO - Fetching data for MSFT...
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=MSFT, interval=1h, period=5d, start=None, end=None, max_retries=2, retry_delay=1.0, timeout=30)
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Using period: 5d
2025-06-12 16:09:46,693 - INFO - [DataCollectionAgent] Fetching data for MSFT (attempt 1/2)...
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:09:46
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Created yf.Ticker for MSFT
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=30s
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1h', 'period': '5d', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:09:46,693 - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:09:46
2025-06-12 16:09:46,693 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-06-12 16:09:46,794 - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.10s
2025-06-12 16:09:46,794 - DEBUG - [DataCollectionAgent] Fetched 35 rows for MSFT
2025-06-12 16:09:46,794 - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 35
2025-06-12 16:09:46,794 - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:09:46,797 - DEBUG - [DataCollectionAgent] Caching result with key: MSFT_1h_5d_None_None
2025-06-12 16:09:46,797 - INFO - [DataCollectionAgent] Successfully fetched 35 data points for MSFT in 0.10s
2025-06-12 16:09:46,797 - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:09:46,797 - INFO - 
FETCH SUCCESSFUL
2025-06-12 16:09:46,797 - INFO - ------------------------------------------------------------
2025-06-12 16:09:46,797 - INFO - Symbol: MSFT
2025-06-12 16:09:46,797 - INFO - Data points: 35
2025-06-12 16:09:46,797 - INFO - Date range: 1749130200 to 1749670200
2025-06-12 16:09:46,797 - INFO - Latest close: $472.90
2025-06-12 16:09:46,797 - INFO - Volume: 1,634,641
2025-06-12 16:09:46,797 - INFO - Time taken: 0.10 seconds
2025-06-12 16:09:46,797 - INFO - Stopped data agent
2025-06-12 16:09:46,797 - INFO - 
============================================================
2025-06-12 16:09:46,797 - INFO - TEST: GOOGL - 1d - 1y
2025-06-12 16:09:46,797 - INFO - ============================================================
2025-06-12 16:09:46,797 - INFO - Initialized data agent
2025-06-12 16:09:46,798 - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 16:09:46,798 - INFO - Starting agent...
2025-06-12 16:09:46,798 - INFO - Started data agent
2025-06-12 16:09:46,798 - INFO - Fetching data for GOOGL...
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=GOOGL, interval=1d, period=1y, start=None, end=None, max_retries=2, retry_delay=1.0, timeout=30)
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] Using period: 1y
2025-06-12 16:09:46,798 - INFO - [DataCollectionAgent] Fetching data for GOOGL (attempt 1/2)...
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:09:46
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] Created yf.Ticker for GOOGL
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=30s
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1d', 'period': '1y', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:09:46,798 - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:09:46
2025-06-12 16:09:46,798 - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-06-12 16:09:46,928 - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.13s
2025-06-12 16:09:46,928 - DEBUG - [DataCollectionAgent] Fetched 250 rows for GOOGL
2025-06-12 16:09:46,928 - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 250
2025-06-12 16:09:46,928 - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:09:46,930 - DEBUG - [DataCollectionAgent] Caching result with key: GOOGL_1d_1y_None_None
2025-06-12 16:09:46,930 - INFO - [DataCollectionAgent] Successfully fetched 250 data points for GOOGL in 0.13s
2025-06-12 16:09:46,932 - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:09:46,932 - INFO - 
FETCH SUCCESSFUL
2025-06-12 16:09:46,932 - INFO - ------------------------------------------------------------
2025-06-12 16:09:46,932 - INFO - Symbol: GOOGL
2025-06-12 16:09:46,932 - INFO - Data points: 250
2025-06-12 16:09:46,932 - INFO - Date range: 1718164800 to 1749614400
2025-06-12 16:09:46,932 - INFO - Latest close: $177.35
2025-06-12 16:09:46,932 - INFO - Volume: 31,607,800
2025-06-12 16:09:46,932 - INFO - Time taken: 0.14 seconds
2025-06-12 16:09:46,932 - INFO - Stopped data agent
