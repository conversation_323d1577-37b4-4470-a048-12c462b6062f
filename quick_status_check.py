#!/usr/bin/env python3
"""
QUICK STATUS CHECK
==================

Quick check of our compression research achievements.
"""

import json
from pathlib import Path
import time

def check_achievements():
    """Check our compression achievements"""
    
    print(f"🚀 LOOP COMPRESSION RESEARCH - STATUS CHECK")
    print(f"=" * 60)
    print(f"🎯 Target: 675B parameter model compression for 8GB RAM")
    print(f"⏱️ Check time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    achievements = []
    
    # Check Phase results
    phase_files = [
        ('Phase 1: BitNet Foundation', 'phase1_action1_results.json'),
        ('Phase 2: Enhanced Compression', 'phase2_final_optimization_results.json'),
        ('Phase 3: Ultra-Aggressive', 'phase3_action2_advanced_compression_results.json'),
        ('Phase 4: Neural Architecture Search', 'phase4_neural_architecture_search_results.json'),
        ('Phase 6: Biological-Inspired', 'phase6_biological_inspired_compression_results.json'),
        ('Phase 7: Hyperdimensional', 'phase7_hyperdimensional_compression_results.json'),
        ('Phase 8: Information-Theoretic', 'phase8_information_theoretic_compression_results.json'),
    ]
    
    print(f"\n📊 COMPRESSION PHASE ACHIEVEMENTS:")
    
    for phase_name, filename in phase_files:
        if Path(filename).exists():
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                
                # Try to extract compression ratio
                compression_ratio = extract_compression_ratio(data)
                
                if compression_ratio >= 1000:
                    status = "🚀 BREAKTHROUGH"
                elif compression_ratio >= 100:
                    status = "⭐ MAJOR"
                elif compression_ratio >= 10:
                    status = "✅ SUCCESS"
                else:
                    status = "✅ COMPLETE"
                
                print(f"   {status}: {phase_name} - {compression_ratio:.1f}× compression")
                achievements.append((phase_name, compression_ratio))
                
            except Exception as e:
                print(f"   ❌ ERROR: {phase_name} - {str(e)}")
        else:
            print(f"   ⏸️ PENDING: {phase_name}")
    
    # Check GPT-J 6B download
    print(f"\n📥 DOWNLOAD STATUS:")
    gptj_path = Path('./downloaded_models/gpt_j_6b')
    if gptj_path.exists():
        model_file = gptj_path / 'pytorch_model.bin'
        if model_file.exists():
            file_size = model_file.stat().st_size
            file_size_gb = file_size / (1024**3)
            progress = (file_size_gb / 33.8) * 100
            
            if file_size_gb >= 33.0:
                print(f"   ✅ GPT-J 6B: COMPLETE ({file_size_gb:.1f}GB)")
            else:
                print(f"   ⏳ GPT-J 6B: {progress:.1f}% ({file_size_gb:.1f}GB / 33.8GB)")
        else:
            print(f"   ⏸️ GPT-J 6B: STARTING")
    else:
        print(f"   ❌ GPT-J 6B: NOT FOUND")
    
    # Summary
    print(f"\n🏆 ACHIEVEMENT SUMMARY:")
    
    if achievements:
        total_phases = len(achievements)
        max_compression = max(ratio for _, ratio in achievements)
        avg_compression = sum(ratio for _, ratio in achievements) / len(achievements)
        
        breakthrough_count = len([ratio for _, ratio in achievements if ratio >= 1000])
        major_count = len([ratio for _, ratio in achievements if ratio >= 100])
        
        print(f"   ✅ Completed phases: {total_phases}")
        print(f"   📈 Maximum compression: {max_compression:.1f}×")
        print(f"   📊 Average compression: {avg_compression:.1f}×")
        print(f"   🚀 Breakthrough achievements (1000×+): {breakthrough_count}")
        print(f"   ⭐ Major achievements (100×+): {major_count}")
        
        # Overall status
        if breakthrough_count > 0:
            overall_status = "🚀 BREAKTHROUGH ACHIEVED"
        elif major_count > 0:
            overall_status = "⭐ MAJOR PROGRESS"
        elif total_phases >= 3:
            overall_status = "✅ GOOD PROGRESS"
        else:
            overall_status = "🔄 IN PROGRESS"
        
        print(f"\n🎯 OVERALL STATUS: {overall_status}")
        
        # Mission assessment
        if max_compression >= 1000:
            print(f"🎉 MISSION STATUS: 675B MODEL COMPRESSION FEASIBLE!")
            print(f"   Theoretical compression achieved: {max_compression:.1f}×")
            print(f"   675B model (2.7TB) → {2700/max_compression:.1f}GB")
            if 2700/max_compression <= 8:
                print(f"   ✅ TARGET ACHIEVED: Fits in 8GB RAM!")
            else:
                print(f"   🎯 Close to target: Need {2700/max_compression:.1f}GB")
        else:
            print(f"🔄 MISSION STATUS: RESEARCH CONTINUING")
            print(f"   Current best: {max_compression:.1f}×")
            print(f"   Need: {1000/max_compression:.1f}× more compression for breakthrough")
    
    else:
        print(f"   ❌ No completed phases found")
        print(f"🔄 MISSION STATUS: STARTING")

def extract_compression_ratio(data):
    """Extract compression ratio from result data"""
    
    # Try different possible locations
    possible_keys = [
        'compression_ratio',
        'final_compression_ratio', 
        'best_compression_ratio',
        'max_compression_ratio',
        'average_compression_ratio'
    ]
    
    # Check top level
    for key in possible_keys:
        if key in data:
            ratio = data[key]
            if isinstance(ratio, (int, float)) and ratio > 0:
                if ratio == float('inf'):
                    return 10000.0  # Treat infinite as very high
                return float(ratio)
    
    # Check in summary
    if 'summary' in data:
        for key in possible_keys:
            if key in data['summary']:
                ratio = data['summary'][key]
                if isinstance(ratio, (int, float)) and ratio > 0:
                    if ratio == float('inf'):
                        return 10000.0
                    return float(ratio)
    
    # Check in results
    if 'results' in data:
        for key in possible_keys:
            if key in data['results']:
                ratio = data['results'][key]
                if isinstance(ratio, (int, float)) and ratio > 0:
                    if ratio == float('inf'):
                        return 10000.0
                    return float(ratio)
    
    return 1.0  # Default

if __name__ == "__main__":
    check_achievements()
