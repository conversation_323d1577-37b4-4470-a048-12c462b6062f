"""
Test script for the Execution Agent.
"""
import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from financial_agent.agents.execution_agent import ExecutionAgent, OrderStatus, OrderSide, OrderType
from financial_agent.agents.strategy_agent import TradeSignal, SignalType
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'execution_agent_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Mock OHLCV data for testing
def generate_mock_ohlcv(days: int = 250) -> pd.DataFrame:
    """Generate mock OHLCV data for testing."""
    dates = pd.date_range(end=datetime.now(), periods=days)
    base_price = 100.0
    
    # Generate random walk for prices
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, days)
    prices = base_price * (1 + returns).cumprod()
    
    # Generate OHLC data with some randomness
    df = pd.DataFrame({
        'open': prices * 0.995 + np.random.normal(0, 0.1, days),
        'high': prices * 1.005 + np.abs(np.random.normal(0, 0.1, days)),
        'low': prices * 0.995 - np.abs(np.random.normal(0, 0.1, days)),
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, days)
    }, index=dates)
    
    # Ensure high > low
    df['high'] = df[['high', 'low']].max(axis=1) + 0.01
    df['low'] = df[['high', 'low']].min(axis=1) - 0.01
    
    # Add moving averages for trend analysis
    df['ma_50'] = df['close'].rolling(window=50).mean()
    df['ma_200'] = df['close'].rolling(window=200).mean()
    
    return df

# Generate mock trade signals
def generate_mock_signals(symbol: str, df: pd.DataFrame, num_signals: int = 5) -> list:
    """Generate mock trade signals for testing."""
    signals = []
    
    # Generate signals at regular intervals
    step = max(1, len(df) // (num_signals + 1))
    
    for i in range(1, num_signals + 1):
        idx = min(i * step, len(df) - 1)
        row = df.iloc[idx]
        
        # Alternate between buy and sell signals
        if i % 2 == 1:
            signal_type = SignalType.BUY
            entry_price = row['close'] * 0.99  # Slightly below close for buy
            stop_loss = entry_price * 0.97      # 2% stop loss
            take_profit = entry_price * 1.03    # 4% take profit
        else:
            signal_type = SignalType.SELL
            entry_price = row['close'] * 1.01   # Slightly above close for sell
            stop_loss = entry_price * 1.03      # 2% stop loss
            take_profit = entry_price * 0.97    # 4% take profit
        
        # Create trade signal
        signal = TradeSignal(
            symbol=symbol,
            signal_type=signal_type,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            order_type=OrderType.LIMIT,
            confidence=0.85,
            timestamp=df.index[idx].timestamp(),
            metadata={
                'strategy': 'mean_reversion' if i % 2 == 1 else 'trend_following',
                'indicator': 'RSI' if i % 2 == 1 else 'MACD',
                'confidence': 0.85,
                'notes': 'Test signal'
            }
        )
        
        signals.append(signal)
    
    return signals

# Test function
async def test_execution_agent():
    """Test the Execution Agent with mock data."""
    logger.info("Starting Execution Agent test...")
    
    # Initialize mock LLM wrapper
    llm_wrapper = MistralWrapper()
    
    # Initialize execution agent with test configuration
    execution_agent = ExecutionAgent(
        llm_wrapper=llm_wrapper,
        config={
            'initial_balance': 100000.0,
            'leverage': 1.0,
            'max_position_size': 0.1,  # 10% of account balance
            'risk_per_trade': 0.01,    # 1% risk per trade
            'slippage': 0.0005,        # 0.05% slippage
            'commission_rate': 0.001,   # 0.1% commission
            'max_open_positions': 3,
            'default_stop_loss_pct': 0.02,  # 2% default stop loss
            'default_take_profit_pct': 0.04,  # 4% default take profit
            'allow_shorting': True,
            'allow_leverage': False
        },
        name='execution_test'
    )
    
    try:
        # Start the agent
        await execution_agent.start()
        
        # Generate mock OHLCV data
        symbol = 'BTC/USDT'
        df = generate_mock_ohlcv(days=30)  # 30 days of hourly data
        
        # Generate mock trade signals
        signals = generate_mock_signals(symbol, df, num_signals=5)
        
        # Process each signal
        for i, signal in enumerate(signals, 1):
            logger.info(f"\n=== Processing Signal {i}/{len(signals)} ===")
            logger.info(f"Signal: {signal.signal_type.name} {signal.symbol} @ {signal.entry_price:.2f}")
            
            # Process the signal
            result = await execution_agent.process({'signals': [signal]})
            
            if result.success:
                logger.info(f"Successfully executed trade: {result.data}")
                
                # Get open positions
                positions = await execution_agent.get_open_positions()
                logger.info(f"Open positions: {len(positions)}")
                
                # Log position details
                for pos_symbol, position in positions.items():
                    logger.info(f"  - {pos_symbol}: {position.side.name} {position.quantity:.4f} @ {position.entry_price:.2f}")
                    
                    # Randomly close some positions (every other position)
                    if i % 2 == 0 and pos_symbol == symbol:
                        logger.info(f"  Closing position for {pos_symbol}...")
                        await execution_agent.close_position(pos_symbol, price=position.entry_price * 1.02)  # 2% profit
                        logger.info(f"  Position closed")
            else:
                logger.error(f"Failed to execute trade: {result.error}")
            
            # Small delay between signals
            await asyncio.sleep(0.5)
        
        # Final account status
        logger.info("\n=== Test Complete ===")
        logger.info(f"Final Account Balance: ${execution_agent.account_balance:,.2f}")
        logger.info(f"Available Balance: ${execution_agent.available_balance:,.2f}")
        
        # Log trade history
        if execution_agent.trade_history:
            logger.info("\nTrade History:")
            for i, trade in enumerate(execution_agent.trade_history, 1):
                logger.info(f"{i}. {trade['side']} {trade['quantity']:.4f} {trade['symbol']} "
                           f"@ {trade['entry_price']:.2f} -> {trade.get('exit_price', 'Open'):.2f} "
                           f"(P&L: ${trade.get('pnl', 0):.2f} | {trade.get('pnl_pct', 0):.2f}%)")
        
    except Exception as e:
        logger.error(f"Error in test_execution_agent: {str(e)}", exc_info=True)
    finally:
        # Stop the agent
        await execution_agent.stop()

if __name__ == "__main__":
    asyncio.run(test_execution_agent())
