#!/usr/bin/env python3
"""
AI SCIENTIST COMPONENTS
======================

Supporting components for the autonomous architecture search AI scientist.
Includes hypothesis generation, experiment planning, results analysis, and paper generation.
"""

import json
import time
import logging
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
import numpy as np
from dataclasses import asdict
import random

from autonomous_architecture_search import ArchitectureGenome, FitnessMetrics

logger = logging.getLogger(__name__)

class ResearchDatabase:
    """Database for storing and retrieving research data"""
    
    def __init__(self):
        self.experiments = []
        self.hypotheses = []
        self.insights = []
        self.architectures = []
    
    def store_experiment(self, experiment_data: Dict[str, Any]) -> None:
        """Store experiment results"""
        experiment_data['timestamp'] = time.time()
        self.experiments.append(experiment_data)
    
    def store_hypothesis(self, hypothesis: Dict[str, Any]) -> None:
        """Store research hypothesis"""
        hypothesis['timestamp'] = time.time()
        self.hypotheses.append(hypothesis)
    
    def store_insight(self, insight: Dict[str, Any]) -> None:
        """Store research insight"""
        insight['timestamp'] = time.time()
        self.insights.append(insight)
    
    def query_similar_experiments(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Query for similar experiments"""
        # Simplified similarity matching
        similar = []
        for exp in self.experiments:
            similarity_score = self._calculate_similarity(exp, criteria)
            if similarity_score > 0.7:
                similar.append(exp)
        return similar
    
    def _calculate_similarity(self, exp1: Dict[str, Any], exp2: Dict[str, Any]) -> float:
        """Calculate similarity between experiments"""
        # Simplified similarity calculation
        common_keys = set(exp1.keys()) & set(exp2.keys())
        if not common_keys:
            return 0.0
        
        similarity = 0.0
        for key in common_keys:
            if exp1[key] == exp2[key]:
                similarity += 1.0
        
        return similarity / len(common_keys)

class HypothesisGenerator:
    """Generates research hypotheses based on current knowledge"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.hypothesis_templates = [
            "Increasing {parameter} will improve {metric}",
            "Combining {feature1} with {feature2} will enhance performance",
            "Reducing {parameter} while maintaining {constraint} will improve efficiency",
            "The optimal {parameter} range for {hardware_constraint} is {range}",
            "Architecture pattern {pattern} is most effective for {use_case}"
        ]
    
    async def generate_hypotheses(self, research_history: List[Dict[str, Any]], 
                                population: List[ArchitectureGenome]) -> List[Dict[str, Any]]:
        """Generate hypotheses for the current generation"""
        
        hypotheses = []
        
        # Analyze current population trends
        population_analysis = self._analyze_population(population)
        
        # Generate hypotheses based on trends
        if research_history:
            trend_hypotheses = self._generate_trend_hypotheses(research_history, population_analysis)
            hypotheses.extend(trend_hypotheses)
        
        # Generate exploration hypotheses
        exploration_hypotheses = self._generate_exploration_hypotheses(population_analysis)
        hypotheses.extend(exploration_hypotheses)
        
        # Generate constraint-based hypotheses
        constraint_hypotheses = self._generate_constraint_hypotheses(population_analysis)
        hypotheses.extend(constraint_hypotheses)
        
        logger.info(f"🧠 Generated {len(hypotheses)} research hypotheses")
        
        return hypotheses
    
    def _analyze_population(self, population: List[ArchitectureGenome]) -> Dict[str, Any]:
        """Analyze current population characteristics"""
        
        if not population:
            return {}
        
        # Extract parameter distributions
        num_layers = [g.num_layers for g in population]
        hidden_sizes = [g.hidden_size for g in population]
        compression_ratios = [g.compression_ratio for g in population]
        sparsity_levels = [g.sparsity_level for g in population]
        
        # Count feature usage
        feature_counts = {
            'rotary_embeddings': sum(g.use_rotary_embeddings for g in population),
            'gated_ffn': sum(g.use_gated_ffn for g in population),
            'grouped_query_attention': sum(g.use_grouped_query_attention for g in population),
            'parameter_sharing': sum(g.parameter_sharing for g in population),
            'mixed_precision': sum(g.mixed_precision for g in population)
        }
        
        return {
            'population_size': len(population),
            'num_layers_stats': {
                'mean': np.mean(num_layers),
                'std': np.std(num_layers),
                'min': min(num_layers),
                'max': max(num_layers)
            },
            'hidden_size_stats': {
                'mean': np.mean(hidden_sizes),
                'std': np.std(hidden_sizes),
                'min': min(hidden_sizes),
                'max': max(hidden_sizes)
            },
            'compression_stats': {
                'mean': np.mean(compression_ratios),
                'std': np.std(compression_ratios)
            },
            'sparsity_stats': {
                'mean': np.mean(sparsity_levels),
                'std': np.std(sparsity_levels)
            },
            'feature_usage': feature_counts
        }
    
    def _generate_trend_hypotheses(self, research_history: List[Dict[str, Any]], 
                                 population_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate hypotheses based on observed trends"""
        
        hypotheses = []
        
        if len(research_history) < 2:
            return hypotheses
        
        # Analyze fitness trends
        recent_generations = research_history[-5:]  # Last 5 generations
        fitness_trends = []
        
        for gen_data in recent_generations:
            avg_fitness = np.mean(gen_data['fitness_scores'])
            max_fitness = max(gen_data['fitness_scores'])
            fitness_trends.append({'avg': avg_fitness, 'max': max_fitness})
        
        # Check if fitness is improving
        if len(fitness_trends) >= 2:
            recent_improvement = fitness_trends[-1]['max'] - fitness_trends[-2]['max']
            
            if recent_improvement > 0.01:
                hypotheses.append({
                    'type': 'trend_positive',
                    'hypothesis': 'Current evolutionary direction is promising and should be continued',
                    'confidence': 0.8,
                    'suggested_action': 'maintain_current_strategy'
                })
            elif recent_improvement < -0.01:
                hypotheses.append({
                    'type': 'trend_negative',
                    'hypothesis': 'Current evolutionary direction is not improving performance',
                    'confidence': 0.7,
                    'suggested_action': 'increase_exploration'
                })
        
        # Analyze parameter trends
        if 'num_layers_stats' in population_analysis:
            layer_mean = population_analysis['num_layers_stats']['mean']
            
            if layer_mean > 20:
                hypotheses.append({
                    'type': 'parameter_trend',
                    'hypothesis': 'Population is trending toward deeper models, may hit diminishing returns',
                    'confidence': 0.6,
                    'suggested_action': 'explore_wider_models'
                })
            elif layer_mean < 8:
                hypotheses.append({
                    'type': 'parameter_trend',
                    'hypothesis': 'Population is trending toward shallow models, may benefit from more depth',
                    'confidence': 0.6,
                    'suggested_action': 'explore_deeper_models'
                })
        
        return hypotheses
    
    def _generate_exploration_hypotheses(self, population_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate hypotheses for exploring new regions"""
        
        hypotheses = []
        
        # Feature exploration hypotheses
        if 'feature_usage' in population_analysis:
            feature_usage = population_analysis['feature_usage']
            population_size = population_analysis['population_size']
            
            for feature, count in feature_usage.items():
                usage_rate = count / population_size
                
                if usage_rate < 0.2:  # Underexplored feature
                    hypotheses.append({
                        'type': 'feature_exploration',
                        'hypothesis': f'{feature} is underexplored and may provide benefits',
                        'confidence': 0.5,
                        'suggested_action': f'increase_{feature}_usage',
                        'target_feature': feature
                    })
                elif usage_rate > 0.8:  # Overused feature
                    hypotheses.append({
                        'type': 'feature_saturation',
                        'hypothesis': f'{feature} is overused, alternatives should be explored',
                        'confidence': 0.6,
                        'suggested_action': f'explore_alternatives_to_{feature}',
                        'target_feature': feature
                    })
        
        # Parameter space exploration
        if 'compression_stats' in population_analysis:
            compression_std = population_analysis['compression_stats']['std']
            
            if compression_std < 0.05:  # Low diversity in compression
                hypotheses.append({
                    'type': 'parameter_exploration',
                    'hypothesis': 'Compression ratio diversity is low, broader exploration needed',
                    'confidence': 0.7,
                    'suggested_action': 'increase_compression_diversity'
                })
        
        return hypotheses
    
    def _generate_constraint_hypotheses(self, population_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate hypotheses related to hardware constraints"""
        
        hypotheses = []
        
        # Memory constraint hypotheses
        hypotheses.append({
            'type': 'memory_constraint',
            'hypothesis': 'Architectures exceeding 8GB memory will not be viable for consumer hardware',
            'confidence': 0.9,
            'suggested_action': 'enforce_memory_limits'
        })
        
        # Speed constraint hypotheses
        hypotheses.append({
            'type': 'speed_constraint',
            'hypothesis': 'Inference speed below 50 tokens/sec will not be acceptable for real-time applications',
            'confidence': 0.8,
            'suggested_action': 'prioritize_speed_optimization'
        })
        
        # Compression trade-off hypotheses
        hypotheses.append({
            'type': 'compression_tradeoff',
            'hypothesis': 'Compression ratios above 0.3 may significantly impact accuracy',
            'confidence': 0.7,
            'suggested_action': 'balance_compression_accuracy'
        })
        
        return hypotheses

class ExperimentPlanner:
    """Plans experiments based on hypotheses"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    def plan_experiments(self, hypotheses: List[Dict[str, Any]], 
                        population: List[ArchitectureGenome]) -> List[Dict[str, Any]]:
        """Plan experiments to test hypotheses"""
        
        experiments = []
        
        for hypothesis in hypotheses:
            experiment = self._design_experiment(hypothesis, population)
            if experiment:
                experiments.append(experiment)
        
        logger.info(f"🔬 Planned {len(experiments)} experiments")
        
        return experiments
    
    def _design_experiment(self, hypothesis: Dict[str, Any], 
                          population: List[ArchitectureGenome]) -> Optional[Dict[str, Any]]:
        """Design an experiment to test a specific hypothesis"""
        
        hypothesis_type = hypothesis.get('type', '')
        
        if hypothesis_type == 'feature_exploration':
            return self._design_feature_experiment(hypothesis, population)
        elif hypothesis_type == 'parameter_trend':
            return self._design_parameter_experiment(hypothesis, population)
        elif hypothesis_type == 'memory_constraint':
            return self._design_constraint_experiment(hypothesis, population)
        else:
            return self._design_general_experiment(hypothesis, population)
    
    def _design_feature_experiment(self, hypothesis: Dict[str, Any], 
                                 population: List[ArchitectureGenome]) -> Dict[str, Any]:
        """Design experiment to test feature effectiveness"""
        
        target_feature = hypothesis.get('target_feature', '')
        
        return {
            'type': 'feature_test',
            'hypothesis_id': id(hypothesis),
            'description': f'Test effectiveness of {target_feature}',
            'method': 'compare_with_without_feature',
            'target_feature': target_feature,
            'sample_size': min(10, len(population) // 5),
            'expected_outcome': hypothesis.get('suggested_action', '')
        }
    
    def _design_parameter_experiment(self, hypothesis: Dict[str, Any], 
                                   population: List[ArchitectureGenome]) -> Dict[str, Any]:
        """Design experiment to test parameter ranges"""
        
        return {
            'type': 'parameter_sweep',
            'hypothesis_id': id(hypothesis),
            'description': 'Test optimal parameter ranges',
            'method': 'systematic_parameter_variation',
            'parameters_to_test': ['num_layers', 'hidden_size'],
            'sample_size': min(15, len(population) // 3),
            'expected_outcome': hypothesis.get('suggested_action', '')
        }
    
    def _design_constraint_experiment(self, hypothesis: Dict[str, Any], 
                                    population: List[ArchitectureGenome]) -> Dict[str, Any]:
        """Design experiment to test constraint boundaries"""
        
        return {
            'type': 'constraint_test',
            'hypothesis_id': id(hypothesis),
            'description': 'Test hardware constraint boundaries',
            'method': 'boundary_exploration',
            'constraints_to_test': ['memory_usage', 'inference_speed'],
            'sample_size': min(8, len(population) // 6),
            'expected_outcome': hypothesis.get('suggested_action', '')
        }
    
    def _design_general_experiment(self, hypothesis: Dict[str, Any], 
                                 population: List[ArchitectureGenome]) -> Dict[str, Any]:
        """Design general experiment"""
        
        return {
            'type': 'general_test',
            'hypothesis_id': id(hypothesis),
            'description': 'General hypothesis testing',
            'method': 'comparative_analysis',
            'sample_size': min(12, len(population) // 4),
            'expected_outcome': hypothesis.get('suggested_action', '')
        }

class ResultsAnalyzer:
    """Analyzes experimental results and extracts insights"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    def analyze_generation_results(self, population: List[ArchitectureGenome], 
                                 fitness_scores: List[float], 
                                 hypotheses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze results from a generation and extract insights"""
        
        insights = []
        
        # Analyze fitness distribution
        fitness_insights = self._analyze_fitness_distribution(fitness_scores)
        insights.extend(fitness_insights)
        
        # Analyze population diversity
        diversity_insights = self._analyze_population_diversity(population)
        insights.extend(diversity_insights)
        
        # Analyze hypothesis outcomes
        hypothesis_insights = self._analyze_hypothesis_outcomes(
            population, fitness_scores, hypotheses
        )
        insights.extend(hypothesis_insights)
        
        # Analyze architectural patterns
        pattern_insights = self._analyze_architectural_patterns(population, fitness_scores)
        insights.extend(pattern_insights)
        
        logger.info(f"🔍 Extracted {len(insights)} insights from generation results")
        
        return insights
    
    def _analyze_fitness_distribution(self, fitness_scores: List[float]) -> List[Dict[str, Any]]:
        """Analyze fitness score distribution"""
        
        insights = []
        
        if not fitness_scores:
            return insights
        
        mean_fitness = np.mean(fitness_scores)
        std_fitness = np.std(fitness_scores)
        max_fitness = max(fitness_scores)
        
        # Check for convergence
        if std_fitness < 0.05:
            insights.append({
                'type': 'convergence_detected',
                'description': 'Population fitness has low variance, indicating convergence',
                'confidence': 0.8,
                'recommendation': 'increase_exploration'
            })
        
        # Check for stagnation
        if max_fitness < 0.3:
            insights.append({
                'type': 'low_performance',
                'description': 'Maximum fitness is low, search may be stuck in local optimum',
                'confidence': 0.7,
                'recommendation': 'restart_with_new_population'
            })
        
        # Check for high performance
        if max_fitness > 0.8:
            insights.append({
                'type': 'high_performance',
                'description': 'High-performing architectures discovered',
                'confidence': 0.9,
                'recommendation': 'exploit_current_region'
            })
        
        return insights
    
    def _analyze_population_diversity(self, population: List[ArchitectureGenome]) -> List[Dict[str, Any]]:
        """Analyze population diversity"""
        
        insights = []
        
        if len(population) < 2:
            return insights
        
        # Calculate parameter diversity
        num_layers_diversity = len(set(g.num_layers for g in population)) / len(population)
        hidden_size_diversity = len(set(g.hidden_size for g in population)) / len(population)
        
        avg_diversity = (num_layers_diversity + hidden_size_diversity) / 2
        
        if avg_diversity < 0.3:
            insights.append({
                'type': 'diversity_low',
                'description': 'Population diversity is low',
                'confidence': 0.8,
                'recommendation': 'increase_mutation_rate'
            })
        elif avg_diversity > 0.8:
            insights.append({
                'type': 'diversity_high',
                'description': 'Population diversity is high',
                'confidence': 0.7,
                'recommendation': 'focus_exploitation'
            })
        
        return insights
    
    def _analyze_hypothesis_outcomes(self, population: List[ArchitectureGenome], 
                                   fitness_scores: List[float], 
                                   hypotheses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze how well hypotheses were supported"""
        
        insights = []
        
        # This is a simplified analysis - in practice, would need more sophisticated
        # tracking of which architectures were generated based on which hypotheses
        
        for hypothesis in hypotheses:
            hypothesis_type = hypothesis.get('type', '')
            
            if hypothesis_type == 'feature_exploration':
                # Check if the suggested feature led to improvements
                target_feature = hypothesis.get('target_feature', '')
                insight = self._evaluate_feature_hypothesis(
                    population, fitness_scores, target_feature
                )
                if insight:
                    insights.append(insight)
        
        return insights
    
    def _evaluate_feature_hypothesis(self, population: List[ArchitectureGenome], 
                                   fitness_scores: List[float], 
                                   feature: str) -> Optional[Dict[str, Any]]:
        """Evaluate whether a feature hypothesis was supported"""
        
        if not hasattr(population[0], feature):
            return None
        
        # Split population by feature usage
        with_feature = []
        without_feature = []
        
        for i, genome in enumerate(population):
            if getattr(genome, feature, False):
                with_feature.append(fitness_scores[i])
            else:
                without_feature.append(fitness_scores[i])
        
        if not with_feature or not without_feature:
            return None
        
        # Compare performance
        avg_with = np.mean(with_feature)
        avg_without = np.mean(without_feature)
        
        if avg_with > avg_without + 0.05:  # Significant improvement
            return {
                'type': 'feature_validated',
                'description': f'{feature} shows positive impact on performance',
                'confidence': 0.7,
                'recommendation': f'increase_{feature}_usage'
            }
        elif avg_without > avg_with + 0.05:  # Feature is harmful
            return {
                'type': 'feature_rejected',
                'description': f'{feature} shows negative impact on performance',
                'confidence': 0.7,
                'recommendation': f'decrease_{feature}_usage'
            }
        
        return None
    
    def _analyze_architectural_patterns(self, population: List[ArchitectureGenome], 
                                      fitness_scores: List[float]) -> List[Dict[str, Any]]:
        """Analyze successful architectural patterns"""
        
        insights = []
        
        # Find top performers
        top_indices = np.argsort(fitness_scores)[-5:]  # Top 5
        top_genomes = [population[i] for i in top_indices]
        
        if len(top_genomes) < 2:
            return insights
        
        # Look for common patterns in top performers
        common_features = self._find_common_features(top_genomes)
        
        for feature, prevalence in common_features.items():
            if prevalence >= 0.8:  # 80% of top performers have this feature
                insights.append({
                    'type': 'successful_pattern',
                    'description': f'{feature} is common in high-performing architectures',
                    'confidence': 0.8,
                    'recommendation': f'prioritize_{feature}'
                })
        
        return insights
    
    def _find_common_features(self, genomes: List[ArchitectureGenome]) -> Dict[str, float]:
        """Find common features among a set of genomes"""
        
        if not genomes:
            return {}
        
        feature_counts = {}
        boolean_features = [
            'use_rotary_embeddings', 'use_gated_ffn', 'use_grouped_query_attention',
            'gradient_checkpointing', 'mixed_precision', 'parameter_sharing',
            'adaptive_attention_span', 'use_distillation'
        ]
        
        for feature in boolean_features:
            count = sum(getattr(genome, feature, False) for genome in genomes)
            feature_counts[feature] = count / len(genomes)
        
        return feature_counts
