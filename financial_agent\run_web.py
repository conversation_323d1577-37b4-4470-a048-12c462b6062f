#!/usr/bin/env python3
"""
Run the Financial Agent web interface.
"""
import os
import uvicorn
from financial_agent.web.app import app

def main():
    """Start the web interface."""
    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "false").lower() == "true"
    
    print(f"Starting Financial Agent web interface on http://{host}:{port}")
    print("Press Ctrl+C to stop")
    
    # Start the server
    uvicorn.run(
        "financial_agent.web.app:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if debug else "warning"
    )

if __name__ == "__main__":
    main()
