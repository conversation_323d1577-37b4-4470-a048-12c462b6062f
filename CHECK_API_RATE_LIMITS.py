#!/usr/bin/env python3
"""
CHECK API RATE LIMITS AND USAGE
===============================

Check current Gemini API rate limits and usage status
"""

import json
import google.generativeai as genai
from datetime import datetime, timedelta
import time

def check_rate_limits_and_usage():
    """Check current rate limits and usage status"""
    
    print("📊 CHECKING GEMINI API RATE LIMITS & USAGE")
    print("=" * 60)
    
    # Load configuration
    try:
        with open('gemini_config.json', 'r') as f:
            config = json.load(f)
        
        rate_limits = config.get('rate_limits', {})
        print("📋 CONFIGURED RATE LIMITS:")
        print(f"   Daily requests: {rate_limits.get('requests_per_day', 'Unknown')}")
        print(f"   Daily tokens: {rate_limits.get('tokens_per_day', 'Unknown')}")
        print(f"   Requests per minute: {rate_limits.get('requests_per_minute', 'Unknown')}")
        print(f"   Delay between requests: {rate_limits.get('delay_between_requests', 'Unknown')}s")
        
    except Exception as e:
        print(f"⚠️ Could not load config: {e}")
        rate_limits = {
            'requests_per_day': 25,
            'tokens_per_day': 250000,
            'requests_per_minute': 2,
            'delay_between_requests': 30
        }
        print("📋 USING DEFAULT RATE LIMITS:")
        print(f"   Daily requests: {rate_limits['requests_per_day']}")
        print(f"   Daily tokens: {rate_limits['tokens_per_day']}")
        print(f"   Requests per minute: {rate_limits['requests_per_minute']}")
        print(f"   Delay between requests: {rate_limits['delay_between_requests']}s")
    
    # Test API availability
    print(f"\n🧪 TESTING API AVAILABILITY:")
    try:
        genai.configure(api_key="AIzaSyAYmCiRrFwIFe9VOUd00A4r6GJVI8ZhAFE")
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
        print("🔬 Sending test query...")
        start_time = time.time()
        
        response = model.generate_content("Test query: What is AGI? (One sentence only)")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response and response.text:
            print(f"✅ API is working!")
            print(f"   Response time: {response_time:.2f}s")
            print(f"   Response length: {len(response.text)} characters")
            print(f"   Response: {response.text[:100]}...")
            
            # Estimate token usage
            estimated_tokens = len(response.text.split()) * 1.3  # Rough estimate
            print(f"   Estimated tokens used: ~{estimated_tokens:.0f}")
            
            api_working = True
        else:
            print("❌ No response received")
            api_working = False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        if "429" in str(e) or "quota" in str(e).lower():
            print("🚨 RATE LIMIT HIT - Need to wait or manage usage")
        api_working = False
    
    # Calculate usage recommendations
    print(f"\n📈 USAGE RECOMMENDATIONS:")
    
    if api_working:
        print("✅ API is currently available")
        
        # Calculate safe usage for AGI research
        daily_requests = rate_limits['requests_per_day']
        requests_per_minute = rate_limits['requests_per_minute']
        delay_seconds = rate_limits['delay_between_requests']
        
        # Conservative estimates for AGI research
        agi_research_requests = min(15, daily_requests - 5)  # Leave buffer
        research_sessions = max(1, agi_research_requests // 5)  # 5 requests per session
        
        print(f"📊 SAFE USAGE FOR AGI RESEARCH:")
        print(f"   Available requests today: ~{agi_research_requests}")
        print(f"   Recommended research sessions: {research_sessions}")
        print(f"   Requests per session: 5")
        print(f"   Minimum delay between requests: {delay_seconds}s")
        print(f"   Estimated session duration: {5 * delay_seconds / 60:.1f} minutes")
        
        usage_status = "AVAILABLE"
        
    else:
        print("❌ API currently unavailable (likely rate limited)")
        print("⏰ WAIT RECOMMENDATIONS:")
        print("   - Wait 1-2 hours and try again")
        print("   - Rate limits reset daily")
        print("   - Use smaller, focused queries")
        print("   - Implement longer delays between requests")
        
        usage_status = "RATE_LIMITED"
    
    # Save usage status
    usage_report = {
        "timestamp": datetime.now().isoformat(),
        "api_working": api_working,
        "usage_status": usage_status,
        "rate_limits": rate_limits,
        "recommendations": {
            "safe_daily_requests": agi_research_requests if api_working else 0,
            "research_sessions": research_sessions if api_working else 0,
            "delay_between_requests": delay_seconds,
            "next_check_time": (datetime.now() + timedelta(hours=1)).isoformat()
        }
    }
    
    with open("api_usage_status.json", 'w') as f:
        json.dump(usage_report, f, indent=2)
    
    print(f"\n💾 Usage status saved to: api_usage_status.json")
    
    return usage_report

def estimate_agi_research_capacity():
    """Estimate research capacity for AGI algorithm discovery"""
    
    print(f"\n🧠 AGI RESEARCH CAPACITY ESTIMATION:")
    print("=" * 60)
    
    # Load usage status
    try:
        with open("api_usage_status.json", 'r') as f:
            usage_status = json.load(f)
    except:
        print("⚠️ No usage status found, using defaults")
        return
    
    if usage_status["api_working"]:
        safe_requests = usage_status["recommendations"]["safe_daily_requests"]
        
        # AGI research phases and their request requirements
        agi_phases = {
            "literature_analysis": 3,      # Analyze current AGI state
            "problem_identification": 2,   # Identify Mistral 7B limitations  
            "algorithm_generation": 4,     # Generate novel AGI algorithms
            "optimization_research": 3,    # Research optimization techniques
            "implementation_planning": 2   # Create implementation roadmap
        }
        
        total_requests_needed = sum(agi_phases.values())
        
        print(f"📋 AGI RESEARCH PHASES:")
        for phase, requests in agi_phases.items():
            print(f"   {phase}: {requests} requests")
        
        print(f"\n📊 CAPACITY ANALYSIS:")
        print(f"   Total requests needed: {total_requests_needed}")
        print(f"   Safe requests available: {safe_requests}")
        
        if safe_requests >= total_requests_needed:
            print(f"✅ FULL RESEARCH POSSIBLE")
            print(f"   Can complete all phases today")
            print(f"   Remaining requests: {safe_requests - total_requests_needed}")
        elif safe_requests >= total_requests_needed // 2:
            print(f"⚠️ PARTIAL RESEARCH POSSIBLE")
            print(f"   Can complete ~{safe_requests / total_requests_needed * 100:.0f}% of research")
            print(f"   Recommend prioritizing key phases")
        else:
            print(f"❌ LIMITED RESEARCH CAPACITY")
            print(f"   Only {safe_requests} requests available")
            print(f"   Recommend waiting or using minimal queries")
        
        # Recommend research strategy
        print(f"\n🎯 RECOMMENDED STRATEGY:")
        if safe_requests >= total_requests_needed:
            print("   Execute full AGI research pipeline")
            print("   Use all phases with proper delays")
        elif safe_requests >= 8:
            print("   Focus on core phases:")
            print("   1. Literature analysis (3 requests)")
            print("   2. Algorithm generation (4 requests)")
            print("   3. Skip detailed optimization for now")
        elif safe_requests >= 4:
            print("   Minimal research approach:")
            print("   1. Problem identification (2 requests)")
            print("   2. Basic algorithm generation (2 requests)")
        else:
            print("   Wait for rate limit reset")
            print("   Or use single focused query")
    
    else:
        print("❌ API currently unavailable")
        print("   Wait for rate limits to reset")
        print("   Estimated reset: Next day (UTC)")

def main():
    """Main function"""
    
    print("🔍 GEMINI API RATE LIMITS & USAGE CHECK")
    print("=" * 70)
    print("Checking current status for AGI research project")
    print()
    
    # Check rate limits and usage
    usage_report = check_rate_limits_and_usage()
    
    # Estimate AGI research capacity
    estimate_agi_research_capacity()
    
    print(f"\n🎯 SUMMARY:")
    if usage_report["api_working"]:
        print("✅ Ready to proceed with AGI research")
        print("📊 Check recommendations above for safe usage")
    else:
        print("⏰ Need to wait for rate limit reset")
        print("🔄 Try again in 1-2 hours")
    
    return usage_report

if __name__ == "__main__":
    main()
