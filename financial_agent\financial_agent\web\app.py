"""
Web interface for monitoring the trading system.
"""
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi import Request
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Financial Agent Dashboard",
    description="Web interface for monitoring the trading system",
    version="1.0.0"
)

# Set up templates
templates = Jinja2Templates(directory=os.path.join(os.path.dirname(__file__), "templates"))

# Mount static files
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

# In-memory data store (replace with database in production)
class DataStore:
    def __init__(self):
        self.portfolio = {
            'current_value': 100000.0,
            'peak_value': 100000.0,
            'cash': 100000.0,
            'positions': {},
            'performance': {
                'daily': 0.0,
                'weekly': 0.0,
                'monthly': 0.0,
                'ytd': 0.0,
                'all_time': 0.0
            },
            'last_updated': datetime.utcnow().isoformat()
        }
        self.trades = []
        self.risk_metrics = {}
        self.connected_clients = set()

# Initialize data store
data_store = DataStore()

# WebSocket manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"New WebSocket connection. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Remaining connections: {len(self.active_connections)}")

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                self.disconnect(connection)

manager = ConnectionManager()

# Models
class TradeSignal(BaseModel):
    symbol: str
    signal_type: str
    entry_price: float
    size_percent: float
    timestamp: str
    metadata: Dict[str, Any] = {}

class Position(BaseModel):
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    pnl: float
    pnl_pct: float
    sector: str
    beta: float

class PortfolioMetrics(BaseModel):
    current_value: float
    peak_value: float
    drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    volatility: float
    beta: float
    alpha: float

# API Endpoints
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serve the main dashboard page."""
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "title": "Trading Dashboard"}
    )

@app.get("/api/portfolio", response_model=Dict[str, Any])
async def get_portfolio():
    """Get current portfolio data."""
    return data_store.portfolio

@app.get("/api/trades", response_model=List[Dict[str, Any]])
async def get_trades(limit: int = 50):
    """Get recent trades."""
    return data_store.trades[-limit:]

@app.get("/api/risk", response_model=Dict[str, Any])
async def get_risk_metrics():
    """Get current risk metrics."""
    return data_store.risk_metrics

@app.get("/api/performance", response_model=Dict[str, Any])
async def get_performance_metrics():
    """Get performance metrics."""
    return {
        "portfolio": data_store.portfolio,
        "risk": data_store.risk_metrics
    }

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
            # Send current state
            await websocket.send_json({
                "type": "update",
                "data": {
                    "portfolio": data_store.portfolio,
                    "risk": data_store.risk_metrics,
                    "last_updated": datetime.utcnow().isoformat()
                }
            })
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Background task to update data (in a real app, this would be connected to your trading system)
async def update_data():
    """Background task to update portfolio and risk data."""
    import random
    import time
    
    while True:
        try:
            # Simulate market data updates
            for symbol, position in data_store.portfolio['positions'].items():
                # Random price movement
                price_change = (random.random() - 0.5) * 0.02  # ±1% change
                position['current_price'] *= (1 + price_change)
                position['value'] = position['quantity'] * position['current_price']
                position['pnl'] = position['value'] - (position['quantity'] * position['entry_price'])
                position['pnl_pct'] = (position['current_price'] / position['entry_price'] - 1) * 100
            
            # Update portfolio value
            total_value = sum(p['value'] for p in data_store.portfolio['positions'].values()) + data_store.portfolio['cash']
            data_store.portfolio['current_value'] = total_value
            data_store.portfolio['peak_value'] = max(data_store.portfolio['peak_value'], total_value)
            data_store.portfolio['last_updated'] = datetime.utcnow().isoformat()
            
            # Update performance metrics
            data_store.portfolio['performance']['daily'] = (random.random() - 0.5) * 0.02
            data_store.portfolio['performance']['weekly'] = (random.random() - 0.4) * 0.05
            data_store.portfolio['performance']['monthly'] = (random.random() - 0.3) * 0.1
            data_store.portfolio['performance']['ytd'] = (random.random() + 0.5) * 0.15
            data_store.portfolio['performance']['all_time'] = (random.random() + 1.0) * 0.25
            
            # Update risk metrics
            data_store.risk_metrics = {
                'volatility': random.random() * 0.1 + 0.05,  # 5-15%
                'sharpe_ratio': random.random() * 1.5 + 0.5,  # 0.5-2.0
                'max_drawdown': random.random() * 0.05 + 0.02,  # 2-7%
                'beta': random.random() * 0.5 + 0.75,  # 0.75-1.25
                'alpha': (random.random() - 0.5) * 0.1,  # -5% to +5%
                'last_updated': datetime.utcnow().isoformat()
            }
            
            # Broadcast updates to all connected clients
            await manager.broadcast(json.dumps({
                "type": "update",
                "data": {
                    "portfolio": data_store.portfolio,
                    "risk": data_store.risk_metrics,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }))
            
            # Wait before next update
            await asyncio.sleep(5)
            
        except Exception as e:
            logger.error(f"Error in update_data: {e}")
            await asyncio.sleep(5)

@app.on_event("startup")
async def startup_event():
    """Initialize the application."""
    # Create some sample data
    data_store.portfolio['positions'] = {
        'AAPL': {
            'symbol': 'AAPL',
            'quantity': 10,
            'entry_price': 150.0,
            'current_price': 155.0,
            'value': 1550.0,
            'pnl': 50.0,
            'pnl_pct': 3.33,
            'sector': 'Technology',
            'beta': 1.2
        },
        'MSFT': {
            'symbol': 'MSFT',
            'quantity': 5,
            'entry_price': 300.0,
            'current_price': 310.0,
            'value': 1550.0,
            'pnl': 50.0,
            'pnl_pct': 3.23,
            'sector': 'Technology',
            'beta': 0.9
        }
    }
    data_store.portfolio['current_value'] = 3100.0 + data_store.portfolio['cash']
    data_store.portfolio['peak_value'] = data_store.portfolio['current_value']
    
    # Start background tasks
    import asyncio
    asyncio.create_task(update_data())

if __name__ == "__main__":
    # Run with auto-reload for development
    import os
    import json
    import logging
    import asyncio
    import uvicorn
    uvicorn.run(
        "financial_agent.web.app:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8000)),
        reload=True,
        log_level="info"
    )
