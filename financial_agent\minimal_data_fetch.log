2025-06-12 15:56:44,420 - asyncio - DEBUG - Using proactor: IocpProactor
2025-06-12 15:56:44,422 - __main__ - INFO - Starting minimal data fetch test...
2025-06-12 15:56:44,422 - __main__ - INFO - Fetching AAPL data with interval=1d, period=1mo
2025-06-12 15:56:44,424 - yfinance - DEBUG - Entering history()
2025-06-12 15:56:44,428 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-06-12 15:56:44,428 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-06-12 15:56:44,516 - yfinance - DEBUG -  Entering history()
2025-06-12 15:56:44,516 - yfinance - DEBUG - AAPL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:56:44,516 - yfinance - DEBUG -   Entering get()
2025-06-12 15:56:44,520 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 15:56:44,520 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-06-12 15:56:44,521 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:56:44,521 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 15:56:44,521 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 15:56:44,522 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 15:56:44,522 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 15:56:44,522 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-06-12 15:56:44,524 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-06-12 15:56:44,524 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-06-12 15:56:44,528 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-06-12 15:56:44,528 - yfinance - DEBUG - reusing persistent cookie
2025-06-12 15:56:44,528 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 15:56:44,528 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 15:56:44,528 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-06-12 15:56:44,528 - yfinance - DEBUG - reusing cookie
2025-06-12 15:56:44,530 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-06-12 15:56:44,703 - yfinance - DEBUG - crumb = 'iDku.3/e0AN'
2025-06-12 15:56:44,703 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 15:56:44,703 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 15:56:44,707 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 15:56:44,979 - yfinance - DEBUG - response code=200
2025-06-12 15:56:44,979 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 15:56:44,979 - yfinance - DEBUG -   Exiting get()
2025-06-12 15:56:44,988 - yfinance - DEBUG - AAPL: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 15:56:44,992 - yfinance - DEBUG - AAPL: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 15:56:45,000 - yfinance - DEBUG - AAPL: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,006 - yfinance - DEBUG - AAPL: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,006 - yfinance - DEBUG -  Exiting history()
2025-06-12 15:56:45,006 - yfinance - DEBUG - Exiting history()
2025-06-12 15:56:45,008 - __main__ - INFO - Successfully fetched 22 rows for AAPL
2025-06-12 15:56:45,008 - __main__ - INFO - AAPL data sample:
2025-06-12 15:56:45,008 - __main__ - INFO -   First date: 2025-05-12 00:00:00-04:00
2025-06-12 15:56:45,008 - __main__ - INFO -   Last date: 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,008 - __main__ - INFO -   Latest close: $198.78
2025-06-12 15:56:45,008 - __main__ - INFO -   Volume: 60,820,200
2025-06-12 15:56:45,008 - __main__ - INFO - --------------------------------------------------------------------------------
2025-06-12 15:56:45,008 - __main__ - INFO - Fetching MSFT data with interval=1d, period=1mo
2025-06-12 15:56:45,011 - yfinance - DEBUG - Entering history()
2025-06-12 15:56:45,011 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-06-12 15:56:45,011 - yfinance - DEBUG -  Entering history()
2025-06-12 15:56:45,011 - yfinance - DEBUG - MSFT: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:56:45,011 - yfinance - DEBUG -   Entering get()
2025-06-12 15:56:45,011 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 15:56:45,011 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-06-12 15:56:45,011 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:56:45,011 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 15:56:45,011 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 15:56:45,014 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 15:56:45,014 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 15:56:45,014 - yfinance - DEBUG - reusing cookie
2025-06-12 15:56:45,014 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 15:56:45,014 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 15:56:45,014 - yfinance - DEBUG - reusing crumb
2025-06-12 15:56:45,014 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 15:56:45,014 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 15:56:45,016 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 15:56:45,161 - yfinance - DEBUG - response code=200
2025-06-12 15:56:45,162 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 15:56:45,162 - yfinance - DEBUG -   Exiting get()
2025-06-12 15:56:45,163 - yfinance - DEBUG - MSFT: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 15:56:45,164 - yfinance - DEBUG - MSFT: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 15:56:45,168 - yfinance - DEBUG - MSFT: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,173 - yfinance - DEBUG - MSFT: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,173 - yfinance - DEBUG -  Exiting history()
2025-06-12 15:56:45,173 - yfinance - DEBUG - Exiting history()
2025-06-12 15:56:45,173 - __main__ - INFO - Successfully fetched 22 rows for MSFT
2025-06-12 15:56:45,173 - __main__ - INFO - MSFT data sample:
2025-06-12 15:56:45,173 - __main__ - INFO -   First date: 2025-05-12 00:00:00-04:00
2025-06-12 15:56:45,177 - __main__ - INFO -   Last date: 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,178 - __main__ - INFO -   Latest close: $472.62
2025-06-12 15:56:45,178 - __main__ - INFO -   Volume: 16,376,800
2025-06-12 15:56:45,178 - __main__ - INFO - --------------------------------------------------------------------------------
2025-06-12 15:56:45,178 - __main__ - INFO - Fetching GOOGL data with interval=1d, period=1mo
2025-06-12 15:56:45,178 - yfinance - DEBUG - Entering history()
2025-06-12 15:56:45,179 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-06-12 15:56:45,179 - yfinance - DEBUG -  Entering history()
2025-06-12 15:56:45,179 - yfinance - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:56:45,179 - yfinance - DEBUG -   Entering get()
2025-06-12 15:56:45,182 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 15:56:45,182 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-06-12 15:56:45,182 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:56:45,182 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 15:56:45,182 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 15:56:45,182 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 15:56:45,182 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 15:56:45,182 - yfinance - DEBUG - reusing cookie
2025-06-12 15:56:45,182 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 15:56:45,182 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 15:56:45,182 - yfinance - DEBUG - reusing crumb
2025-06-12 15:56:45,182 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 15:56:45,182 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 15:56:45,182 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 15:56:45,311 - yfinance - DEBUG - response code=200
2025-06-12 15:56:45,311 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 15:56:45,311 - yfinance - DEBUG -   Exiting get()
2025-06-12 15:56:45,311 - yfinance - DEBUG - GOOGL: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 15:56:45,311 - yfinance - DEBUG - GOOGL: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 15:56:45,320 - yfinance - DEBUG - GOOGL: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,328 - yfinance - DEBUG - GOOGL: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,328 - yfinance - DEBUG -  Exiting history()
2025-06-12 15:56:45,328 - yfinance - DEBUG - Exiting history()
2025-06-12 15:56:45,328 - __main__ - INFO - Successfully fetched 22 rows for GOOGL
2025-06-12 15:56:45,328 - __main__ - INFO - GOOGL data sample:
2025-06-12 15:56:45,330 - __main__ - INFO -   First date: 2025-05-12 00:00:00-04:00
2025-06-12 15:56:45,330 - __main__ - INFO -   Last date: 2025-06-11 00:00:00-04:00
2025-06-12 15:56:45,330 - __main__ - INFO -   Latest close: $177.35
2025-06-12 15:56:45,330 - __main__ - INFO -   Volume: 31,607,800
2025-06-12 15:56:45,330 - __main__ - INFO - --------------------------------------------------------------------------------
2025-06-12 15:56:45,330 - __main__ - INFO - Test completed.
