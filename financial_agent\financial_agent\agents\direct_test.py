"""
Direct test of DataCollectionAgent from within the agents directory
"""
import asyncio
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the agent directly
from agents.data_agent import DataCollectionAgent

async def test_agent():
    print("Testing DataCollectionAgent...")
    agent = DataCollectionAgent()
    
    try:
        # Start the agent
        print("Starting agent...")
        await agent.start()
        
        # Test basic data fetch
        print("\nFetching AAPL data...")
        ohlcv = await agent.fetch_ohlcv(
            symbol="AAPL",
            interval="1d",
            period="1mo"
        )
        
        if ohlcv:
            print(f"Success! Got {len(ohlcv.timestamp)} data points for {ohlcv.symbol}")
            print(f"Sample data (first 5 close prices): {ohlcv.close[:5]}")
        else:
            print("Failed to fetch data")
            
    except Exception as e:
        print(f"Error: {e}")
        raise
    finally:
        await agent.stop()
        print("\nAgent stopped")

if __name__ == "__main__":
    asyncio.run(test_agent())
