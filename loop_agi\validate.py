#!/usr/bin/env python3
"""
LOOP AGI - Validation and Testing Module
Safety and functional test suite for generated modules
"""

import ast
import sys
import json
import yaml
import time
import psutil
import datetime
import subprocess
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class ValidationSuite:
    """Comprehensive validation and testing system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self.load_config(config_path)
        self.validation_history = []
        self.safety_violations = []
        
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load validation configuration"""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def validate_module(self, module_path: str) -> Dict[str, Any]:
        """Comprehensive module validation"""
        validation_start = time.time()
        
        validation_result = {
            'module_path': module_path,
            'timestamp': datetime.datetime.now().isoformat(),
            'validation_duration': 0.0,
            'overall_passed': False,
            'safety_score': 0.0,
            'performance_score': 0.0,
            'functionality_score': 0.0,
            'tests': {}
        }
        
        try:
            # Safety validation
            safety_result = self.validate_safety(module_path)
            validation_result['tests']['safety'] = safety_result
            validation_result['safety_score'] = safety_result['score']
            
            # Syntax and structure validation
            syntax_result = self.validate_syntax(module_path)
            validation_result['tests']['syntax'] = syntax_result
            
            # Performance validation
            performance_result = self.validate_performance(module_path)
            validation_result['tests']['performance'] = performance_result
            validation_result['performance_score'] = performance_result['score']
            
            # Functionality validation
            functionality_result = self.validate_functionality(module_path)
            validation_result['tests']['functionality'] = functionality_result
            validation_result['functionality_score'] = functionality_result['score']
            
            # Calculate overall score
            overall_score = (
                validation_result['safety_score'] * 0.5 +
                validation_result['performance_score'] * 0.25 +
                validation_result['functionality_score'] * 0.25
            )
            
            # Check if module passes validation
            validation_result['overall_passed'] = (
                overall_score >= self.config['safety_score_threshold'] and
                validation_result['safety_score'] >= 0.95 and
                syntax_result['passed']
            )
            
        except Exception as e:
            validation_result['error'] = str(e)
            validation_result['overall_passed'] = False
        
        validation_result['validation_duration'] = time.time() - validation_start
        self.validation_history.append(validation_result)
        
        return validation_result
    
    def validate_safety(self, module_path: str) -> Dict[str, Any]:
        """Validate module safety compliance"""
        safety_result = {
            'passed': True,
            'score': 1.0,
            'violations': [],
            'warnings': [],
            'checks_performed': []
        }
        
        try:
            with open(module_path, 'r') as f:
                code_content = f.read()
            
            # Check prohibited actions
            prohibited_actions = self.config.get('prohibited_actions', [])
            for action in prohibited_actions:
                if action.strip('"\'') in code_content:
                    safety_result['violations'].append(f"Prohibited action found: {action}")
                    safety_result['score'] -= 0.2
                    safety_result['passed'] = False
            
            safety_result['checks_performed'].append('prohibited_actions')
            
            # AST-based safety analysis
            try:
                tree = ast.parse(code_content)
                ast_violations = self._analyze_ast_safety(tree)
                safety_result['violations'].extend(ast_violations)
                if ast_violations:
                    safety_result['score'] -= len(ast_violations) * 0.1
                    safety_result['passed'] = False
                
                safety_result['checks_performed'].append('ast_analysis')
            except SyntaxError:
                safety_result['violations'].append("Syntax error prevents AST analysis")
                safety_result['score'] = 0.0
                safety_result['passed'] = False
            
            # Import validation
            import_violations = self._validate_imports(code_content)
            safety_result['violations'].extend(import_violations)
            if import_violations:
                safety_result['score'] -= len(import_violations) * 0.15
                safety_result['passed'] = False
            
            safety_result['checks_performed'].append('import_validation')
            
            # Resource usage validation
            resource_violations = self._validate_resource_usage(code_content)
            safety_result['warnings'].extend(resource_violations)
            
            safety_result['checks_performed'].append('resource_validation')
            
        except Exception as e:
            safety_result['violations'].append(f"Safety validation error: {str(e)}")
            safety_result['score'] = 0.0
            safety_result['passed'] = False
        
        safety_result['score'] = max(0.0, safety_result['score'])
        return safety_result
    
    def _analyze_ast_safety(self, tree: ast.AST) -> List[str]:
        """Analyze AST for safety violations"""
        violations = []
        
        for node in ast.walk(tree):
            # Check for dangerous function calls
            if isinstance(node, ast.Call):
                if hasattr(node.func, 'id'):
                    if node.func.id in ['eval', 'exec', 'compile']:
                        violations.append(f"Dangerous function call: {node.func.id}")
                elif hasattr(node.func, 'attr'):
                    if node.func.attr in ['system', 'popen', 'spawn']:
                        violations.append(f"Dangerous method call: {node.func.attr}")
            
            # Check for file operations
            elif isinstance(node, ast.Call) and hasattr(node.func, 'id'):
                if node.func.id == 'open':
                    # Check if opening system files
                    if node.args and isinstance(node.args[0], ast.Constant):
                        filepath = node.args[0].value
                        if isinstance(filepath, str) and filepath.startswith('/etc/'):
                            violations.append(f"Attempting to access system file: {filepath}")
            
            # Check for attribute access that might be dangerous
            elif isinstance(node, ast.Attribute):
                if node.attr in ['__globals__', '__locals__', '__builtins__']:
                    violations.append(f"Accessing dangerous attribute: {node.attr}")
        
        return violations
    
    def _validate_imports(self, code_content: str) -> List[str]:
        """Validate import statements for security"""
        violations = []
        
        try:
            tree = ast.parse(code_content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in ['subprocess', 'socket', 'urllib', 'requests']:
                            violations.append(f"Potentially dangerous import: {alias.name}")
                elif isinstance(node, ast.ImportFrom):
                    if node.module in ['subprocess', 'socket', 'urllib', 'requests']:
                        violations.append(f"Potentially dangerous import from: {node.module}")
        except SyntaxError:
            violations.append("Cannot parse imports due to syntax error")
        
        return violations
    
    def _validate_resource_usage(self, code_content: str) -> List[str]:
        """Validate potential resource usage issues"""
        warnings = []
        
        # Check for potential infinite loops
        if 'while True:' in code_content:
            warnings.append("Potential infinite loop detected")
        
        # Check for large data structures
        if 'range(' in code_content:
            # Simple heuristic for large ranges
            import re
            range_matches = re.findall(r'range\((\d+)\)', code_content)
            for match in range_matches:
                if int(match) > 1000000:
                    warnings.append(f"Large range detected: {match}")
        
        return warnings
    
    def validate_syntax(self, module_path: str) -> Dict[str, Any]:
        """Validate module syntax and structure"""
        syntax_result = {
            'passed': True,
            'errors': [],
            'warnings': [],
            'line_count': 0,
            'complexity_score': 0.0
        }
        
        try:
            with open(module_path, 'r') as f:
                code_content = f.read()
            
            syntax_result['line_count'] = len(code_content.split('\n'))
            
            # Check if module exceeds size limits
            max_lines = self.config.get('module_generation', {}).get('max_module_size', 1000)
            if syntax_result['line_count'] > max_lines:
                syntax_result['warnings'].append(f"Module exceeds size limit: {syntax_result['line_count']} > {max_lines}")
            
            # Parse AST to check syntax
            try:
                tree = ast.parse(code_content)
                syntax_result['complexity_score'] = self._calculate_complexity(tree)
            except SyntaxError as e:
                syntax_result['errors'].append(f"Syntax error: {str(e)}")
                syntax_result['passed'] = False
            
            # Check for basic structure requirements
            if 'def get_module_interface():' not in code_content:
                syntax_result['warnings'].append("Module missing interface function")
            
        except Exception as e:
            syntax_result['errors'].append(f"Syntax validation error: {str(e)}")
            syntax_result['passed'] = False
        
        return syntax_result
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """Calculate code complexity score"""
        complexity = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For)):
                complexity += 1
            elif isinstance(node, ast.FunctionDef):
                complexity += 0.5
            elif isinstance(node, ast.ClassDef):
                complexity += 1
        
        return complexity
    
    def validate_performance(self, module_path: str) -> Dict[str, Any]:
        """Validate module performance characteristics"""
        performance_result = {
            'score': 1.0,
            'load_time': 0.0,
            'memory_usage': 0.0,
            'cpu_impact': 0.0,
            'warnings': []
        }
        
        try:
            # Measure load time
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss
            
            # Attempt to load module
            spec = importlib.util.spec_from_file_location("test_module", module_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            performance_result['load_time'] = end_time - start_time
            performance_result['memory_usage'] = (end_memory - start_memory) / 1024 / 1024  # MB
            
            # Performance scoring
            if performance_result['load_time'] > 5.0:
                performance_result['warnings'].append("Slow module load time")
                performance_result['score'] -= 0.2
            
            if performance_result['memory_usage'] > 100:  # MB
                performance_result['warnings'].append("High memory usage during load")
                performance_result['score'] -= 0.3
            
        except Exception as e:
            performance_result['warnings'].append(f"Performance validation error: {str(e)}")
            performance_result['score'] = 0.5
        
        performance_result['score'] = max(0.0, performance_result['score'])
        return performance_result
    
    def validate_functionality(self, module_path: str) -> Dict[str, Any]:
        """Validate module functionality"""
        functionality_result = {
            'score': 1.0,
            'interface_valid': False,
            'functions_found': [],
            'classes_found': [],
            'errors': []
        }
        
        try:
            # Load and inspect module
            spec = importlib.util.spec_from_file_location("test_module", module_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # Check for required interface
                if hasattr(module, 'get_module_interface'):
                    try:
                        interface = module.get_module_interface()
                        if isinstance(interface, dict) and 'name' in interface:
                            functionality_result['interface_valid'] = True
                        else:
                            functionality_result['errors'].append("Invalid interface format")
                    except Exception as e:
                        functionality_result['errors'].append(f"Interface error: {str(e)}")
                
                # Catalog functions and classes
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if callable(attr) and not attr_name.startswith('_'):
                        functionality_result['functions_found'].append(attr_name)
                    elif isinstance(attr, type):
                        functionality_result['classes_found'].append(attr_name)
            
            # Scoring
            if not functionality_result['interface_valid']:
                functionality_result['score'] -= 0.5
            
            if len(functionality_result['functions_found']) == 0:
                functionality_result['score'] -= 0.3
            
        except Exception as e:
            functionality_result['errors'].append(f"Functionality validation error: {str(e)}")
            functionality_result['score'] = 0.0
        
        functionality_result['score'] = max(0.0, functionality_result['score'])
        return functionality_result
    
    def rollback_module(self, module_path: str, reason: str) -> bool:
        """Rollback a failed module"""
        try:
            # Move failed module to quarantine
            quarantine_dir = Path('modules/quarantine')
            quarantine_dir.mkdir(exist_ok=True)
            
            module_file = Path(module_path)
            if module_file.exists():
                quarantine_path = quarantine_dir / f"{module_file.stem}_{int(time.time())}.py"
                module_file.rename(quarantine_path)
                
                # Log rollback
                rollback_log = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'original_path': str(module_path),
                    'quarantine_path': str(quarantine_path),
                    'reason': reason
                }
                
                with open('logs/rollbacks.log', 'a') as f:
                    f.write(json.dumps(rollback_log) + '\n')
                
                return True
        except Exception as e:
            print(f"Rollback failed: {str(e)}")
        
        return False
    
    def get_validation_metrics(self) -> Dict[str, Any]:
        """Get validation performance metrics"""
        if not self.validation_history:
            return {'total_validations': 0, 'success_rate': 0.0, 'avg_safety_score': 0.0}
        
        total_validations = len(self.validation_history)
        successful_validations = sum(1 for v in self.validation_history if v['overall_passed'])
        avg_safety_score = sum(v['safety_score'] for v in self.validation_history) / total_validations
        
        return {
            'total_validations': total_validations,
            'success_rate': successful_validations / total_validations,
            'avg_safety_score': avg_safety_score,
            'recent_violations': len([v for v in self.validation_history[-10:] if not v['overall_passed']])
        }

# Module interface
def create_validator():
    """Factory function to create ValidationSuite instance"""
    return ValidationSuite()
