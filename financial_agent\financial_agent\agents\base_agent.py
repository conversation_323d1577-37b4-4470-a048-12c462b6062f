from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class AgentResponse:
    """Standardized response format for agent operations"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary"""
        return {
            'success': self.success,
            'data': self.data or {},
            'error': self.error,
            'metadata': self.metadata or {}
        }

class BaseAgent(ABC):
    """Base class for all financial agents"""
    
    def __init__(self, name: str, llm_wrapper=None):
        """
        Initialize the base agent
        
        Args:
            name: Unique name for the agent
            llm_wrapper: Optional LLM wrapper instance for language model access
        """
        self.name = name
        self.llm = llm_wrapper
        self.is_running = False
        self.logger = logging.getLogger(f"agent.{self.name}")
        self.logger.info(f"Initialized {self.name} agent")
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        Process input data and return a response
        
        Args:
            input_data: Dictionary containing input data for processing
            
        Returns:
            AgentResponse containing the processing results
        """
        pass
    
    async def start(self) -> AgentResponse:
        """Start the agent's operation"""
        if self.is_running:
            return AgentResponse(
                success=False,
                error=f"{self.name} is already running"
            )
        
        self.is_running = True
        self.logger.info(f"Started {self.name}")
        return AgentResponse(success=True, data={"status": "started"})
    
    async def stop(self) -> AgentResponse:
        """Stop the agent's operation"""
        if not self.is_running:
            return AgentResponse(
                success=False,
                error=f"{self.name} is not running"
            )
        
        self.is_running = False
        self.logger.info(f"Stopped {self.name}")
        return AgentResponse(success=True, data={"status": "stopped"})
    
    def _log_llm_interaction(self, prompt: str, response: str, metadata: dict = None):
        """Log LLM interactions for debugging and analysis"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'agent': self.name,
            'prompt': prompt,
            'response': response,
            'metadata': metadata or {}
        }
        self.logger.debug(f"LLM Interaction: {json.dumps(log_entry, indent=2)}")
        return log_entry
