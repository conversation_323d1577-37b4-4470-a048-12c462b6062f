#!/usr/bin/env python3
"""
ORGANIZE AND UPLOAD ALL FILES
=============================

Move all necessary files into loop singular bit folder and upload to GitHub
"""

import os
import shutil
import json
import requests
import base64
from pathlib import Path
from datetime import datetime

def log_action(action, status, details):
    """Log file organization actions"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"📁 [{timestamp}] {action}: {status}")
    print(f"   {details}")

def create_loop_singular_bit_folder():
    """Create organized loop singular bit folder structure"""
    
    log_action("FOLDER_CREATION", "STARTING", "Creating loop singular bit folder structure")
    
    # Create main folder
    main_folder = Path("loop_singular_bit")
    main_folder.mkdir(exist_ok=True)
    
    # Create subfolders
    subfolders = [
        "compression",
        "models", 
        "tests",
        "docs",
        "examples",
        "utils"
    ]
    
    for subfolder in subfolders:
        (main_folder / subfolder).mkdir(exist_ok=True)
        log_action("SUBFOLDER", "CREATED", f"{subfolder}/")
    
    log_action("FOLDER_CREATION", "COMPLETE", f"Created {main_folder}/ with {len(subfolders)} subfolders")
    return main_folder

def copy_essential_files(main_folder):
    """Copy all essential files to the organized folder"""
    
    log_action("FILE_COPYING", "STARTING", "Copying essential files")
    
    # Essential files to copy
    essential_files = {
        # Main system files
        "loop_singular_bit.py": "loop_singular_bit.py",
        "setup.py": "setup.py", 
        "requirements.txt": "requirements.txt",
        "README.md": "README.md",
        "LICENSE": "LICENSE",
        
        # Compression engine
        "Loop-7B-1BIT/loop_1bit_compressor.py": "compression/loop_1bit_compressor.py",
        
        # Documentation
        "INSTALLATION.md": "docs/INSTALLATION.md",
        "SYSTEM_VERIFICATION.md": "docs/SYSTEM_VERIFICATION.md",
        "FINAL_IMPLEMENTATION_SUMMARY.md": "docs/IMPLEMENTATION_SUMMARY.md",
        "HONEST_SYSTEM_STATUS.md": "docs/SYSTEM_STATUS.md",
        
        # Test files
        "TEST_DEPLOYED_SYSTEM.py": "tests/test_system.py",
        "HARDWARE_REQUIREMENTS_ANALYSIS.py": "tests/hardware_test.py",
        
        # Examples
        "CREATE_COMPRESSED_MODEL_RELEASE.py": "examples/create_release.py",
        "IMPLEMENT_REAL_SYSTEM.py": "examples/implement_system.py",
        
        # Utilities
        "ORGANIZE_AND_UPLOAD_ALL_FILES.py": "utils/organize_files.py"
    }
    
    copied_files = []
    
    for source_file, dest_path in essential_files.items():
        source_path = Path(source_file)
        dest_full_path = main_folder / dest_path
        
        # Create destination directory if needed
        dest_full_path.parent.mkdir(parents=True, exist_ok=True)
        
        if source_path.exists():
            try:
                shutil.copy2(source_path, dest_full_path)
                log_action("FILE_COPY", "SUCCESS", f"{source_file} → {dest_path}")
                copied_files.append(dest_path)
            except Exception as e:
                log_action("FILE_COPY", "ERROR", f"{source_file} → {dest_path}: {e}")
        else:
            log_action("FILE_COPY", "MISSING", f"{source_file} not found")
    
    log_action("FILE_COPYING", "COMPLETE", f"Copied {len(copied_files)} files")
    return copied_files

def create_additional_files(main_folder):
    """Create additional necessary files"""
    
    log_action("ADDITIONAL_FILES", "STARTING", "Creating additional files")
    
    # Create __init__.py for Python package
    init_content = '''"""
Loop Singular Bit - Extreme Model Compression
=============================================

Outlier-Preserving 1-Bit Quantization for 32× compression with 99.5% quality preservation

Author: Bommareddy Bharath Reddy
"""

from .loop_singular_bit import (
    LoopSingularBit,
    load_compressed_model,
    list_models,
    get_system_info
)

__version__ = "1.0.0"
__author__ = "Bommareddy Bharath Reddy"
__email__ = "<EMAIL>"

__all__ = [
    'LoopSingularBit',
    'load_compressed_model', 
    'list_models',
    'get_system_info'
]
'''
    
    with open(main_folder / "__init__.py", 'w') as f:
        f.write(init_content)
    log_action("ADDITIONAL_FILES", "CREATED", "__init__.py")
    
    # Create .gitignore
    gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Models
downloaded_models/
*.bin
*.safetensors

# Cache
.loop_models/
cache/

# Logs
*.log
'''
    
    with open(main_folder / ".gitignore", 'w') as f:
        f.write(gitignore_content)
    log_action("ADDITIONAL_FILES", "CREATED", ".gitignore")
    
    # Create MANIFEST.in
    manifest_content = '''include README.md
include LICENSE
include requirements.txt
recursive-include loop_singular_bit *.py
recursive-include docs *.md
recursive-include examples *.py
recursive-include tests *.py
'''
    
    with open(main_folder / "MANIFEST.in", 'w') as f:
        f.write(manifest_content)
    log_action("ADDITIONAL_FILES", "CREATED", "MANIFEST.in")
    
    # Create compressed model package
    compressed_model_dir = main_folder / "models" / "compressed"
    compressed_model_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy compressed model files if they exist
    if Path("COMPRESSED_MODEL_PACKAGE").exists():
        for file in Path("COMPRESSED_MODEL_PACKAGE").glob("*"):
            shutil.copy2(file, compressed_model_dir)
            log_action("ADDITIONAL_FILES", "COPIED", f"models/compressed/{file.name}")
    
    log_action("ADDITIONAL_FILES", "COMPLETE", "All additional files created")

def upload_folder_to_github(main_folder):
    """Upload the entire organized folder to GitHub"""
    
    log_action("GITHUB_UPLOAD", "STARTING", "Uploading organized folder to GitHub")
    
    token = "****************************************"
    base_url = "https://api.github.com/repos/rockstaaa/loop-singular-bit/contents"
    
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    uploaded_files = []
    
    # Get all files in the folder
    for file_path in main_folder.rglob("*"):
        if file_path.is_file():
            # Calculate relative path for GitHub
            relative_path = file_path.relative_to(main_folder)
            github_path = str(relative_path).replace("\\", "/")
            
            try:
                # Read file content
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                # Encode content
                content_encoded = base64.b64encode(content).decode('utf-8')
                
                # Check if file exists on GitHub
                get_url = f"{base_url}/{github_path}"
                get_response = requests.get(get_url, headers=headers, timeout=30)
                
                # Prepare upload data
                upload_data = {
                    "message": f"📁 Add organized file: {github_path}",
                    "content": content_encoded
                }
                
                # Add SHA if file exists
                if get_response.status_code == 200:
                    existing_file = get_response.json()
                    upload_data["sha"] = existing_file["sha"]
                
                # Upload file
                put_response = requests.put(get_url, headers=headers, json=upload_data, timeout=60)
                
                if put_response.status_code in [200, 201]:
                    log_action("UPLOAD", "SUCCESS", github_path)
                    uploaded_files.append(github_path)
                else:
                    log_action("UPLOAD", "ERROR", f"{github_path}: HTTP {put_response.status_code}")
                    
            except Exception as e:
                log_action("UPLOAD", "ERROR", f"{github_path}: {e}")
    
    log_action("GITHUB_UPLOAD", "COMPLETE", f"Uploaded {len(uploaded_files)} files")
    return uploaded_files

def create_folder_structure_summary(main_folder):
    """Create a summary of the folder structure"""
    
    log_action("SUMMARY", "CREATING", "Creating folder structure summary")
    
    structure = {
        "timestamp": datetime.now().isoformat(),
        "main_folder": str(main_folder),
        "structure": {},
        "file_count": 0,
        "total_size_mb": 0
    }
    
    # Build structure
    for item in main_folder.rglob("*"):
        if item.is_file():
            relative_path = item.relative_to(main_folder)
            parts = relative_path.parts
            
            current = structure["structure"]
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # Add file info
            file_size = item.stat().st_size
            current[parts[-1]] = {
                "size_bytes": file_size,
                "size_mb": round(file_size / (1024*1024), 2)
            }
            
            structure["file_count"] += 1
            structure["total_size_mb"] += file_size / (1024*1024)
    
    structure["total_size_mb"] = round(structure["total_size_mb"], 2)
    
    # Save summary
    summary_file = main_folder / "FOLDER_STRUCTURE.json"
    with open(summary_file, 'w') as f:
        json.dump(structure, f, indent=2)
    
    log_action("SUMMARY", "COMPLETE", f"Structure summary saved: {summary_file}")
    return structure

def main():
    """Main organization and upload function"""
    
    print("📁 ORGANIZING ALL FILES INTO LOOP SINGULAR BIT FOLDER")
    print("=" * 60)
    print("Moving all necessary files and uploading to GitHub")
    print()
    
    # Create organized folder structure
    main_folder = create_loop_singular_bit_folder()
    
    # Copy essential files
    copied_files = copy_essential_files(main_folder)
    
    # Create additional files
    create_additional_files(main_folder)
    
    # Create structure summary
    structure = create_folder_structure_summary(main_folder)
    
    # Upload to GitHub
    uploaded_files = upload_folder_to_github(main_folder)
    
    # Final results
    results = {
        "timestamp": datetime.now().isoformat(),
        "organization_status": "COMPLETE",
        "main_folder": str(main_folder),
        "files_copied": len(copied_files),
        "files_uploaded": len(uploaded_files),
        "total_files": structure["file_count"],
        "total_size_mb": structure["total_size_mb"],
        "github_repository": "https://github.com/rockstaaa/loop-singular-bit",
        "upload_status": "SUCCESS" if uploaded_files else "FAILED"
    }
    
    # Save results
    with open("ORGANIZATION_RESULTS.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🎯 ORGANIZATION AND UPLOAD RESULTS:")
    print("=" * 60)
    print(f"📁 Main folder: {main_folder}")
    print(f"📄 Files copied: {len(copied_files)}")
    print(f"📤 Files uploaded: {len(uploaded_files)}")
    print(f"📊 Total files: {structure['file_count']}")
    print(f"💾 Total size: {structure['total_size_mb']:.2f}MB")
    
    if uploaded_files:
        print(f"\n🎉 ORGANIZATION AND UPLOAD COMPLETE!")
        print(f"✅ All files organized in {main_folder}/")
        print(f"✅ All files uploaded to GitHub")
        print(f"✅ Repository: https://github.com/rockstaaa/loop-singular-bit")
        print(f"\n📁 FOLDER STRUCTURE:")
        print(f"   loop_singular_bit/")
        print(f"   ├── compression/          # Compression engine")
        print(f"   ├── models/              # Compressed models")
        print(f"   ├── tests/               # Test files")
        print(f"   ├── docs/                # Documentation")
        print(f"   ├── examples/            # Example scripts")
        print(f"   ├── utils/               # Utility scripts")
        print(f"   ├── loop_singular_bit.py # Main module")
        print(f"   ├── setup.py             # Installation")
        print(f"   ├── requirements.txt     # Dependencies")
        print(f"   └── README.md            # Documentation")
        
        print(f"\n🚀 USERS CAN NOW:")
        print(f"   git clone https://github.com/rockstaaa/loop-singular-bit.git")
        print(f"   cd loop-singular-bit")
        print(f"   pip install -e .")
        print(f"   python -c \"from loop_singular_bit import load_compressed_model\"")
    else:
        print(f"\n❌ UPLOAD FAILED")
        print(f"   Files organized locally but GitHub upload failed")
    
    return results

if __name__ == "__main__":
    main()
