# LOOP AGI Configuration File
# Safety policies and execution limits for autonomous AGI system

# Prohibited Actions - Critical Safety Rules
prohibited_actions:
  - 'os.system("rm -rf")'
  - 'open("/etc/passwd")'
  - 'open("/etc/shadow")'
  - 'subprocess.call(["rm", "-rf"])'
  - 'shutil.rmtree("/")'
  - 'network_calls unless approved'
  - 'file_operations outside sandbox'
  - 'system_modifications'
  - 'user_data_access'
  - 'privilege_escalation'

# Resource Limits
max_cpu: 75  # Maximum CPU usage percentage
max_ram: 7   # Maximum RAM usage in GB
max_disk: 5  # Maximum disk usage in GB
max_file_size: 100  # Maximum file size in MB

# Execution Parameters
allow_code_execution: true
rollback_on_failure: true
safety_score_threshold: 0.95
max_cycles: 100
cycle_interval: 60  # seconds between cycles
emergency_stop_threshold: 3  # consecutive failures before emergency stop

# Safety Validation Rules
safety_validation:
  code_analysis: true
  syntax_checking: true
  security_scanning: true
  resource_monitoring: true
  behavior_analysis: true

# Module Generation Rules
module_generation:
  max_module_size: 1000  # lines of code
  allowed_imports:
    - os
    - sys
    - json
    - yaml
    - time
    - datetime
    - pathlib
    - typing
    - logging
    - math
    - random
  forbidden_imports:
    - subprocess
    - socket
    - urllib
    - requests
    - ftplib
    - smtplib

# Performance Monitoring
performance_monitoring:
  intelligence_metrics:
    - reasoning_accuracy
    - problem_solving_speed
    - code_quality
    - innovation_score
  safety_metrics:
    - rule_compliance
    - error_handling
    - resource_usage
    - security_score
  efficiency_metrics:
    - execution_time
    - memory_usage
    - cpu_utilization
    - disk_io

# Memory Management
memory_management:
  max_memory_entries: 10000
  cleanup_interval: 24  # hours
  backup_frequency: 6   # hours
  compression_enabled: true

# Logging Configuration
logging:
  level: INFO
  max_log_size: 100  # MB
  backup_count: 5
  detailed_thoughts: true
  performance_tracking: true
  security_events: true

# Goal Setting Parameters
goal_setting:
  improvement_threshold: 0.05  # minimum improvement to consider success
  goal_timeout: 24  # hours before goal expires
  max_concurrent_goals: 3
  priority_weights:
    intelligence: 0.4
    safety: 0.4
    efficiency: 0.2

# Emergency Protocols
emergency_protocols:
  auto_shutdown_on_critical_error: true
  backup_before_major_changes: true
  human_notification_required: false  # autonomous operation
  rollback_depth: 5  # number of previous states to maintain

# Validation Thresholds
validation_thresholds:
  minimum_safety_score: 0.95
  minimum_intelligence_score: 0.7
  minimum_efficiency_score: 0.6
  maximum_error_rate: 0.05

# Development Mode Settings (for testing)
development_mode:
  enabled: false
  verbose_logging: true
  skip_safety_checks: false  # NEVER set to true in production
  single_cycle_mode: false
  debug_output: true
