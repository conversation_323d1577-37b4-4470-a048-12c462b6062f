#!/usr/bin/env python3
"""
Performance Benchmarking for Loop 7B 1-BIT
==========================================

Comprehensive benchmarking suite for measuring:
- RAM usage during inference
- Compression ratios
- Inference speed
- Text generation quality
- Memory efficiency

Based on real test results from Loop 7B 1-BIT system.
"""

import os
import time
import psutil
import json
from typing import Dict, Any, List
from loop_1bit_compressor import Loop1BitCompressor

class PerformanceBenchmarker:
    """Comprehensive performance benchmarking for Loop 1-BIT"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.results = {
            'timestamp': time.time(),
            'benchmarks': {},
            'system_info': self._get_system_info()
        }
        
        print("📊 Loop 7B 1-BIT Performance Benchmarker")
        print("=" * 50)
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            'total_ram_gb': psutil.virtual_memory().total / (1024**3),
            'available_ram_gb': psutil.virtual_memory().available / (1024**3),
            'cpu_count': psutil.cpu_count(),
            'platform': os.name
        }
    
    def benchmark_ram_usage(self) -> Dict[str, Any]:
        """Benchmark RAM usage during compression and inference"""
        
        print("\n💾 BENCHMARKING RAM USAGE")
        print("=" * 40)
        
        start_time = time.time()
        baseline_ram = psutil.Process().memory_info().rss / (1024**2)
        
        # Initialize compressor
        compressor = Loop1BitCompressor(self.model_path)
        init_ram = psutil.Process().memory_info().rss / (1024**2)
        
        # Load tokenizer
        compressor.load_tokenizer()
        tokenizer_ram = psutil.Process().memory_info().rss / (1024**2)
        
        # Compress model
        compression_result = compressor.compress_model()
        compression_ram = psutil.Process().memory_info().rss / (1024**2)
        
        # Test inference
        response = compressor.generate("Test prompt for benchmarking", max_tokens=20)
        inference_ram = psutil.Process().memory_info().rss / (1024**2)
        
        end_time = time.time()
        
        result = {
            'baseline_ram_mb': baseline_ram,
            'init_ram_mb': init_ram,
            'tokenizer_ram_mb': tokenizer_ram,
            'compression_ram_mb': compression_ram,
            'inference_ram_mb': inference_ram,
            'peak_ram_mb': max(init_ram, tokenizer_ram, compression_ram, inference_ram),
            'total_ram_usage_mb': inference_ram - baseline_ram,
            'benchmark_time_s': end_time - start_time,
            'compression_stats': compression_result
        }
        
        print(f"✅ RAM Benchmark Results:")
        print(f"   Peak RAM usage: {result['peak_ram_mb']:.1f}MB")
        print(f"   Total RAM for inference: {result['total_ram_usage_mb']:.1f}MB")
        print(f"   Compression ratio: {compression_result.get('overall_compression_ratio', 0):.1f}×")
        
        return result
    
    def benchmark_inference_speed(self) -> Dict[str, Any]:
        """Benchmark inference speed with different prompt lengths"""
        
        print("\n⚡ BENCHMARKING INFERENCE SPEED")
        print("=" * 40)
        
        compressor = Loop1BitCompressor(self.model_path)
        compressor.load_tokenizer()
        compressor.compress_model()
        
        test_prompts = [
            ("Short", "Hello"),
            ("Medium", "Explain artificial intelligence in simple terms"),
            ("Long", "Write a detailed explanation of how neural networks work, including the mathematical foundations and practical applications in modern AI systems")
        ]
        
        speed_results = []
        
        for prompt_type, prompt in test_prompts:
            print(f"\n🧪 Testing {prompt_type} prompt...")
            
            start_time = time.time()
            start_ram = psutil.Process().memory_info().rss / (1024**2)
            
            response = compressor.generate(prompt, max_tokens=50)
            
            end_time = time.time()
            end_ram = psutil.Process().memory_info().rss / (1024**2)
            
            inference_time = end_time - start_time
            tokens_generated = len(response.split())
            tokens_per_second = tokens_generated / inference_time if inference_time > 0 else 0
            
            result = {
                'prompt_type': prompt_type,
                'prompt_length': len(prompt),
                'inference_time_s': inference_time,
                'tokens_generated': tokens_generated,
                'tokens_per_second': tokens_per_second,
                'ram_usage_mb': end_ram - start_ram,
                'response_preview': response[:100] + "..." if len(response) > 100 else response
            }
            
            speed_results.append(result)
            
            print(f"   ⏱️ Time: {inference_time:.2f}s")
            print(f"   🔤 Tokens/sec: {tokens_per_second:.1f}")
            print(f"   💾 RAM: {result['ram_usage_mb']:.1f}MB")
        
        return {
            'speed_tests': speed_results,
            'average_tokens_per_second': sum(r['tokens_per_second'] for r in speed_results) / len(speed_results),
            'average_inference_time': sum(r['inference_time_s'] for r in speed_results) / len(speed_results)
        }
    
    def benchmark_compression_consistency(self) -> Dict[str, Any]:
        """Benchmark compression consistency across different weight types"""
        
        print("\n🔄 BENCHMARKING COMPRESSION CONSISTENCY")
        print("=" * 40)
        
        compressor = Loop1BitCompressor(self.model_path)
        
        # Test compression on different weight types
        weight_types = [
            "model.embed_tokens.weight",           # Embedding
            "model.layers.0.self_attn.q_proj.weight",  # Attention
            "model.layers.0.mlp.gate_proj.weight",     # MLP
            "model.layers.15.self_attn.k_proj.weight", # Mid-layer
            "model.layers.31.mlp.down_proj.weight",    # Final layer
            "lm_head.weight"                            # Output
        ]
        
        compression_result = compressor.compress_model(sample_weights=weight_types)
        
        # Analyze compression consistency
        if compression_result['success']:
            ratios = [r['compression_ratio'] for r in compression_result['individual_results']]
            
            consistency_stats = {
                'compression_ratios': ratios,
                'min_ratio': min(ratios),
                'max_ratio': max(ratios),
                'avg_ratio': sum(ratios) / len(ratios),
                'ratio_std': (sum((r - sum(ratios)/len(ratios))**2 for r in ratios) / len(ratios))**0.5,
                'consistency_score': 1.0 - (max(ratios) - min(ratios)) / max(ratios),
                'weight_types_tested': len(weight_types),
                'total_compression_time': compression_result['compression_time_s']
            }
            
            print(f"✅ Compression Consistency Results:")
            print(f"   Average ratio: {consistency_stats['avg_ratio']:.1f}×")
            print(f"   Range: {consistency_stats['min_ratio']:.1f}× - {consistency_stats['max_ratio']:.1f}×")
            print(f"   Consistency score: {consistency_stats['consistency_score']:.3f}")
            
            return consistency_stats
        else:
            return {'error': 'Compression failed'}
    
    def benchmark_memory_efficiency(self) -> Dict[str, Any]:
        """Benchmark memory efficiency during different operations"""
        
        print("\n🧠 BENCHMARKING MEMORY EFFICIENCY")
        print("=" * 40)
        
        memory_timeline = []
        
        def track_memory(phase: str):
            ram_mb = psutil.Process().memory_info().rss / (1024**2)
            memory_timeline.append({'phase': phase, 'ram_mb': ram_mb, 'timestamp': time.time()})
            return ram_mb
        
        # Baseline
        track_memory("baseline")
        
        # Initialize
        compressor = Loop1BitCompressor(self.model_path)
        track_memory("initialized")
        
        # Load tokenizer
        compressor.load_tokenizer()
        track_memory("tokenizer_loaded")
        
        # Compress model
        compressor.compress_model()
        track_memory("model_compressed")
        
        # Multiple inferences
        for i in range(3):
            compressor.generate(f"Test prompt {i+1}", max_tokens=10)
            track_memory(f"inference_{i+1}")
        
        # Calculate efficiency metrics
        peak_memory = max(m['ram_mb'] for m in memory_timeline)
        baseline_memory = memory_timeline[0]['ram_mb']
        working_memory = peak_memory - baseline_memory
        
        efficiency_result = {
            'memory_timeline': memory_timeline,
            'peak_memory_mb': peak_memory,
            'baseline_memory_mb': baseline_memory,
            'working_memory_mb': working_memory,
            'memory_efficiency_score': 1000 / working_memory if working_memory > 0 else 0,  # Higher is better
            'stable_memory_usage': len(set(round(m['ram_mb']) for m in memory_timeline[-3:])) == 1  # Last 3 measurements same
        }
        
        print(f"✅ Memory Efficiency Results:")
        print(f"   Peak memory: {peak_memory:.1f}MB")
        print(f"   Working memory: {working_memory:.1f}MB")
        print(f"   Efficiency score: {efficiency_result['memory_efficiency_score']:.1f}")
        print(f"   Stable usage: {efficiency_result['stable_memory_usage']}")
        
        return efficiency_result
    
    def run_full_benchmark(self) -> Dict[str, Any]:
        """Run complete benchmark suite"""
        
        print("🚀🚀🚀 LOOP 7B 1-BIT FULL BENCHMARK SUITE 🚀🚀🚀")
        print("=" * 70)
        print("📊 Running comprehensive performance benchmarks...")
        print()
        
        start_time = time.time()
        
        # Run all benchmarks
        self.results['benchmarks']['ram_usage'] = self.benchmark_ram_usage()
        self.results['benchmarks']['inference_speed'] = self.benchmark_inference_speed()
        self.results['benchmarks']['compression_consistency'] = self.benchmark_compression_consistency()
        self.results['benchmarks']['memory_efficiency'] = self.benchmark_memory_efficiency()
        
        end_time = time.time()
        self.results['total_benchmark_time_s'] = end_time - start_time
        
        # Generate summary
        summary = self._generate_benchmark_summary()
        self.results['summary'] = summary
        
        print(f"\n🏁 BENCHMARK SUITE COMPLETE!")
        print(f"=" * 50)
        print(f"⏱️ Total time: {self.results['total_benchmark_time_s']:.1f}s")
        print(f"📊 Peak RAM: {summary['peak_ram_mb']:.1f}MB")
        print(f"🔄 Avg compression: {summary['avg_compression_ratio']:.1f}×")
        print(f"⚡ Avg speed: {summary['avg_tokens_per_second']:.1f} tokens/sec")
        
        return self.results
    
    def _generate_benchmark_summary(self) -> Dict[str, Any]:
        """Generate benchmark summary"""
        
        benchmarks = self.results['benchmarks']
        
        summary = {
            'peak_ram_mb': benchmarks['ram_usage']['peak_ram_mb'],
            'total_ram_usage_mb': benchmarks['ram_usage']['total_ram_usage_mb'],
            'avg_compression_ratio': benchmarks['compression_consistency']['avg_ratio'],
            'avg_tokens_per_second': benchmarks['inference_speed']['average_tokens_per_second'],
            'memory_efficiency_score': benchmarks['memory_efficiency']['memory_efficiency_score'],
            'target_300mb_achieved': benchmarks['ram_usage']['total_ram_usage_mb'] <= 300,
            'performance_grade': self._calculate_performance_grade(benchmarks)
        }
        
        return summary
    
    def _calculate_performance_grade(self, benchmarks: Dict[str, Any]) -> str:
        """Calculate overall performance grade"""
        
        ram_score = 100 if benchmarks['ram_usage']['total_ram_usage_mb'] <= 300 else max(0, 100 - (benchmarks['ram_usage']['total_ram_usage_mb'] - 300) / 10)
        compression_score = min(100, benchmarks['compression_consistency']['avg_ratio'] * 3)
        speed_score = min(100, benchmarks['inference_speed']['average_tokens_per_second'] * 10)
        
        overall_score = (ram_score + compression_score + speed_score) / 3
        
        if overall_score >= 90:
            return "A+"
        elif overall_score >= 80:
            return "A"
        elif overall_score >= 70:
            return "B+"
        elif overall_score >= 60:
            return "B"
        else:
            return "C"
    
    def save_results(self, filename: str = None) -> str:
        """Save benchmark results to file"""
        
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"loop_1bit_benchmark_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"💾 Benchmark results saved to {filename}")
        return filename

def main():
    """Run performance benchmarks"""
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    benchmarker = PerformanceBenchmarker(model_path)
    results = benchmarker.run_full_benchmark()
    
    # Save results
    results_file = benchmarker.save_results()
    
    # Display final summary
    summary = results['summary']
    print(f"\n📋 FINAL PERFORMANCE SUMMARY:")
    print(f"   Performance Grade: {summary['performance_grade']}")
    print(f"   RAM Usage: {summary['total_ram_usage_mb']:.1f}MB")
    print(f"   300MB Target: {'✅ ACHIEVED' if summary['target_300mb_achieved'] else '❌ NOT ACHIEVED'}")
    print(f"   Compression: {summary['avg_compression_ratio']:.1f}×")
    print(f"   Speed: {summary['avg_tokens_per_second']:.1f} tokens/sec")

if __name__ == "__main__":
    main()
