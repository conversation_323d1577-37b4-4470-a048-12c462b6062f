"""
Data Collection Agent Demo
-------------------------
This script demonstrates the capabilities of the DataCollectionAgent using yfinance.
It shows how to:
1. Initialize the agent
2. Fetch market data for multiple symbols
3. Display market information
4. Generate price charts
"""

import asyncio
import logging
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_collection_demo.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from financial_agent.agents.data_agent import DataCollectionAgent

# Import the mock MistralWrapper
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

async def fetch_market_data(agent, symbols, interval="1d", period="1mo"):
    """Fetch and display market data for the given symbols"""
    logger.info("="*50)
    logger.info(f"FETCHING {interval.upper()} DATA FOR {', '.join(symbols)}")
    logger.info(f"Time period: {period if period else 'custom date range'}")
    logger.info("="*50)
    
    results = {}
    for symbol in symbols:
        logger.info(f"\nFetching {symbol} data...")
        try:
            ohlcv = await agent.fetch_ohlcv(
                symbol=symbol,
                interval=interval,
                period=period
            )
            
            if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
                results[symbol] = ohlcv
                logger.info(f"  Data points: {len(ohlcv.timestamp)}")
                logger.info(f"  Date range: {datetime.fromtimestamp(ohlcv.timestamp[0]).strftime('%Y-%m-%d')} to {datetime.fromtimestamp(ohlcv.timestamp[-1]).strftime('%Y-%m-%d')}")
                logger.info(f"  Latest close: ${ohlcv.close[-1]:.2f}")
                logger.info(f"  Volume: {ohlcv.volume[-1]:,}")
            else:
                logger.warning(f"  No data returned for {symbol}")
        except Exception as e:
            logger.error(f"  Error fetching data for {symbol}: {str(e)}", exc_info=True)
    
    return results

def plot_price_chart(data, title="Price Chart"):
    """Plot price chart for the given OHLCV data"""
    plt.figure(figsize=(12, 6))
    
    for symbol, ohlcv in data.items():
        dates = [datetime.fromtimestamp(ts) for ts in ohlcv.timestamp]
        plt.plot(dates, ohlcv.close, label=symbol, linewidth=2)
    
    plt.title(title)
    plt.xlabel("Date")
    plt.ylabel("Price ($)")
    plt.legend()
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

async def main():
    # Initialize the DataCollectionAgent with a mock LLM wrapper
    logger.info("Initializing DataCollectionAgent with mock LLM...")
    try:
        mock_llm = MistralWrapper()
        agent = DataCollectionAgent(llm_wrapper=mock_llm)
        
        # Start the agent
        logger.info("Starting agent...")
        await agent.start()
        
        # Verify agent is running
        if not agent.is_running:
            raise RuntimeError("Failed to start DataCollectionAgent")
        
        # Example 1: Basic data fetching
        symbols = ["AAPL", "MSFT", "GOOGL"]
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 1: FETCHING DAILY DATA")
        logger.info("="*50)
        
        try:
            daily_data = await fetch_market_data(agent, symbols, interval="1d", period="1mo")
            
            if daily_data:
                # Plot the price chart
                logger.info("Generating price chart...")
                plot_price_chart(daily_data, "Daily Closing Prices (1 Month)")
            else:
                logger.warning("No data returned for any symbols")
        except Exception as e:
            logger.error(f"Error in Example 1: {str(e)}", exc_info=True)
        
        # Example 2: Check market hours
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 2: MARKET HOURS INFORMATION")
        logger.info("="*50)
        try:
            market_open, market_close = agent.get_market_hours()
            logger.info(f"  Market Open: {datetime.fromtimestamp(market_open).strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  Market Close: {datetime.fromtimestamp(market_close).strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  Market is currently: {'OPEN' if agent.is_market_open() else 'CLOSED'}")
        except Exception as e:
            logger.error(f"Error getting market hours: {str(e)}", exc_info=True)
        
        # Example 3: Fetch intraday data
        logger.info("\n" + "="*50)
        logger.info("EXAMPLE 3: INTRADAY DATA")
        logger.info("="*50)
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3)
            
            logger.info(f"\nFetching hourly data for AAPL from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}...")
            aapl_data = await agent.fetch_ohlcv(
                symbol="AAPL",
                interval="1h",
                start=start_date.strftime("%Y-%m-%d"),
                end=end_date.strftime("%Y-%m-%d")
            )
            
            if aapl_data and hasattr(aapl_data, 'timestamp') and aapl_data.timestamp:
                # Plot intraday chart
                logger.info("Generating intraday price chart...")
                dates = [datetime.fromtimestamp(ts) for ts in aapl_data.timestamp]
                plt.figure(figsize=(14, 6))
                plt.plot(dates, aapl_data.close, 'b-', linewidth=2, label='AAPL (1h)')
                plt.title("AAPL Intraday Price Movement")
                plt.xlabel("Time")
                plt.ylabel("Price ($)")
                plt.grid(True)
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.show()
            else:
                logger.warning("No intraday data returned for AAPL")
        except Exception as e:
            logger.error(f"Error in Example 3: {str(e)}", exc_info=True)
        
        logger.info("\nDemo completed successfully!")
        
    except Exception as e:
        logger.error(f"\nAn error occurred in main: {str(e)}", exc_info=True)
    finally:
        # Always stop the agent when done
        try:
            if 'agent' in locals() and agent is not None:
                logger.info("Stopping DataCollectionAgent...")
                await agent.stop()
                logger.info("DataCollectionAgent has been stopped.")
        except Exception as e:
            logger.error(f"Error stopping agent: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
