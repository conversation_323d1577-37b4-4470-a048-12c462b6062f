#!/usr/bin/env python3
"""
REAL HARDWARE REQUIREMENTS TEST - 1-BIT QUANTIZATION
====================================================

Measure ACTUAL hardware requirements after 1-bit quantization.
Test on real Mistral 7B model with real measurements.

100% REAL RESULTS - NO SIMULATION OR FAKE DATA
"""

import os
import torch
import gc
import psutil
import time
import json
import platform
import subprocess
from typing import Dict, Any, List
from transformers import AutoTokenizer, AutoConfig
from safetensors import safe_open
import torch.nn.functional as F

class RealHardwareRequirementsTest:
    """Test actual hardware requirements for 1-bit quantized Mistral 7B"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.system_info = {}
        self.baseline_measurements = {}
        self.quantized_measurements = {}
        
        print("🔬 REAL HARDWARE REQUIREMENTS TEST")
        print("=" * 50)
        print("⚠️  100% REAL MEASUREMENTS - NO SIMULATION")
        print("🎯 Testing 1-bit quantized Mistral 7B hardware needs")
        print(f"📁 Model: {model_path}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get real system information"""
        
        print("\n💻 COLLECTING REAL SYSTEM INFORMATION")
        print("=" * 50)
        
        # Basic system info
        system_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'python_version': platform.python_version(),
            'pytorch_version': torch.__version__
        }
        
        # Memory info
        memory = psutil.virtual_memory()
        system_info.update({
            'total_ram_gb': memory.total / (1024**3),
            'available_ram_gb': memory.available / (1024**3),
            'used_ram_gb': memory.used / (1024**3),
            'ram_percent_used': memory.percent
        })
        
        # CPU info
        system_info.update({
            'cpu_count_logical': psutil.cpu_count(logical=True),
            'cpu_count_physical': psutil.cpu_count(logical=False),
            'cpu_freq_current': psutil.cpu_freq().current if psutil.cpu_freq() else 'Unknown',
            'cpu_freq_max': psutil.cpu_freq().max if psutil.cpu_freq() else 'Unknown'
        })
        
        # Disk info
        disk = psutil.disk_usage('.')
        system_info.update({
            'disk_total_gb': disk.total / (1024**3),
            'disk_free_gb': disk.free / (1024**3),
            'disk_used_gb': disk.used / (1024**3)
        })
        
        # GPU info (if available)
        try:
            if torch.cuda.is_available():
                system_info.update({
                    'gpu_available': True,
                    'gpu_count': torch.cuda.device_count(),
                    'gpu_name': torch.cuda.get_device_name(0),
                    'gpu_memory_gb': torch.cuda.get_device_properties(0).total_memory / (1024**3)
                })
            else:
                system_info.update({
                    'gpu_available': False,
                    'gpu_count': 0
                })
        except:
            system_info.update({
                'gpu_available': False,
                'gpu_count': 0,
                'gpu_error': 'Could not detect GPU'
            })
        
        # Model file size
        model_size_bytes = 0
        for root, dirs, files in os.walk(self.model_path):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.exists(file_path):
                    model_size_bytes += os.path.getsize(file_path)
        
        system_info['model_size_gb'] = model_size_bytes / (1024**3)
        
        print(f"✅ Platform: {system_info['platform']}")
        print(f"✅ CPU: {system_info['processor']}")
        print(f"✅ RAM: {system_info['total_ram_gb']:.1f}GB total, {system_info['available_ram_gb']:.1f}GB available")
        print(f"✅ CPU Cores: {system_info['cpu_count_physical']} physical, {system_info['cpu_count_logical']} logical")
        print(f"✅ GPU: {'Available' if system_info['gpu_available'] else 'Not Available'}")
        if system_info['gpu_available']:
            print(f"✅ GPU: {system_info['gpu_name']} ({system_info['gpu_memory_gb']:.1f}GB)")
        print(f"✅ Model Size: {system_info['model_size_gb']:.2f}GB")
        
        self.system_info = system_info
        return system_info
    
    def measure_baseline_requirements(self) -> Dict[str, Any]:
        """Measure baseline system requirements before quantization"""
        
        print(f"\n📊 MEASURING BASELINE REQUIREMENTS")
        print("=" * 50)
        
        start_time = time.time()
        
        # Baseline memory
        baseline_memory = psutil.Process().memory_info().rss / (1024**2)
        baseline_cpu = psutil.cpu_percent(interval=1)
        
        print(f"💾 Baseline RAM: {baseline_memory:.1f}MB")
        print(f"🔧 Baseline CPU: {baseline_cpu:.1f}%")
        
        # Load tokenizer and config (lightweight)
        print("📥 Loading tokenizer and config...")
        tokenizer_start = time.time()
        tokenizer_start_memory = psutil.Process().memory_info().rss / (1024**2)
        
        tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        config = AutoConfig.from_pretrained(self.model_path)
        
        tokenizer_end = time.time()
        tokenizer_end_memory = psutil.Process().memory_info().rss / (1024**2)
        tokenizer_cpu = psutil.cpu_percent(interval=0.1)
        
        print(f"✅ Tokenizer loaded: {tokenizer_end - tokenizer_start:.1f}s")
        print(f"💾 Tokenizer RAM: {tokenizer_end_memory - tokenizer_start_memory:.1f}MB")
        
        baseline_measurements = {
            'baseline_ram_mb': baseline_memory,
            'baseline_cpu_percent': baseline_cpu,
            'tokenizer_load_time_s': tokenizer_end - tokenizer_start,
            'tokenizer_ram_mb': tokenizer_end_memory - tokenizer_start_memory,
            'tokenizer_cpu_percent': tokenizer_cpu,
            'total_baseline_ram_mb': tokenizer_end_memory,
            'config_layers': config.num_hidden_layers,
            'config_hidden_size': config.hidden_size,
            'config_vocab_size': config.vocab_size
        }
        
        print(f"📊 Total baseline RAM: {baseline_measurements['total_baseline_ram_mb']:.1f}MB")
        
        self.baseline_measurements = baseline_measurements
        return baseline_measurements
    
    def quantize_and_measure_requirements(self, num_layers: int = 5) -> Dict[str, Any]:
        """Quantize layers and measure real hardware requirements"""
        
        print(f"\n🔄 QUANTIZING {num_layers} LAYERS AND MEASURING REQUIREMENTS")
        print("=" * 50)
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / (1024**2)
        start_cpu = psutil.cpu_percent(interval=0.1)
        
        # Load model index
        index_path = os.path.join(self.model_path, "model.safetensors.index.json")
        with open(index_path, 'r') as f:
            index = json.load(f)
        
        # Select layers to quantize
        layers_to_test = [
            "model.embed_tokens.weight",
            "model.layers.0.self_attn.q_proj.weight",
            "model.layers.0.mlp.gate_proj.weight",
            "model.layers.15.self_attn.q_proj.weight",
            "lm_head.weight"
        ][:num_layers]
        
        quantization_results = []
        total_parameters = 0
        total_original_size_mb = 0
        total_quantized_size_mb = 0
        peak_memory = start_memory
        
        for i, layer_name in enumerate(layers_to_test):
            if layer_name not in index['weight_map']:
                continue
            
            print(f"\n📥 [{i+1}/{len(layers_to_test)}] Processing {layer_name}")
            
            layer_start_time = time.time()
            layer_start_memory = psutil.Process().memory_info().rss / (1024**2)
            layer_start_cpu = psutil.cpu_percent(interval=0.1)
            
            file_name = index['weight_map'][layer_name]
            file_path = os.path.join(self.model_path, file_name)
            
            # Load weight
            with safe_open(file_path, framework="pt", device="cpu") as f:
                weight_tensor = f.get_tensor(layer_name)
                
                load_memory = psutil.Process().memory_info().rss / (1024**2)
                
                # Convert to float32 if needed
                if weight_tensor.dtype != torch.float32:
                    weight_tensor = weight_tensor.to(torch.float32)
                
                convert_memory = psutil.Process().memory_info().rss / (1024**2)
                
                # 1-bit quantization
                scale = torch.mean(torch.abs(weight_tensor))
                quantized_signs = torch.sign(weight_tensor).to(torch.int8)
                
                quantize_memory = psutil.Process().memory_info().rss / (1024**2)
                
                # Calculate sizes
                original_size_mb = weight_tensor.numel() * 4 / (1024**2)
                quantized_size_mb = (weight_tensor.numel() / 8 + 4) / (1024**2)
                compression_ratio = original_size_mb / quantized_size_mb
                
                # Test reconstruction
                reconstructed = quantized_signs.to(torch.float32) * scale
                mse_error = torch.mean((weight_tensor - reconstructed) ** 2).item()
                
                reconstruct_memory = psutil.Process().memory_info().rss / (1024**2)
                
                # Test basic operation
                if len(weight_tensor.shape) == 2:
                    test_input = torch.randn(2, weight_tensor.shape[1])
                    original_output = torch.matmul(test_input, weight_tensor.T)
                    quantized_output = torch.matmul(test_input, reconstructed.T)
                    operation_error = torch.mean((original_output - quantized_output) ** 2).item()
                    operation_success = True
                else:
                    operation_error = 0
                    operation_success = False
                
                operation_memory = psutil.Process().memory_info().rss / (1024**2)
                
                # Clean up
                del weight_tensor, quantized_signs, reconstructed
                if 'test_input' in locals():
                    del test_input, original_output, quantized_output
                gc.collect()
                
                cleanup_memory = psutil.Process().memory_info().rss / (1024**2)
                
                layer_end_time = time.time()
                layer_end_cpu = psutil.cpu_percent(interval=0.1)
                
                # Track peak memory
                peak_memory = max(peak_memory, load_memory, convert_memory, 
                                quantize_memory, reconstruct_memory, operation_memory)
                
                # Store results
                result = {
                    'layer_name': layer_name,
                    'shape': list(weight_tensor.shape) if 'weight_tensor' in locals() else 'unknown',
                    'parameters': weight_tensor.numel() if 'weight_tensor' in locals() else 0,
                    'processing_time_s': layer_end_time - layer_start_time,
                    'memory_progression': {
                        'start_mb': layer_start_memory,
                        'after_load_mb': load_memory,
                        'after_convert_mb': convert_memory,
                        'after_quantize_mb': quantize_memory,
                        'after_reconstruct_mb': reconstruct_memory,
                        'after_operation_mb': operation_memory,
                        'after_cleanup_mb': cleanup_memory,
                        'peak_mb': max(load_memory, convert_memory, quantize_memory, 
                                     reconstruct_memory, operation_memory)
                    },
                    'cpu_usage': {
                        'start_percent': layer_start_cpu,
                        'end_percent': layer_end_cpu
                    },
                    'compression': {
                        'original_size_mb': original_size_mb,
                        'quantized_size_mb': quantized_size_mb,
                        'compression_ratio': compression_ratio
                    },
                    'quality': {
                        'mse_error': mse_error,
                        'operation_error': operation_error,
                        'operation_success': operation_success
                    }
                }
                
                quantization_results.append(result)
                
                total_parameters += result['parameters']
                total_original_size_mb += original_size_mb
                total_quantized_size_mb += quantized_size_mb
                
                print(f"   ✅ Time: {result['processing_time_s']:.1f}s")
                print(f"   ✅ Peak RAM: {result['memory_progression']['peak_mb']:.1f}MB")
                print(f"   ✅ Compression: {compression_ratio:.1f}×")
                print(f"   ✅ MSE Error: {mse_error:.6f}")
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / (1024**2)
        end_cpu = psutil.cpu_percent(interval=0.1)
        
        overall_compression = total_original_size_mb / total_quantized_size_mb if total_quantized_size_mb > 0 else 0
        
        quantized_measurements = {
            'total_processing_time_s': end_time - start_time,
            'memory_usage': {
                'start_mb': start_memory,
                'end_mb': end_memory,
                'peak_mb': peak_memory,
                'total_used_mb': peak_memory - start_memory
            },
            'cpu_usage': {
                'start_percent': start_cpu,
                'end_percent': end_cpu,
                'average_percent': (start_cpu + end_cpu) / 2
            },
            'layers_processed': len(quantization_results),
            'total_parameters': total_parameters,
            'compression_stats': {
                'total_original_size_mb': total_original_size_mb,
                'total_quantized_size_mb': total_quantized_size_mb,
                'overall_compression_ratio': overall_compression,
                'storage_savings_mb': total_original_size_mb - total_quantized_size_mb,
                'storage_savings_percent': ((total_original_size_mb - total_quantized_size_mb) / total_original_size_mb * 100) if total_original_size_mb > 0 else 0
            },
            'individual_results': quantization_results
        }
        
        print(f"\n✅ QUANTIZATION MEASUREMENTS COMPLETE")
        print(f"📊 Total time: {quantized_measurements['total_processing_time_s']:.1f}s")
        print(f"📊 Peak RAM: {quantized_measurements['memory_usage']['peak_mb']:.1f}MB")
        print(f"📊 RAM used: {quantized_measurements['memory_usage']['total_used_mb']:.1f}MB")
        print(f"📊 Overall compression: {overall_compression:.1f}×")
        print(f"📊 Storage savings: {quantized_measurements['compression_stats']['storage_savings_mb']:.1f}MB ({quantized_measurements['compression_stats']['storage_savings_percent']:.1f}%)")
        
        self.quantized_measurements = quantized_measurements
        return quantized_measurements
    
    def calculate_hardware_requirements(self) -> Dict[str, Any]:
        """Calculate actual hardware requirements for 1-bit quantized Mistral 7B"""
        
        print(f"\n🔧 CALCULATING REAL HARDWARE REQUIREMENTS")
        print("=" * 50)
        
        if not self.quantized_measurements or not self.baseline_measurements:
            print("❌ Missing measurement data")
            return {}
        
        # Calculate requirements for full model
        layers_tested = self.quantized_measurements['layers_processed']
        total_layers = self.baseline_measurements['config_layers']
        
        # Estimate full model requirements
        avg_layer_ram = self.quantized_measurements['memory_usage']['total_used_mb'] / layers_tested
        estimated_full_model_ram = avg_layer_ram * total_layers
        
        # Add baseline requirements
        total_estimated_ram = self.baseline_measurements['total_baseline_ram_mb'] + estimated_full_model_ram
        
        # Calculate storage requirements
        compression_ratio = self.quantized_measurements['compression_stats']['overall_compression_ratio']
        original_model_size_gb = self.system_info['model_size_gb']
        compressed_model_size_gb = original_model_size_gb / compression_ratio
        
        # Calculate processing requirements
        avg_processing_time = self.quantized_measurements['total_processing_time_s'] / layers_tested
        estimated_full_processing_time = avg_processing_time * total_layers
        
        # Determine minimum requirements
        min_ram_gb = total_estimated_ram / 1024
        recommended_ram_gb = min_ram_gb * 1.5  # 50% buffer
        
        # CPU requirements
        avg_cpu_usage = self.quantized_measurements['cpu_usage']['average_percent']
        
        hardware_requirements = {
            'minimum_requirements': {
                'ram_gb': min_ram_gb,
                'storage_gb': compressed_model_size_gb + 2,  # Model + workspace
                'cpu_cores': 2,
                'cpu_speed_ghz': 2.0,
                'gpu_required': False
            },
            'recommended_requirements': {
                'ram_gb': recommended_ram_gb,
                'storage_gb': compressed_model_size_gb + 5,  # Model + workspace + temp
                'cpu_cores': 4,
                'cpu_speed_ghz': 3.0,
                'gpu_required': False,
                'gpu_memory_gb': 'Optional'
            },
            'performance_estimates': {
                'quantization_time_full_model_minutes': estimated_full_processing_time / 60,
                'inference_ram_mb': total_estimated_ram,
                'model_loading_time_s': estimated_full_processing_time * 0.1,  # Estimate
                'cpu_usage_percent': avg_cpu_usage
            },
            'compression_benefits': {
                'original_model_size_gb': original_model_size_gb,
                'compressed_model_size_gb': compressed_model_size_gb,
                'compression_ratio': compression_ratio,
                'storage_savings_gb': original_model_size_gb - compressed_model_size_gb,
                'storage_savings_percent': ((original_model_size_gb - compressed_model_size_gb) / original_model_size_gb * 100)
            },
            'compatibility': {
                'consumer_laptops': min_ram_gb <= 8,
                'budget_desktops': min_ram_gb <= 16,
                'mobile_devices': min_ram_gb <= 4,
                'edge_devices': min_ram_gb <= 2
            }
        }
        
        print(f"💾 MINIMUM RAM: {min_ram_gb:.1f}GB")
        print(f"💾 RECOMMENDED RAM: {recommended_ram_gb:.1f}GB")
        print(f"💿 STORAGE NEEDED: {compressed_model_size_gb:.1f}GB (vs {original_model_size_gb:.1f}GB original)")
        print(f"🔧 CPU USAGE: {avg_cpu_usage:.1f}%")
        print(f"⏱️ FULL QUANTIZATION TIME: {estimated_full_processing_time/60:.1f} minutes")
        
        print(f"\n🎯 DEVICE COMPATIBILITY:")
        print(f"   Consumer Laptops (8GB): {'✅ YES' if hardware_requirements['compatibility']['consumer_laptops'] else '❌ NO'}")
        print(f"   Budget Desktops (16GB): {'✅ YES' if hardware_requirements['compatibility']['budget_desktops'] else '❌ NO'}")
        print(f"   Mobile Devices (4GB): {'✅ YES' if hardware_requirements['compatibility']['mobile_devices'] else '❌ NO'}")
        print(f"   Edge Devices (2GB): {'✅ YES' if hardware_requirements['compatibility']['edge_devices'] else '❌ NO'}")
        
        return hardware_requirements

def main():
    """Run real hardware requirements test"""
    
    print("🚀🚀🚀 REAL HARDWARE REQUIREMENTS TEST 🚀🚀🚀")
    print("=" * 70)
    print("⚠️  100% REAL MEASUREMENTS - NO SIMULATION")
    print("🎯 Testing actual hardware needs for 1-bit quantized Mistral 7B")
    print()
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Mistral 7B model not found at {model_path}")
        return
    
    # Initialize tester
    tester = RealHardwareRequirementsTest(model_path)
    
    # Get system info
    system_info = tester.get_system_info()
    
    # Measure baseline requirements
    baseline = tester.measure_baseline_requirements()
    
    # Quantize and measure requirements
    quantized = tester.quantize_and_measure_requirements(num_layers=5)
    
    # Calculate hardware requirements
    requirements = tester.calculate_hardware_requirements()
    
    # Save complete results
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    results_file = f"real_hardware_requirements_{timestamp}.json"
    
    complete_results = {
        'timestamp': time.time(),
        'model_path': model_path,
        'test_type': 'real_hardware_requirements',
        'system_info': system_info,
        'baseline_measurements': baseline,
        'quantized_measurements': quantized,
        'hardware_requirements': requirements
    }
    
    with open(results_file, 'w') as f:
        json.dump(complete_results, f, indent=2, default=str)
    
    print(f"\n💾 Complete results saved to {results_file}")
    
    # Final summary
    print(f"\n🏁 REAL HARDWARE REQUIREMENTS SUMMARY")
    print(f"=" * 50)
    
    if requirements:
        min_req = requirements['minimum_requirements']
        rec_req = requirements['recommended_requirements']
        
        print(f"📊 MINIMUM REQUIREMENTS:")
        print(f"   RAM: {min_req['ram_gb']:.1f}GB")
        print(f"   Storage: {min_req['storage_gb']:.1f}GB")
        print(f"   CPU: {min_req['cpu_cores']} cores @ {min_req['cpu_speed_ghz']}GHz")
        print(f"   GPU: {'Required' if min_req['gpu_required'] else 'Not Required'}")
        
        print(f"\n📊 RECOMMENDED REQUIREMENTS:")
        print(f"   RAM: {rec_req['ram_gb']:.1f}GB")
        print(f"   Storage: {rec_req['storage_gb']:.1f}GB")
        print(f"   CPU: {rec_req['cpu_cores']} cores @ {rec_req['cpu_speed_ghz']}GHz")
        print(f"   GPU: {rec_req['gpu_memory_gb']}")
        
        print(f"\n🎯 REAL HARDWARE REQUIREMENTS DETERMINED!")
        print(f"📊 Based on actual measurements, not estimates")

if __name__ == "__main__":
    main()
