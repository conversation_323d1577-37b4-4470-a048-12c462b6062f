#!/usr/bin/env python3
"""Debug script for the web interface."""

import os
import sys
import uvicorn
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.absolute())
sys.path.insert(0, project_root)
logger.info(f"Project root: {project_root}")

def check_imports():
    """Check if all required modules can be imported."""
    modules = [
        'financial_agent',
        'financial_agent.agents',
        'financial_agent.agents.base_agent',
        'financial_agent.agents.data_agent',
        'financial_agent.agents.analysis_agent',
        'financial_agent.agents.strategy_agent',
        'financial_agent.agents.execution_agent',
        'financial_agent.agents.risk_agent',
        'financial_agent.web',
        'financial_agent.web.app',
        'financial_agent.web.integration'
    ]
    
    for module in modules:
        try:
            importlib.import_module(module)
            logger.info(f"✅ Imported {module}")
        except ImportError as e:
            logger.error(f"❌ Failed to import {module}: {e}")
            return False
    return True

def main():
    """Run the web interface with debugging."""
    try:
        # Check all imports first
        logger.info("=== Checking module imports ===")
        if not check_imports():
            logger.error("❌ Some modules failed to import")
            return 1
        
        # Try to import the app
        try:
            logger.info("\n=== Testing web app import ===")
            from financial_agent.web.app import app
            logger.info("✅ Successfully imported web app")
            
            # Test creating a TradingSystem instance
            try:
                logger.info("\n=== Testing TradingSystem initialization ===")
                from financial_agent.web.integration import TradingSystem
                
                config = {
                    'initial_portfolio': 100000.0,
                    'watchlist': ['SPY', 'QQQ'],
                    'timeframe': '1d',
                    'data_agent': {'api_key': 'test', 'cache_ttl': 300},
                    'analysis_agent': {'indicators': ['sma', 'rsi']},
                    'strategy_agent': {'strategy': 'etf_rotation'},
                    'execution_agent': {'paper_trading': True},
                    'risk_agent': {'max_drawdown': 0.1}
                }
                
                trading_system = TradingSystem(config)
                logger.info("✅ Successfully created TradingSystem instance")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize TradingSystem: {e}")
                raise
                
        except ImportError as e:
            logger.error(f"❌ Failed to import web app: {e}")
            raise
            
        # Start the server
        logger.info("\n=== Starting web server ===")
        uvicorn.run(
            "financial_agent.web.app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="debug"
        )
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    return 0

if __name__ == "__main__":
    sys.exit(main())
