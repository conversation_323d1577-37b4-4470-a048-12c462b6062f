#!/usr/bin/env python3
"""
Test the Autonomous Research Agent
"""

from autonomous_researcher import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_autonomous_researcher():
    print('🚀 Testing Autonomous Research Agent...')
    
    researcher = AutonomousResearcher()
    
    # Test research cycle
    result = researcher.autonomous_research_cycle('machine learning')
    
    print('✅ Research cycle completed!')
    print(f'Papers analyzed: {result.get("papers_analyzed", 0)}')
    print(f'Research quality: {result.get("research_quality", 0):.2f}')
    
    # Get summary
    summary = researcher.get_research_summary()
    print(f'Total hypotheses: {summary["hypotheses_generated"]}')
    print(f'Domains explored: {summary["domains_explored"]}')
    
    if summary["latest_hypothesis"]:
        latest = summary["latest_hypothesis"]
        print(f'Latest hypothesis: {latest["title"]}')
        print(f'Novelty score: {latest["novelty_score"]:.2f}')

if __name__ == "__main__":
    test_autonomous_researcher()
