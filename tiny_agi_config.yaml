# Tiny AGI Configuration
# Realistic autonomous intelligence for compression research

# Resource Constraints
max_ram_gb: 6.0
max_cpu_percent: 75
max_disk_usage_gb: 4.0

# Safety Configuration
safety_score_threshold: 0.75
rollback_on_failure: true
emergency_stop_enabled: true

# Operational Parameters
max_cycles: 100
cycle_interval_seconds: 30
domain_focus: "compression_research"

# Prohibited Actions (Safety)
prohibited_actions:
  - "os.system(\"rm -rf\")"
  - "open(\"/etc/passwd\")"
  - "network_calls_unauthorized"
  - "file_deletion_system"
  - "registry_modification"
  - "process_termination_external"

# Allowed Domains
allowed_domains:
  - "compression_research"
  - "model_optimization"
  - "performance_analysis"
  - "algorithm_testing"
  - "benchmark_execution"

# Intelligence Parameters
reasoning_depth: 3
multi_pass_reasoning: true
confidence_threshold: 0.7
learning_rate: 0.1

# Memory Configuration
memory_retention_days: 30
max_knowledge_entries: 10000
knowledge_confidence_decay: 0.95

# Performance Targets
target_compression_ratio: 32.0
target_quality_retention: 0.99
target_ram_usage_mb: 400
target_inference_speed_ms: 100

# Logging Configuration
log_level: "INFO"
detailed_logging: true
performance_tracking: true
thought_logging: true

# Model Configuration
model_name: "mistral-7b-v0.1"
model_path: "downloaded_models/mistral-7b-v0.1"
compression_enabled: true
quantization_bits: 1

# Autonomous Behavior
autonomous_goal_setting: true
autonomous_improvement: true
autonomous_research: true
autonomous_validation: true

# Safety Limits
max_consecutive_failures: 3
max_execution_time_minutes: 60
resource_monitoring_interval: 10

# Research Focus Areas
research_priorities:
  - "compression_efficiency"
  - "quality_preservation" 
  - "memory_optimization"
  - "inference_speed"
  - "algorithm_innovation"

# Success Metrics
success_criteria:
  min_success_rate: 0.8
  min_performance_improvement: 0.05
  min_safety_score: 0.95
  max_resource_usage: 0.8
