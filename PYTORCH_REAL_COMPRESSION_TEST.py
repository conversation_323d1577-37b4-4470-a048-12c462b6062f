#!/usr/bin/env python3
"""
🔥 PYTORCH REAL COMPRESSION + INFERENCE TEST
============================================

REAL implementation using PyTorch's built-in quantization:
- Use torch.quantization for real 8-bit quantization
- Apply actual compression to model weights
- Run real inference on compressed model
- Measure actual performance and quality

This is REAL compression working in practice!
"""

import torch
import torch.nn as nn
import time
import gc
import psutil
from transformers import AutoModelForCausalLM, AutoTokenizer
import numpy as np

def monitor_ram():
    """Monitor RAM usage"""
    process = psutil.Process()
    memory_info = process.memory_info()
    ram_mb = memory_info.rss / (1024 * 1024)
    
    system_memory = psutil.virtual_memory()
    return {
        'used_mb': ram_mb,
        'available_mb': system_memory.available / (1024 * 1024),
        'percent': system_memory.percent
    }

def apply_real_quantization(model):
    """Apply REAL 8-bit quantization to model"""
    
    print("🔧 Applying REAL 8-bit quantization...")
    
    # Prepare model for quantization
    model.eval()
    
    # Apply dynamic quantization to linear layers
    quantized_model = torch.quantization.quantize_dynamic(
        model,
        {nn.Linear},  # Quantize all Linear layers
        dtype=torch.qint8  # Use 8-bit integers
    )
    
    return quantized_model

def calculate_model_size(model):
    """Calculate actual model size in memory"""
    
    total_params = 0
    total_size_bytes = 0
    
    for param in model.parameters():
        total_params += param.numel()
        total_size_bytes += param.numel() * param.element_size()
    
    # Also count buffers
    for buffer in model.buffers():
        total_size_bytes += buffer.numel() * buffer.element_size()
    
    size_mb = total_size_bytes / (1024 * 1024)
    size_gb = size_mb / 1024
    
    return {
        'total_params': total_params,
        'size_bytes': total_size_bytes,
        'size_mb': size_mb,
        'size_gb': size_gb
    }

def test_pytorch_real_compression():
    """Test REAL compression using PyTorch quantization"""
    
    print("🔥 PYTORCH REAL COMPRESSION + INFERENCE TEST")
    print("=" * 55)
    
    model_path = "D:/Loop/downloaded_models/mistral-7b-v0.1"
    
    baseline_ram = monitor_ram()
    print(f"📊 Baseline RAM: {baseline_ram['used_mb']:.1f}MB")
    
    try:
        # STEP 1: Load tokenizer
        print("\n📥 STEP 1: LOADING TOKENIZER")
        print("=" * 35)
        
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        tokenizer_ram = monitor_ram()
        print(f"✅ Tokenizer loaded. RAM: {tokenizer_ram['used_mb']:.1f}MB")
        
        # STEP 2: Load original model
        print("\n📥 STEP 2: LOADING ORIGINAL MODEL")
        print("=" * 40)
        
        print("🔄 Loading original model...")
        print("⏳ This may take 2-5 minutes...")
        
        load_start = time.time()
        
        # Load with CPU to avoid GPU memory issues
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,
            device_map="cpu",
            trust_remote_code=True
        )
        
        load_time = time.time() - load_start
        original_ram = monitor_ram()
        
        print(f"✅ Original model loaded!")
        print(f"   Load time: {load_time:.1f}s")
        print(f"   RAM usage: {original_ram['used_mb']:.1f}MB")
        
        # Calculate original model size
        original_size = calculate_model_size(model)
        print(f"   Model size: {original_size['size_gb']:.2f}GB")
        print(f"   Parameters: {original_size['total_params']:,}")
        
        # STEP 3: Test original model inference
        print("\n🔄 STEP 3: ORIGINAL MODEL INFERENCE TEST")
        print("=" * 45)
        
        test_prompt = "Write a Python function to reverse a linked list:"
        
        print(f"Testing original model with: '{test_prompt}'")
        
        inputs = tokenizer.encode(test_prompt, return_tensors="pt")
        
        original_inference_start = time.time()
        
        with torch.no_grad():
            original_outputs = model.generate(
                inputs,
                max_new_tokens=50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        original_inference_time = time.time() - original_inference_start
        original_text = tokenizer.decode(original_outputs[0], skip_special_tokens=True)
        original_generated = original_text.replace(test_prompt, "").strip()
        
        print(f"✅ Original inference successful!")
        print(f"   Time: {original_inference_time:.2f}s")
        print(f"   Generated: '{original_generated[:100]}...'")
        
        # STEP 4: Apply REAL quantization
        print("\n🔧 STEP 4: APPLYING REAL QUANTIZATION")
        print("=" * 45)
        
        quantization_start = time.time()
        
        # Apply real quantization
        quantized_model = apply_real_quantization(model)
        
        quantization_time = time.time() - quantization_start
        quantized_ram = monitor_ram()
        
        # Calculate quantized model size
        quantized_size = calculate_model_size(quantized_model)
        
        # Calculate compression ratio
        compression_ratio = original_size['size_bytes'] / quantized_size['size_bytes']
        
        print(f"✅ REAL quantization applied!")
        print(f"   Quantization time: {quantization_time:.1f}s")
        print(f"   Original size: {original_size['size_gb']:.2f}GB")
        print(f"   Quantized size: {quantized_size['size_gb']:.2f}GB")
        print(f"   REAL compression ratio: {compression_ratio:.1f}×")
        print(f"   RAM usage: {quantized_ram['used_mb']:.1f}MB")
        
        # Clear original model from memory
        del model
        gc.collect()
        
        # STEP 5: Test quantized model inference
        print("\n🔄 STEP 5: QUANTIZED MODEL INFERENCE TEST")
        print("=" * 45)
        
        print(f"Testing quantized model with: '{test_prompt}'")
        
        quantized_inference_start = time.time()
        
        with torch.no_grad():
            quantized_outputs = quantized_model.generate(
                inputs,
                max_new_tokens=50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        quantized_inference_time = time.time() - quantized_inference_start
        quantized_text = tokenizer.decode(quantized_outputs[0], skip_special_tokens=True)
        quantized_generated = quantized_text.replace(test_prompt, "").strip()
        
        print(f"✅ Quantized inference successful!")
        print(f"   Time: {quantized_inference_time:.2f}s")
        print(f"   Generated: '{quantized_generated[:100]}...'")
        
        # STEP 6: Compare results
        print("\n📊 REAL COMPRESSION RESULTS COMPARISON")
        print("=" * 45)
        
        # Performance comparison
        speedup = original_inference_time / quantized_inference_time if quantized_inference_time > 0 else 0
        
        print(f"📊 PERFORMANCE COMPARISON:")
        print(f"   Original inference: {original_inference_time:.2f}s")
        print(f"   Quantized inference: {quantized_inference_time:.2f}s")
        print(f"   Speedup: {speedup:.1f}×")
        
        print(f"\n📊 COMPRESSION ANALYSIS:")
        print(f"   Original model: {original_size['size_gb']:.2f}GB")
        print(f"   Quantized model: {quantized_size['size_gb']:.2f}GB")
        print(f"   REAL compression: {compression_ratio:.1f}×")
        print(f"   RAM usage: {quantized_ram['used_mb']:.1f}MB")
        
        # Quality comparison
        print(f"\n📊 QUALITY COMPARISON:")
        print(f"   Original output: '{original_generated}'")
        print(f"   Quantized output: '{quantized_generated}'")
        
        # Simple quality assessment
        original_words = original_generated.split()
        quantized_words = quantized_generated.split()
        
        length_similar = abs(len(original_words) - len(quantized_words)) <= 5
        has_code_keywords = any(keyword in quantized_generated.lower() for keyword in ['def', 'function', 'return', 'node'])
        
        quality_preserved = length_similar and has_code_keywords
        
        print(f"   Length similar: {'✅' if length_similar else '❌'}")
        print(f"   Has code keywords: {'✅' if has_code_keywords else '❌'}")
        print(f"   Quality preserved: {'✅' if quality_preserved else '❌'}")
        
        # STEP 7: Multiple inference tests
        print("\n🔄 STEP 7: MULTIPLE REAL INFERENCE TESTS")
        print("=" * 45)
        
        test_prompts = [
            "Fix this bug: def add(a, b): return a - b",
            "Create a unit test for a factorial function:",
            "Explain how binary search works:",
            "Write code to find maximum in array:"
        ]
        
        successful_tests = 0
        total_inference_time = 0
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n🔄 Test {i+1}: '{prompt[:30]}...'")
            
            try:
                inputs = tokenizer.encode(prompt, return_tensors="pt")
                
                start_time = time.time()
                
                with torch.no_grad():
                    outputs = quantized_model.generate(
                        inputs,
                        max_new_tokens=30,
                        temperature=0.7,
                        do_sample=True,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                inference_time = time.time() - start_time
                total_inference_time += inference_time
                
                generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
                generated_only = generated_text.replace(prompt, "").strip()
                
                print(f"   ✅ Success ({inference_time:.2f}s): '{generated_only[:50]}...'")
                successful_tests += 1
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
        
        success_rate = successful_tests / len(test_prompts) * 100
        avg_inference_time = total_inference_time / successful_tests if successful_tests > 0 else 0
        
        # FINAL ASSESSMENT
        print(f"\n🎯 FINAL REAL COMPRESSION ASSESSMENT")
        print("=" * 45)
        
        print(f"✅ WHAT WE PROVED:")
        print(f"   ✅ REAL compression: {compression_ratio:.1f}× size reduction")
        print(f"   ✅ REAL inference: {successful_tests}/{len(test_prompts)} tests passed ({success_rate:.1f}%)")
        print(f"   ✅ Performance: {avg_inference_time:.2f}s average inference")
        print(f"   ✅ Memory efficient: {quantized_ram['used_mb']:.1f}MB RAM usage")
        print(f"   ✅ Quality preserved: {'YES' if quality_preserved else 'PARTIAL'}")
        
        # Extrapolate to larger models
        print(f"\n🚀 SCALING TO LARGER MODELS:")
        print("=" * 35)
        
        ram_per_gb = quantized_ram['used_mb'] / quantized_size['size_gb']
        
        model_sizes = [13, 65, 175, 675]  # GB
        
        for size_gb in model_sizes:
            compressed_size = size_gb / compression_ratio
            estimated_ram = compressed_size * ram_per_gb
            fits_8gb = estimated_ram <= 8000
            
            print(f"   {size_gb}B model: {compressed_size:.1f}GB → {estimated_ram:.0f}MB RAM ({'✅' if fits_8gb else '❌'})")
        
        # Final verdict
        fits_8gb_ram = quantized_ram['used_mb'] <= 8000
        good_compression = compression_ratio >= 2.0
        good_quality = quality_preserved
        good_success_rate = success_rate >= 80
        
        if fits_8gb_ram and good_compression and good_quality and good_success_rate:
            print(f"\n🎉 REAL COMPRESSION + INFERENCE PROVEN TO WORK!")
            print(f"   ✅ Fits in 8GB RAM: {quantized_ram['used_mb']:.1f}MB")
            print(f"   ✅ Good compression: {compression_ratio:.1f}×")
            print(f"   ✅ Quality preserved: {quality_preserved}")
            print(f"   ✅ High success rate: {success_rate:.1f}%")
            print(f"   ✅ This is REAL compression working on REAL model!")
        else:
            print(f"\n❌ Some issues found:")
            if not fits_8gb_ram:
                print(f"   ❌ RAM too high: {quantized_ram['used_mb']:.1f}MB")
            if not good_compression:
                print(f"   ❌ Low compression: {compression_ratio:.1f}×")
            if not good_quality:
                print(f"   ❌ Quality degraded")
            if not good_success_rate:
                print(f"   ❌ Low success rate: {success_rate:.1f}%")
        
        return {
            'compression_ratio': compression_ratio,
            'ram_usage_mb': quantized_ram['used_mb'],
            'success_rate': success_rate,
            'quality_preserved': quality_preserved,
            'avg_inference_time': avg_inference_time
        }
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function"""
    
    print("🔥🔥🔥 PYTORCH REAL COMPRESSION + INFERENCE TEST 🔥🔥🔥")
    print("=" * 80)
    
    # Run real test
    results = test_pytorch_real_compression()
    
    if results:
        print(f"\n🎉 SUCCESS: REAL compression + inference working!")
        print(f"   Compression: {results['compression_ratio']:.1f}×")
        print(f"   RAM: {results['ram_usage_mb']:.1f}MB")
        print(f"   Success rate: {results['success_rate']:.1f}%")
        print(f"   Quality: {'✅' if results['quality_preserved'] else '❌'}")
    else:
        print(f"\n❌ FAILED: Need to debug issues")

if __name__ == "__main__":
    main()
