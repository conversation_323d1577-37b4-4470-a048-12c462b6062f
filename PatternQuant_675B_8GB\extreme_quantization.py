#!/usr/bin/env python3
"""
EXTREME QUANTIZATION
===================

Phase 2: Implement extreme quantization for 3-5× additional compression
Combined with Phase 1: Target 24-50× total compression
"""

import torch
import torch.nn as nn
import numpy as np
import time
from typing import Dict, List, Tuple, Any, Optional
from sklearn.cluster import KMeans
import json
import gc

class ExtremeQuantizer:
    """Extreme quantization for maximum compression"""
    
    def __init__(self, target_compression: float = 4.0):
        self.target_compression = target_compression
        self.quantization_schemes = {}
        
        print(f"⚡ EXTREME QUANTIZATION")
        print(f"🎯 Target compression: {target_compression}×")
    
    def sub_bit_quantization(self, tensor: torch.Tensor, bits_per_weight: float = 0.5) -> Dict[str, Any]:
        """Sub-1-bit quantization using advanced techniques"""
        
        print(f"🔬 Sub-bit quantization: {bits_per_weight} bits per weight")
        
        # Flatten tensor for processing
        flat_tensor = tensor.flatten()
        
        # Calculate sparsity - zero out smallest weights
        sparsity_ratio = 1.0 - bits_per_weight  # If 0.5 bits, 50% sparsity
        
        # Find threshold for sparsity
        abs_weights = torch.abs(flat_tensor)
        threshold = torch.quantile(abs_weights, sparsity_ratio)
        
        # Create sparse mask
        sparse_mask = abs_weights > threshold
        sparse_weights = flat_tensor * sparse_mask
        
        # Quantize remaining weights
        non_zero_weights = sparse_weights[sparse_mask]
        
        if len(non_zero_weights) > 0:
            # Binary quantization for remaining weights
            weight_mean = torch.mean(non_zero_weights)
            weight_std = torch.std(non_zero_weights)
            
            # Normalize weights
            if weight_std > 1e-8:
                normalized_weights = (non_zero_weights - weight_mean) / weight_std
            else:
                normalized_weights = non_zero_weights - weight_mean
            
            # Binary quantization: +1 or -1
            binary_weights = torch.sign(normalized_weights)
            
            # Store quantization parameters
            quantization_params = {
                'mean': weight_mean.item(),
                'std': weight_std.item(),
                'threshold': threshold.item(),
                'sparsity_ratio': sparsity_ratio,
                'bits_per_weight': bits_per_weight
            }
            
            # Calculate compression ratio
            original_bits = 32  # float32
            effective_bits = bits_per_weight
            compression_ratio = original_bits / effective_bits
            
            # Reconstruct for error calculation
            reconstructed_sparse = torch.zeros_like(flat_tensor)
            reconstructed_values = binary_weights * weight_std + weight_mean
            reconstructed_sparse[sparse_mask] = reconstructed_values
            
            # Calculate error
            mse_error = torch.mean((flat_tensor - reconstructed_sparse) ** 2).item()
            
            result = {
                'method': 'sub_bit_quantization',
                'compression_ratio': compression_ratio,
                'quantization_params': quantization_params,
                'sparse_mask': sparse_mask,
                'binary_weights': binary_weights,
                'mse_error': mse_error,
                'sparsity_achieved': (1.0 - torch.sum(sparse_mask).float() / len(flat_tensor)).item(),
                'bits_per_weight': bits_per_weight
            }
            
            print(f"✅ Sub-bit compression: {compression_ratio:.1f}×, sparsity: {result['sparsity_achieved']*100:.1f}%")
            
            return result
        
        else:
            print("⚠️ All weights below threshold - extreme sparsity")
            return {'compression_ratio': 1.0, 'error': 'extreme_sparsity'}
    
    def adaptive_cluster_quantization(self, tensor: torch.Tensor, 
                                    num_clusters: int = 16,
                                    use_kmeans_plus: bool = True) -> Dict[str, Any]:
        """Adaptive cluster quantization with optimized centroids"""
        
        print(f"🔬 Adaptive cluster quantization: {num_clusters} clusters")
        
        flat_tensor = tensor.flatten().numpy()
        
        # Remove outliers for better clustering
        q1 = np.percentile(flat_tensor, 25)
        q3 = np.percentile(flat_tensor, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # Separate outliers and normal values
        outlier_mask = (flat_tensor < lower_bound) | (flat_tensor > upper_bound)
        normal_values = flat_tensor[~outlier_mask]
        outlier_values = flat_tensor[outlier_mask]
        
        if len(normal_values) > num_clusters:
            # Cluster normal values
            init_method = 'k-means++' if use_kmeans_plus else 'random'
            kmeans = KMeans(n_clusters=num_clusters, init=init_method, n_init=10, random_state=42)
            
            normal_labels = kmeans.fit_predict(normal_values.reshape(-1, 1))
            centroids = kmeans.cluster_centers_.flatten()
            
            # Assign outliers to nearest centroids
            outlier_labels = []
            for outlier in outlier_values:
                distances = np.abs(centroids - outlier)
                closest_cluster = np.argmin(distances)
                outlier_labels.append(closest_cluster)
            
            # Combine labels
            full_labels = np.zeros(len(flat_tensor), dtype=int)
            full_labels[~outlier_mask] = normal_labels
            full_labels[outlier_mask] = outlier_labels
            
            # Calculate compression
            original_bits = 32  # float32
            quantized_bits = np.ceil(np.log2(num_clusters))
            compression_ratio = original_bits / quantized_bits
            
            # Reconstruct for error calculation
            reconstructed = centroids[full_labels]
            mse_error = np.mean((flat_tensor - reconstructed) ** 2)
            
            # Calculate cluster efficiency
            cluster_usage = np.bincount(full_labels, minlength=num_clusters)
            cluster_efficiency = np.sum(cluster_usage > 0) / num_clusters
            
            result = {
                'method': 'adaptive_cluster_quantization',
                'compression_ratio': compression_ratio,
                'num_clusters': num_clusters,
                'centroids': centroids.tolist(),
                'labels': full_labels.tolist(),
                'mse_error': mse_error,
                'cluster_efficiency': cluster_efficiency,
                'outlier_ratio': len(outlier_values) / len(flat_tensor),
                'quantized_bits': quantized_bits
            }
            
            print(f"✅ Cluster compression: {compression_ratio:.1f}×, efficiency: {cluster_efficiency*100:.1f}%")
            
            return result
        
        else:
            print("⚠️ Too few values for clustering")
            return {'compression_ratio': 1.0, 'error': 'insufficient_data'}
    
    def sparse_representation_quantization(self, tensor: torch.Tensor, 
                                         sparsity_target: float = 0.9) -> Dict[str, Any]:
        """Sparse representation with quantization"""
        
        print(f"🔬 Sparse representation: {sparsity_target*100:.1f}% sparsity target")
        
        flat_tensor = tensor.flatten()
        
        # Magnitude-based pruning
        abs_weights = torch.abs(flat_tensor)
        threshold = torch.quantile(abs_weights, sparsity_target)
        
        # Create sparse representation
        sparse_mask = abs_weights > threshold
        sparse_weights = flat_tensor[sparse_mask]
        
        if len(sparse_weights) > 0:
            # Quantize remaining weights with higher precision
            num_levels = 256  # 8-bit quantization for remaining weights
            
            weight_min = torch.min(sparse_weights)
            weight_max = torch.max(sparse_weights)
            weight_range = weight_max - weight_min
            
            if weight_range > 1e-8:
                # Uniform quantization
                scale = weight_range / (num_levels - 1)
                quantized_indices = torch.round((sparse_weights - weight_min) / scale).long()
                quantized_indices = torch.clamp(quantized_indices, 0, num_levels - 1)
                
                # Reconstruct quantized weights
                quantized_weights = weight_min + quantized_indices.float() * scale
                
                # Calculate compression
                original_bits = 32 * len(flat_tensor)  # All weights in float32
                sparse_bits = (
                    len(sparse_mask) +  # 1 bit per position for mask
                    len(sparse_weights) * 8  # 8 bits per remaining weight
                )
                compression_ratio = original_bits / sparse_bits
                
                # Reconstruct full tensor for error calculation
                reconstructed = torch.zeros_like(flat_tensor)
                reconstructed[sparse_mask] = quantized_weights
                
                mse_error = torch.mean((flat_tensor - reconstructed) ** 2).item()
                
                result = {
                    'method': 'sparse_representation_quantization',
                    'compression_ratio': compression_ratio,
                    'sparsity_achieved': 1.0 - len(sparse_weights) / len(flat_tensor),
                    'sparse_mask': sparse_mask,
                    'quantized_weights': quantized_weights,
                    'quantization_params': {
                        'min': weight_min.item(),
                        'max': weight_max.item(),
                        'scale': scale.item(),
                        'num_levels': num_levels
                    },
                    'mse_error': mse_error,
                    'threshold': threshold.item()
                }
                
                print(f"✅ Sparse compression: {compression_ratio:.1f}×, sparsity: {result['sparsity_achieved']*100:.1f}%")
                
                return result
            
            else:
                print("⚠️ Weight range too small for quantization")
                return {'compression_ratio': 1.0, 'error': 'small_range'}
        
        else:
            print("⚠️ All weights pruned - extreme sparsity")
            return {'compression_ratio': float('inf'), 'error': 'all_pruned'}
    
    def combined_extreme_quantization(self, tensor: torch.Tensor) -> Dict[str, Any]:
        """Apply multiple quantization techniques and choose the best"""
        
        print(f"🚀 Combined extreme quantization on {tensor.shape}")
        
        techniques = [
            ('sub_bit_0.5', lambda: self.sub_bit_quantization(tensor, 0.5)),
            ('sub_bit_0.25', lambda: self.sub_bit_quantization(tensor, 0.25)),
            ('adaptive_16', lambda: self.adaptive_cluster_quantization(tensor, 16)),
            ('adaptive_32', lambda: self.adaptive_cluster_quantization(tensor, 32)),
            ('sparse_90', lambda: self.sparse_representation_quantization(tensor, 0.9)),
            ('sparse_95', lambda: self.sparse_representation_quantization(tensor, 0.95))
        ]
        
        results = {}
        best_compression = 0
        best_method = None
        best_result = None
        
        for method_name, method_func in techniques:
            try:
                print(f"  🔬 Testing {method_name}...")
                result = method_func()
                
                if 'compression_ratio' in result and result['compression_ratio'] > best_compression:
                    # Check if error is reasonable (not too high)
                    if 'mse_error' in result and result['mse_error'] < 1.0:  # Reasonable error threshold
                        best_compression = result['compression_ratio']
                        best_method = method_name
                        best_result = result
                
                results[method_name] = result
                
            except Exception as e:
                print(f"    ❌ {method_name} failed: {e}")
                results[method_name] = {'error': str(e), 'compression_ratio': 1.0}
        
        # Combine results
        combined_result = {
            'method': 'combined_extreme_quantization',
            'best_method': best_method,
            'best_compression': best_compression,
            'best_result': best_result,
            'all_results': results,
            'target_achieved': best_compression >= self.target_compression
        }
        
        print(f"🎯 Best method: {best_method} with {best_compression:.1f}× compression")
        
        return combined_result
    
    def compress_layer_extreme(self, weight_tensor: torch.Tensor, layer_name: str) -> Dict[str, Any]:
        """Apply extreme quantization to a layer"""
        
        print(f"\n⚡ Extreme quantization: {layer_name}")
        print(f"📊 Input shape: {weight_tensor.shape}")
        
        start_time = time.time()
        
        # Apply combined extreme quantization
        quantization_result = self.combined_extreme_quantization(weight_tensor)
        
        # Calculate memory savings
        original_size_mb = weight_tensor.numel() * weight_tensor.element_size() / (1024**2)
        
        if quantization_result['best_compression'] > 1:
            compressed_size_mb = original_size_mb / quantization_result['best_compression']
            memory_savings_mb = original_size_mb - compressed_size_mb
        else:
            compressed_size_mb = original_size_mb
            memory_savings_mb = 0
        
        duration = time.time() - start_time
        
        result = {
            'layer_name': layer_name,
            'original_shape': list(weight_tensor.shape),
            'original_size_mb': original_size_mb,
            'compressed_size_mb': compressed_size_mb,
            'memory_savings_mb': memory_savings_mb,
            'compression_ratio': quantization_result['best_compression'],
            'compression_method': 'extreme_quantization',
            'quantization_analysis': quantization_result,
            'processing_time_s': duration,
            'target_achieved': quantization_result['target_achieved']
        }
        
        print(f"✅ Compression: {quantization_result['best_compression']:.1f}× (target: {self.target_compression}×)")
        print(f"📊 Size: {original_size_mb:.1f}MB → {compressed_size_mb:.1f}MB")
        print(f"⏱️ Time: {duration:.1f}s")
        
        return result
    
    def batch_quantize_model(self, model_path: str, max_layers: int = 10) -> Dict[str, Any]:
        """Apply extreme quantization to multiple layers"""
        
        print(f"\n🚀 BATCH EXTREME QUANTIZATION")
        print(f"📁 Model: {model_path}")
        print(f"🎯 Target: {self.target_compression}× per layer")
        
        # Load model index
        import os
        from safetensors import safe_open
        
        index_path = os.path.join(model_path, "model.safetensors.index.json")
        if not os.path.exists(index_path):
            print(f"❌ Model index not found: {index_path}")
            return {}
        
        with open(index_path, 'r') as f:
            weight_index = json.load(f)
        
        # Select layers for quantization
        layer_names = list(weight_index['weight_map'].keys())
        priority_layers = [name for name in layer_names 
                          if any(keyword in name for keyword in ['q_proj', 'k_proj', 'v_proj', 'o_proj', 
                                                               'gate_proj', 'up_proj', 'down_proj'])]
        
        selected_layers = priority_layers[:max_layers]
        
        print(f"📊 Selected {len(selected_layers)} layers for quantization")
        
        batch_results = {}
        total_original_size = 0
        total_compressed_size = 0
        successful_quantizations = 0
        
        for i, layer_name in enumerate(selected_layers):
            print(f"\n📊 Layer {i+1}/{len(selected_layers)}: {layer_name}")
            
            try:
                # Load layer
                file_name = weight_index['weight_map'][layer_name]
                file_path = os.path.join(model_path, file_name)
                
                with safe_open(file_path, framework="pt", device="cpu") as f:
                    weight_tensor = f.get_tensor(layer_name)
                    
                    # Skip non-2D tensors
                    if len(weight_tensor.shape) != 2:
                        print(f"⚠️ Skipping non-2D tensor: {weight_tensor.shape}")
                        continue
                    
                    # Apply extreme quantization
                    result = self.compress_layer_extreme(weight_tensor, layer_name)
                    
                    batch_results[layer_name] = result
                    total_original_size += result['original_size_mb']
                    total_compressed_size += result['compressed_size_mb']
                    
                    if result['target_achieved']:
                        successful_quantizations += 1
                    
                    # Memory cleanup
                    del weight_tensor
                    gc.collect()
            
            except Exception as e:
                print(f"❌ Error processing {layer_name}: {e}")
                continue
        
        # Calculate overall statistics
        overall_compression = total_original_size / total_compressed_size if total_compressed_size > 0 else 1.0
        success_rate = successful_quantizations / len(batch_results) * 100 if batch_results else 0
        
        batch_summary = {
            'timestamp': time.time(),
            'model_path': model_path,
            'layers_processed': len(batch_results),
            'successful_quantizations': successful_quantizations,
            'success_rate_percent': success_rate,
            'total_original_size_mb': total_original_size,
            'total_compressed_size_mb': total_compressed_size,
            'overall_compression_ratio': overall_compression,
            'target_compression': self.target_compression,
            'target_achieved': overall_compression >= self.target_compression,
            'layer_results': batch_results
        }
        
        print(f"\n📊 BATCH QUANTIZATION SUMMARY:")
        print(f"   Layers processed: {len(batch_results)}")
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Overall compression: {overall_compression:.1f}×")
        print(f"   Target achieved: {'✅ YES' if batch_summary['target_achieved'] else '❌ NO'}")
        
        return batch_summary

def main():
    """Test extreme quantization"""
    
    print("🚀🚀🚀 EXTREME QUANTIZATION 🚀🚀🚀")
    print("=" * 70)
    print("🎯 Phase 2: Achieve 3-5× compression with extreme quantization")
    print()
    
    # Initialize quantizer
    quantizer = ExtremeQuantizer(target_compression=4.0)
    
    # Test on model
    model_path = "../downloaded_models/mistral-7b-v0.1"
    if not os.path.exists(model_path):
        model_path = "downloaded_models/mistral-7b-v0.1"
    
    if os.path.exists(model_path):
        # Run batch quantization
        results = quantizer.batch_quantize_model(model_path, max_layers=5)
        
        # Save results
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        results_file = f"extreme_quantization_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n✅ Results saved: {results_file}")
        
        if results.get('target_achieved', False):
            print(f"🎉 PHASE 2 SUCCESS: {results['overall_compression_ratio']:.1f}× compression achieved!")
        else:
            print(f"🔧 Continue optimization: {results['overall_compression_ratio']:.1f}× achieved, target: {quantizer.target_compression}×")
    
    else:
        print(f"❌ Model not found: {model_path}")
        print("🔧 Testing with synthetic data...")
        
        # Test with synthetic data
        test_tensor = torch.randn(512, 512) * 0.1
        result = quantizer.compress_layer_extreme(test_tensor, "test_layer")
        
        print(f"🧪 Synthetic test: {result['compression_ratio']:.1f}× compression")

if __name__ == "__main__":
    main()
