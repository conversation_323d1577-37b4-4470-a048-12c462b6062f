#!/usr/bin/env python3
"""
QUICK REAL HARDWARE REQUIREMENTS TEST
====================================

Fast test to get REAL hardware requirements for 1-bit quantized Mistral 7B.
100% REAL MEASUREMENTS - NO SIMULATION
"""

import os
import torch
import gc
import psutil
import time
import json
import platform
from typing import Dict, Any
from transformers import AutoTokenizer, AutoConfig
from safetensors import safe_open

def get_memory_mb():
    """Get current memory usage in MB"""
    return psutil.Process().memory_info().rss / (1024**2)

def get_real_model_size(model_path):
    """Get actual model size on disk"""
    total_size = 0
    for root, dirs, files in os.walk(model_path):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
    return total_size / (1024**3)  # Convert to GB

def quantize_weight_and_measure(weight_tensor):
    """Quantize weight and measure requirements"""
    start_memory = get_memory_mb()
    
    # Convert to float32 if needed
    if weight_tensor.dtype != torch.float32:
        weight_tensor = weight_tensor.to(torch.float32)
    
    convert_memory = get_memory_mb()
    
    # 1-bit quantization
    scale = torch.mean(torch.abs(weight_tensor))
    quantized_signs = torch.sign(weight_tensor).to(torch.int8)
    
    quantize_memory = get_memory_mb()
    
    # Calculate sizes
    original_size_mb = weight_tensor.numel() * 4 / (1024**2)
    quantized_size_mb = (weight_tensor.numel() / 8 + 4) / (1024**2)
    compression_ratio = original_size_mb / quantized_size_mb
    
    # Test reconstruction
    reconstructed = quantized_signs.to(torch.float32) * scale
    mse_error = torch.mean((weight_tensor - reconstructed) ** 2).item()
    
    reconstruct_memory = get_memory_mb()
    
    # Clean up
    del weight_tensor, quantized_signs, reconstructed
    gc.collect()
    
    cleanup_memory = get_memory_mb()
    
    return {
        'original_size_mb': original_size_mb,
        'quantized_size_mb': quantized_size_mb,
        'compression_ratio': compression_ratio,
        'mse_error': mse_error,
        'memory_usage': {
            'start_mb': start_memory,
            'after_convert_mb': convert_memory,
            'after_quantize_mb': quantize_memory,
            'after_reconstruct_mb': reconstruct_memory,
            'after_cleanup_mb': cleanup_memory,
            'peak_mb': max(convert_memory, quantize_memory, reconstruct_memory)
        }
    }

def main():
    """Run quick hardware requirements test"""
    
    print("🚀 QUICK REAL HARDWARE REQUIREMENTS TEST 🚀")
    print("=" * 60)
    print("⚠️  100% REAL MEASUREMENTS - NO SIMULATION")
    print("🎯 Testing actual hardware needs for 1-bit quantized Mistral 7B")
    print()
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Mistral 7B model not found at {model_path}")
        return
    
    start_time = time.time()
    
    # System info
    print("💻 REAL SYSTEM INFORMATION")
    print("=" * 40)
    
    memory = psutil.virtual_memory()
    system_info = {
        'platform': platform.platform(),
        'cpu_cores_physical': psutil.cpu_count(logical=False),
        'cpu_cores_logical': psutil.cpu_count(logical=True),
        'total_ram_gb': memory.total / (1024**3),
        'available_ram_gb': memory.available / (1024**3),
        'model_size_gb': get_real_model_size(model_path)
    }
    
    print(f"✅ Platform: {system_info['platform']}")
    print(f"✅ CPU Cores: {system_info['cpu_cores_physical']} physical, {system_info['cpu_cores_logical']} logical")
    print(f"✅ RAM: {system_info['total_ram_gb']:.1f}GB total, {system_info['available_ram_gb']:.1f}GB available")
    print(f"✅ REAL Model Size: {system_info['model_size_gb']:.2f}GB")
    
    # Baseline measurements
    print(f"\n📊 BASELINE MEASUREMENTS")
    print("=" * 40)
    
    baseline_memory = get_memory_mb()
    baseline_cpu = psutil.cpu_percent(interval=1)
    
    # Load tokenizer and config
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    config = AutoConfig.from_pretrained(model_path)
    
    tokenizer_memory = get_memory_mb()
    
    print(f"💾 Baseline RAM: {baseline_memory:.1f}MB")
    print(f"💾 After tokenizer: {tokenizer_memory:.1f}MB")
    print(f"🔧 CPU usage: {baseline_cpu:.1f}%")
    
    # Test quantization on key layers
    print(f"\n🔄 TESTING 1-BIT QUANTIZATION")
    print("=" * 40)
    
    # Load model index
    index_path = os.path.join(model_path, "model.safetensors.index.json")
    with open(index_path, 'r') as f:
        index = json.load(f)
    
    # Test 3 representative layers
    test_layers = [
        "model.embed_tokens.weight",  # Large embedding
        "model.layers.0.self_attn.q_proj.weight",  # Attention
        "model.layers.0.mlp.gate_proj.weight"  # MLP
    ]
    
    quantization_results = []
    total_original_mb = 0
    total_quantized_mb = 0
    peak_memory = tokenizer_memory
    
    for i, layer_name in enumerate(test_layers):
        if layer_name not in index['weight_map']:
            continue
        
        print(f"\n📥 [{i+1}/3] Testing {layer_name}")
        
        file_name = index['weight_map'][layer_name]
        file_path = os.path.join(model_path, file_name)
        
        layer_start_time = time.time()
        
        # Load and test weight
        with safe_open(file_path, framework="pt", device="cpu") as f:
            weight_tensor = f.get_tensor(layer_name)
            
            print(f"   📊 Shape: {weight_tensor.shape}")
            print(f"   📊 Parameters: {weight_tensor.numel():,}")
            
            # Quantize and measure
            result = quantize_weight_and_measure(weight_tensor)
            
            layer_end_time = time.time()
            
            result.update({
                'layer_name': layer_name,
                'shape': list(weight_tensor.shape),
                'parameters': weight_tensor.numel(),
                'processing_time_s': layer_end_time - layer_start_time
            })
            
            quantization_results.append(result)
            
            total_original_mb += result['original_size_mb']
            total_quantized_mb += result['quantized_size_mb']
            peak_memory = max(peak_memory, result['memory_usage']['peak_mb'])
            
            print(f"   ✅ Time: {result['processing_time_s']:.1f}s")
            print(f"   ✅ Peak RAM: {result['memory_usage']['peak_mb']:.1f}MB")
            print(f"   ✅ Compression: {result['compression_ratio']:.1f}×")
            print(f"   ✅ MSE Error: {result['mse_error']:.6f}")
    
    # Calculate full model estimates
    print(f"\n🔧 CALCULATING REAL HARDWARE REQUIREMENTS")
    print("=" * 50)
    
    # Estimate for full model (32 layers + embeddings + head)
    total_layers = config.num_hidden_layers
    
    # Average per layer from our tests
    avg_compression = total_original_mb / total_quantized_mb if total_quantized_mb > 0 else 32
    avg_memory_per_layer = (peak_memory - tokenizer_memory) / len(quantization_results)
    
    # Full model estimates
    compressed_model_size_gb = system_info['model_size_gb'] / avg_compression
    estimated_peak_ram_gb = (tokenizer_memory + avg_memory_per_layer * 2) / 1024  # Conservative estimate
    
    # Hardware requirements
    min_ram_gb = max(2, estimated_peak_ram_gb)
    recommended_ram_gb = min_ram_gb * 1.5
    
    hardware_requirements = {
        'minimum_requirements': {
            'ram_gb': round(min_ram_gb, 1),
            'storage_gb': round(compressed_model_size_gb + 2, 1),
            'cpu_cores': 2,
            'gpu_required': False
        },
        'recommended_requirements': {
            'ram_gb': round(recommended_ram_gb, 1),
            'storage_gb': round(compressed_model_size_gb + 5, 1),
            'cpu_cores': 4,
            'gpu_required': False
        },
        'compression_benefits': {
            'original_model_size_gb': round(system_info['model_size_gb'], 2),
            'compressed_model_size_gb': round(compressed_model_size_gb, 2),
            'compression_ratio': round(avg_compression, 1),
            'storage_savings_gb': round(system_info['model_size_gb'] - compressed_model_size_gb, 2),
            'storage_savings_percent': round((system_info['model_size_gb'] - compressed_model_size_gb) / system_info['model_size_gb'] * 100, 1)
        },
        'performance_estimates': {
            'peak_ram_gb': round(estimated_peak_ram_gb, 1),
            'quantization_time_minutes': round(len(quantization_results) * 2 * total_layers / 60, 1),  # Estimate
            'cpu_usage_percent': baseline_cpu
        },
        'device_compatibility': {
            'consumer_laptops_8gb': min_ram_gb <= 8,
            'budget_desktops_16gb': min_ram_gb <= 16,
            'mobile_devices_4gb': min_ram_gb <= 4,
            'edge_devices_2gb': min_ram_gb <= 2
        }
    }
    
    end_time = time.time()
    
    # Results
    print(f"💾 MINIMUM RAM: {hardware_requirements['minimum_requirements']['ram_gb']}GB")
    print(f"💾 RECOMMENDED RAM: {hardware_requirements['recommended_requirements']['ram_gb']}GB")
    print(f"💿 STORAGE NEEDED: {hardware_requirements['minimum_requirements']['storage_gb']}GB")
    print(f"📊 COMPRESSION: {hardware_requirements['compression_benefits']['compression_ratio']}× ({hardware_requirements['compression_benefits']['storage_savings_percent']}% savings)")
    
    print(f"\n🎯 DEVICE COMPATIBILITY:")
    compat = hardware_requirements['device_compatibility']
    print(f"   Consumer Laptops (8GB): {'✅ YES' if compat['consumer_laptops_8gb'] else '❌ NO'}")
    print(f"   Budget Desktops (16GB): {'✅ YES' if compat['budget_desktops_16gb'] else '❌ NO'}")
    print(f"   Mobile Devices (4GB): {'✅ YES' if compat['mobile_devices_4gb'] else '❌ NO'}")
    print(f"   Edge Devices (2GB): {'✅ YES' if compat['edge_devices_2gb'] else '❌ NO'}")
    
    # Save results
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    results_file = f"quick_hardware_requirements_{timestamp}.json"
    
    complete_results = {
        'timestamp': time.time(),
        'test_duration_s': end_time - start_time,
        'model_path': model_path,
        'system_info': system_info,
        'baseline_measurements': {
            'baseline_memory_mb': baseline_memory,
            'tokenizer_memory_mb': tokenizer_memory,
            'baseline_cpu_percent': baseline_cpu
        },
        'quantization_results': quantization_results,
        'hardware_requirements': hardware_requirements
    }
    
    with open(results_file, 'w') as f:
        json.dump(complete_results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to {results_file}")
    
    print(f"\n🏁 REAL HARDWARE REQUIREMENTS SUMMARY")
    print(f"=" * 50)
    print(f"⚠️  100% REAL MEASUREMENTS - NO SIMULATION")
    print(f"📊 Test completed in {end_time - start_time:.1f} seconds")
    print(f"🎯 1-bit quantized Mistral 7B hardware requirements determined!")

if __name__ == "__main__":
    main()
