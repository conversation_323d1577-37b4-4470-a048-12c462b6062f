# 📘 PLANNING.md — LOOP Trinity AGI Core

## 🧠 PROJECT VISION
Build a truly autonomous, self-improving AGI system that follows <PERSON>'s vision:
> "A very tiny model with superhuman reasoning, 1 trillion tokens of context, and access to every tool you can imagine."

### OBJECTIVE:
Create a working prototype of Loop AGI with:
- Autonomous recursive self-improvement
- Real performance benchmarking
- Multi-agent structure (Memory, Reasoner, Tools)
- 100% local operation on compressed model

---

## 🧩 MODULES OVERVIEW

```
/loop_agi/
│
├── loop_core.py               # AGI controller
├── memory/
│   └── memory_manager.py      # Vector + long-term memory
├── reasoning/
│   └── reasoner.py            # Chain-of-thought solver
├── tools/
│   ├── search_tool.py         # Google/web API wrapper
│   └── code_executor.py       # Python eval, sim runner
├── evolution/
│   └── mutator.py             # Self-modifier + selector
├── prompts/
│   └── meta_prompts.txt       # Core system prompt rules
├── logs/
│   └── evolution_log.json     # Benchmark scores + mutation logs
└── benchmark/
    └── test_suite.json        # Real reasoning + math tasks
```

---

## ✅ RULES FOR AUGMENT

### 1. SYSTEM RULES
- All modules must improve intelligence score based on benchmarks
- No simulated metrics — only real test results allowed
- Every improvement must be validated through re-testing

### 2. MUTATION RULES
- Mutator must only keep changes that improve benchmark scores
- Mutator can mutate: prompts, logic blocks, tool usage, memory retrieval
- Revert automatically if score drops

### 3. MEMORY RULES
- Use vector memory to store reasoning traces and benchmark attempts
- Retrieve top-k relevant items per problem
- Continuously compress and retain useful context

### 4. TOOL RULES
- Use tools only when reasoning fails or requires data/simulation
- Prioritize self-reasoning before calling external APIs
- All tool usage must be logged and reproducible

### 5. EXECUTION RULES
- Always run `benchmark/test_suite.json` after each mutation
- Always update `logs/evolution_log.json` with exact results
- If no improvement in 5 cycles → flag as stagnation

---

## 📋 TO-DO LIST (DO NOT DEVIATE)

### DAY 1: Project Bootstrap
- [ ] Scaffold full folder + file structure
- [ ] Add placeholder stubs for all modules
- [ ] Write `loop_core.py` as controller

### DAY 2: Benchmark Setup
- [ ] Add 5 math, 5 logic, 3 planning, 3 creative challenges
- [ ] Write benchmark scoring logic

### DAY 3: Initial Reasoner
- [ ] Build `reasoner.py` with CoT + retry
- [ ] Add base prompt strategies

### DAY 4: Basic Tools
- [ ] Implement `search_tool.py` using DuckDuckGo or local crawl
- [ ] Implement `code_executor.py` with safe eval

### DAY 5: LoopMemory
- [ ] Build `memory_manager.py` with JSON store
- [ ] Implement top-k similarity lookup

### DAY 6: LoopMutator
- [ ] Build `mutator.py` to mutate reasoner prompt + logic
- [ ] Add mutation scoring based on test_suite.json

### DAY 7: Autonomous Loop
- [ ] Integrate all modules
- [ ] Run 3 full evolution cycles
- [ ] Output benchmark improvements

---

## 🏁 FINAL GOAL:
Working prototype of Loop AGI Core with:
- Real intelligence score tracking
- Measurable evolution logs
- Self-improving logic and memory
- Tool-assisted autonomous problem-solving

🎯 **Stay honest. Stay scientific. Stay ruthless.**

