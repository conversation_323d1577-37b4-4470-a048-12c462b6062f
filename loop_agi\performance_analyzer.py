#!/usr/bin/env python3
"""
LOOP AGI - Advanced Performance Analysis System
Comprehensive performance tracking, analysis, and optimization
"""

import json
import csv
import time
import datetime
import statistics
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class PerformanceAnalyzer:
    """Advanced performance analysis and optimization system"""
    
    def __init__(self):
        self.performance_history = []
        self.baseline_metrics = None
        self.improvement_targets = {
            'intelligence_multiplier': 4.0,
            'safety_threshold': 0.95,
            'efficiency_target': 0.8,
            'quality_target': 0.85
        }
        
        # Load existing performance data
        self._load_performance_history()
        
    def _load_performance_history(self):
        """Load existing performance data from CSV"""
        csv_path = Path('benchmarks/performance.csv')
        if csv_path.exists():
            with open(csv_path, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        self.performance_history.append({
                            'timestamp': row['timestamp'],
                            'cycle': int(row['cycle']),
                            'intelligence_score': float(row['intelligence_score']),
                            'safety_score': float(row['safety_score']),
                            'efficiency_score': float(row['efficiency_score'])
                        })
                    except (ValueError, KeyError):
                        continue
        
        # Set baseline if we have data
        if self.performance_history:
            first_entry = self.performance_history[0]
            self.baseline_metrics = {
                'intelligence': first_entry['intelligence_score'],
                'safety': first_entry['safety_score'],
                'efficiency': first_entry['efficiency_score']
            }
    
    def analyze_performance_trends(self, window_size: int = 10) -> Dict[str, Any]:
        """Analyze performance trends over time"""
        
        if len(self.performance_history) < 2:
            return {'status': 'insufficient_data', 'message': 'Need at least 2 data points'}
        
        recent_data = self.performance_history[-window_size:]
        
        analysis = {
            'timestamp': datetime.datetime.now().isoformat(),
            'window_size': len(recent_data),
            'trends': {},
            'statistics': {},
            'predictions': {},
            'recommendations': []
        }
        
        # Analyze trends for each metric
        metrics = ['intelligence_score', 'safety_score', 'efficiency_score']
        
        for metric in metrics:
            values = [entry[metric] for entry in recent_data]
            
            # Calculate trend
            if len(values) >= 2:
                # Simple linear trend
                x = list(range(len(values)))
                trend_slope = self._calculate_trend_slope(x, values)
                
                # Statistical analysis
                analysis['trends'][metric] = {
                    'slope': trend_slope,
                    'direction': 'improving' if trend_slope > 0 else 'declining' if trend_slope < 0 else 'stable',
                    'current_value': values[-1],
                    'change_from_previous': values[-1] - values[-2] if len(values) >= 2 else 0,
                    'volatility': statistics.stdev(values) if len(values) > 1 else 0
                }
                
                analysis['statistics'][metric] = {
                    'mean': statistics.mean(values),
                    'median': statistics.median(values),
                    'min': min(values),
                    'max': max(values),
                    'std_dev': statistics.stdev(values) if len(values) > 1 else 0
                }
                
                # Predict next value
                if len(values) >= 3:
                    predicted_next = self._predict_next_value(values)
                    analysis['predictions'][metric] = {
                        'predicted_value': predicted_next,
                        'confidence': self._calculate_prediction_confidence(values)
                    }
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_performance_recommendations(analysis)
        
        # Calculate overall performance score
        analysis['overall_performance'] = self._calculate_overall_performance(analysis)
        
        return analysis
    
    def _calculate_trend_slope(self, x: List[float], y: List[float]) -> float:
        """Calculate linear trend slope using least squares"""
        n = len(x)
        if n < 2:
            return 0.0
        
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        denominator = n * sum_x2 - sum_x ** 2
        if denominator == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope
    
    def _predict_next_value(self, values: List[float]) -> float:
        """Predict next value using simple trend analysis"""
        if len(values) < 3:
            return values[-1]
        
        # Use last 3 values for prediction
        recent = values[-3:]
        x = [0, 1, 2]
        slope = self._calculate_trend_slope(x, recent)
        
        # Predict next value
        predicted = recent[-1] + slope
        
        # Clamp to reasonable bounds
        return max(0.0, min(1.0, predicted))
    
    def _calculate_prediction_confidence(self, values: List[float]) -> float:
        """Calculate confidence in prediction based on trend stability"""
        if len(values) < 3:
            return 0.5
        
        # Calculate how consistent the trend has been
        recent_slopes = []
        for i in range(len(values) - 2):
            window = values[i:i+3]
            x = [0, 1, 2]
            slope = self._calculate_trend_slope(x, window)
            recent_slopes.append(slope)
        
        if not recent_slopes:
            return 0.5
        
        # Confidence based on slope consistency
        slope_std = statistics.stdev(recent_slopes) if len(recent_slopes) > 1 else 0
        confidence = max(0.1, min(1.0, 1.0 - slope_std))
        
        return confidence
    
    def _generate_performance_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        trends = analysis.get('trends', {})
        
        # Intelligence recommendations
        if 'intelligence_score' in trends:
            intel_trend = trends['intelligence_score']
            if intel_trend['direction'] == 'declining':
                recommendations.append("Intelligence declining - focus on reasoning module generation")
            elif intel_trend['current_value'] < 0.7:
                recommendations.append("Intelligence below target - implement advanced reasoning algorithms")
            elif intel_trend['volatility'] > 0.2:
                recommendations.append("Intelligence unstable - improve consistency in reasoning modules")
        
        # Safety recommendations
        if 'safety_score' in trends:
            safety_trend = trends['safety_score']
            if safety_trend['current_value'] < 0.95:
                recommendations.append("Safety score below threshold - enhance validation systems")
            elif safety_trend['direction'] == 'declining':
                recommendations.append("Safety declining - review and strengthen safety protocols")
        
        # Efficiency recommendations
        if 'efficiency_score' in trends:
            eff_trend = trends['efficiency_score']
            if eff_trend['direction'] == 'declining':
                recommendations.append("Efficiency declining - implement resource optimization modules")
            elif eff_trend['current_value'] < 0.6:
                recommendations.append("Low efficiency - focus on performance optimization")
        
        return recommendations
    
    def _calculate_overall_performance(self, analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall performance metrics"""
        trends = analysis.get('trends', {})
        
        if not trends:
            return {'score': 0.0, 'grade': 'F'}
        
        # Weight the different metrics
        weights = {
            'intelligence_score': 0.4,
            'safety_score': 0.4,
            'efficiency_score': 0.2
        }
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for metric, weight in weights.items():
            if metric in trends:
                current_value = trends[metric]['current_value']
                weighted_score += current_value * weight
                total_weight += weight
        
        if total_weight > 0:
            overall_score = weighted_score / total_weight
        else:
            overall_score = 0.0
        
        # Calculate grade
        if overall_score >= 0.9:
            grade = 'A'
        elif overall_score >= 0.8:
            grade = 'B'
        elif overall_score >= 0.7:
            grade = 'C'
        elif overall_score >= 0.6:
            grade = 'D'
        else:
            grade = 'F'
        
        return {
            'score': overall_score,
            'grade': grade,
            'improvement_needed': max(0, self.improvement_targets['intelligence_multiplier'] - overall_score)
        }
    
    def calculate_intelligence_multiplier(self) -> float:
        """Calculate current intelligence multiplier from baseline"""
        if not self.baseline_metrics or not self.performance_history:
            return 1.0
        
        current_intelligence = self.performance_history[-1]['intelligence_score']
        baseline_intelligence = self.baseline_metrics['intelligence']
        
        if baseline_intelligence <= 0:
            return 1.0
        
        multiplier = current_intelligence / baseline_intelligence
        return max(1.0, multiplier)
    
    def auto_score_reasoning_quality(self, reasoning_chain: List[str], 
                                   conclusion: str, 
                                   evidence: List[str] = None) -> Dict[str, float]:
        """Automatically score reasoning quality"""
        
        scores = {
            'logical_consistency': 0.0,
            'evidence_support': 0.0,
            'chain_completeness': 0.0,
            'conclusion_validity': 0.0,
            'overall_quality': 0.0
        }
        
        # Score logical consistency
        if reasoning_chain:
            # Check for logical connectors
            logical_words = ['therefore', 'because', 'since', 'thus', 'hence', 'consequently']
            logical_count = sum(1 for step in reasoning_chain 
                              for word in logical_words 
                              if word in step.lower())
            scores['logical_consistency'] = min(1.0, logical_count / len(reasoning_chain))
        
        # Score evidence support
        if evidence:
            evidence_words = set(word.lower() for item in evidence for word in item.split())
            reasoning_words = set(word.lower() for step in reasoning_chain for word in step.split())
            overlap = len(evidence_words.intersection(reasoning_words))
            scores['evidence_support'] = min(1.0, overlap / max(1, len(evidence_words)))
        else:
            scores['evidence_support'] = 0.5  # Neutral if no evidence provided
        
        # Score chain completeness
        if reasoning_chain:
            # Heuristic: longer chains with more steps are more complete
            completeness = min(1.0, len(reasoning_chain) / 5)  # Optimal around 5 steps
            scores['chain_completeness'] = completeness
        
        # Score conclusion validity
        if conclusion and reasoning_chain:
            # Check if conclusion follows from reasoning
            reasoning_text = ' '.join(reasoning_chain).lower()
            conclusion_words = set(conclusion.lower().split())
            reasoning_words = set(reasoning_text.split())
            relevance = len(conclusion_words.intersection(reasoning_words)) / max(1, len(conclusion_words))
            scores['conclusion_validity'] = min(1.0, relevance)
        
        # Calculate overall quality
        scores['overall_quality'] = statistics.mean([
            scores['logical_consistency'],
            scores['evidence_support'],
            scores['chain_completeness'],
            scores['conclusion_validity']
        ])
        
        return scores
    
    def auto_score_code_quality(self, code: str, module_type: str = 'general') -> Dict[str, float]:
        """Automatically score code quality"""
        
        scores = {
            'readability': 0.0,
            'documentation': 0.0,
            'complexity': 0.0,
            'safety': 0.0,
            'functionality': 0.0,
            'overall_quality': 0.0
        }
        
        lines = code.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        # Score readability
        if non_empty_lines:
            # Check for meaningful variable names, proper spacing, etc.
            readable_indicators = 0
            for line in non_empty_lines:
                if '=' in line and not line.strip().startswith('#'):
                    # Check for descriptive variable names
                    if any(len(word) > 3 for word in line.split() if word.isalpha()):
                        readable_indicators += 1
            
            scores['readability'] = min(1.0, readable_indicators / max(1, len(non_empty_lines) * 0.3))
        
        # Score documentation
        comment_lines = sum(1 for line in lines if line.strip().startswith('#') or '"""' in line)
        docstring_lines = sum(1 for line in lines if '"""' in line or "'''" in line)
        
        doc_ratio = (comment_lines + docstring_lines * 2) / max(1, len(non_empty_lines))
        scores['documentation'] = min(1.0, doc_ratio)
        
        # Score complexity (inverse - simpler is better for maintainability)
        complexity_indicators = code.count('if ') + code.count('for ') + code.count('while ') + code.count('try:')
        complexity_ratio = complexity_indicators / max(1, len(non_empty_lines))
        scores['complexity'] = max(0.0, 1.0 - complexity_ratio)
        
        # Score safety (basic checks)
        dangerous_patterns = ['eval(', 'exec(', 'os.system', '__import__']
        safety_violations = sum(1 for pattern in dangerous_patterns if pattern in code)
        scores['safety'] = max(0.0, 1.0 - safety_violations * 0.5)
        
        # Score functionality (basic structure checks)
        has_functions = 'def ' in code
        has_classes = 'class ' in code
        has_error_handling = 'try:' in code or 'except' in code
        
        functionality_score = 0.0
        if has_functions:
            functionality_score += 0.4
        if has_classes:
            functionality_score += 0.3
        if has_error_handling:
            functionality_score += 0.3
        
        scores['functionality'] = functionality_score
        
        # Calculate overall quality
        scores['overall_quality'] = statistics.mean([
            scores['readability'],
            scores['documentation'],
            scores['complexity'],
            scores['safety'],
            scores['functionality']
        ])
        
        return scores
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        
        report = {
            'timestamp': datetime.datetime.now().isoformat(),
            'summary': {},
            'trends': {},
            'intelligence_multiplier': self.calculate_intelligence_multiplier(),
            'recommendations': [],
            'targets': self.improvement_targets
        }
        
        # Get trend analysis
        trend_analysis = self.analyze_performance_trends()
        report['trends'] = trend_analysis
        
        # Summary statistics
        if self.performance_history:
            latest = self.performance_history[-1]
            report['summary'] = {
                'current_cycle': latest['cycle'],
                'current_intelligence': latest['intelligence_score'],
                'current_safety': latest['safety_score'],
                'current_efficiency': latest['efficiency_score'],
                'total_cycles': len(self.performance_history)
            }
        
        # Overall assessment
        if 'overall_performance' in trend_analysis:
            report['overall_assessment'] = trend_analysis['overall_performance']
        
        # Recommendations
        if 'recommendations' in trend_analysis:
            report['recommendations'] = trend_analysis['recommendations']
        
        return report
    
    def save_performance_report(self, report: Dict[str, Any]):
        """Save performance report to file"""
        
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = Path(f'benchmarks/performance_report_{timestamp}.json')
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Also save latest report
        with open('benchmarks/latest_performance_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)

# Module interface
def create_performance_analyzer():
    """Factory function to create PerformanceAnalyzer instance"""
    return PerformanceAnalyzer()
