#!/usr/bin/env python3
"""
Basic inference example for Loop 7B SW.

This example demonstrates how to use the streaming inference system
to generate text with ultra-low memory usage.
"""

import sys
import os

# Add src to path for development
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from loop_7b_sw import StreamingInference


def main():
    """Basic inference example"""
    
    print("🧬 Loop 7B SW - Basic Inference Example")
    print("=" * 50)
    
    # Model path (adjust to your model location)
    model_path = "../../downloaded_models/mistral-7b-v0.1"
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found at: {model_path}")
        print("Please download Mistral 7B and update the model_path variable")
        return
    
    try:
        # Initialize the streaming inference system
        print("🚀 Initializing streaming inference...")
        model = StreamingInference(model_path)
        
        # Test prompts
        test_prompts = [
            "The future of artificial intelligence is",
            "Machine learning will help us",
            "Technology advances when we"
        ]
        
        print(f"\n🧪 Running inference on {len(test_prompts)} prompts...")
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n--- Test {i}/{len(test_prompts)} ---")
            print(f"Prompt: '{prompt}'")
            
            # Generate text
            result = model.generate(prompt, max_tokens=8)
            
            if result['success']:
                print(f"✅ Generated: '{result['generated_text']}'")
                print(f"   Tokens: {result['tokens_generated']}")
                print(f"   Speed: {result['tokens_per_second']:.1f} tok/s")
                print(f"   Memory: {result['peak_memory_mb']:.1f}MB")
                print(f"   Under 8GB: {'✅' if result['memory_under_8gb'] else '❌'}")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
        
        # Show overall performance
        stats = model.get_performance_stats()
        print(f"\n📊 Overall Performance:")
        print(f"   Total inferences: {stats['total_inferences']}")
        print(f"   Total tokens: {stats['total_tokens_generated']}")
        print(f"   Average speed: {stats['average_tokens_per_second']:.1f} tokens/sec")
        print(f"   Peak memory: {stats['peak_memory_mb']:.1f}MB")
        
        print(f"\n🎉 Example complete! Memory usage stayed under 8GB limit.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
