"""
Test script for the Strategy Agent.
"""
import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from financial_agent.agents.strategy_agent import StrategyAgent, StrategyType, SignalType
from financial_agent.agents.analysis_agent import AnalysisResult, Indicator, IndicatorType
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'strategy_agent_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Mock OHLCV data for testing
def generate_mock_ohlcv(days: int = 250) -> dict:
    """Generate mock OHLCV data for testing."""
    dates = pd.date_range(end=datetime.now(), periods=days)
    base_price = 100.0
    
    # Generate random walk for prices
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, days)
    prices = base_price * (1 + returns).cumprod()
    
    # Generate OHLC data with some randomness
    df = pd.DataFrame({
        'open': prices * 0.995 + np.random.normal(0, 0.1, days),
        'high': prices * 1.005 + np.abs(np.random.normal(0, 0.1, days)),
        'low': prices * 0.995 - np.abs(np.random.normal(0, 0.1, days)),
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, days)
    }, index=dates)
    
    # Ensure high > low
    df['high'] = df[['high', 'low']].max(axis=1)
    df['low'] = df[['high', 'low']].min(axis=1)
    
    # Ensure high >= open, close and low <= open, close
    df['high'] = df[['high', 'open', 'close']].max(axis=1)
    df['low'] = df[['low', 'open', 'close']].min(axis=1)
    
    return df

def calculate_technical_indicators(df: pd.DataFrame) -> dict:
    """Calculate technical indicators for the mock data."""
    # Simple Moving Averages
    df['MA_50'] = df['close'].rolling(window=50).mean()
    df['MA_200'] = df['close'].rolling(window=200).mean()
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # MACD
    exp1 = df['close'].ewm(span=12, adjust=False).mean()
    exp2 = df['close'].ewm(span=26, adjust=False).mean()
    df['MACD'] = exp1 - exp2
    df['MACD_Signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
    
    # Bollinger Bands
    df['BB_Middle'] = df['close'].rolling(window=20).mean()
    df['BB_Upper'] = df['BB_Middle'] + 2 * df['close'].rolling(window=20).std()
    df['BB_Lower'] = df['BB_Middle'] - 2 * df['close'].rolling(window=20).std()
    
    return df

def create_mock_analysis_result(symbol: str, df: pd.DataFrame) -> AnalysisResult:
    """Create a mock analysis result from OHLCV data."""
    # Get the latest values
    latest = df.iloc[-1]
    
    # Create indicators
    indicators = [
        Indicator(
            name='RSI',
            value=latest['RSI'],
            indicator_type=IndicatorType.MOMENTUM,
            metadata={'overbought': 70, 'oversold': 30}
        ),
        Indicator(
            name='MACD',
            value=latest['MACD'],
            indicator_type=IndicatorType.TREND,
            metadata={'signal': latest['MACD_Signal']}
        ),
        Indicator(
            name='MA_50',
            value=latest['MA_50'],
            indicator_type=IndicatorType.TREND,
            metadata={'period': 50}
        ),
        Indicator(
            name='MA_200',
            value=latest['MA_200'],
            indicator_type=IndicatorType.TREND,
            metadata={'period': 200}
        )
    ]
    
    # Determine signal based on indicators
    signal_type = SignalType.HOLD
    confidence = 0.5
    
    # Simple signal logic for testing
    if latest['close'] > latest['MA_50'] > latest['MA_200'] and latest['MACD'] > latest['MACD_Signal']:
        signal_type = SignalType.BUY
        confidence = 0.7
    elif latest['close'] < latest['MA_50'] < latest['MA_200'] and latest['MACD'] < latest['MACD_Signal']:
        signal_type = SignalType.SELL
        confidence = 0.7
    
    # Create analysis result
    return AnalysisResult(
        symbol=symbol,
        signal=signal_type,
        confidence=confidence,
        indicators=indicators,
        timestamp=int(datetime.now().timestamp()),
        metadata={
            'close_prices': df['close'].tolist(),
            'bb_upper': df['BB_Upper'].tolist(),
            'bb_lower': df['BB_Lower'].tolist(),
            'ma_50': df['MA_50'].tolist(),
            'ma_200': df['MA_200'].tolist(),
            'macd_signal': df['MACD_Signal'].tolist()
        }
    )

async def test_strategy_agent():
    """Test the Strategy Agent with mock data."""
    logger.info("Starting Strategy Agent test...")
    
    # Initialize mock LLM wrapper
    llm_wrapper = MistralWrapper()
    
    # Initialize strategy agent
    strategy_agent = StrategyAgent(llm_wrapper=llm_wrapper, name='strategy_test')
    
    # Start the agent
    await strategy_agent.start()
    
    try:
        # Generate mock data for multiple symbols
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META']
        test_cases = []
        
        logger.info("Generating test cases...")
        for symbol in symbols:
            # Generate mock OHLCV data
            df = generate_mock_ohlcv()
            
            # Calculate technical indicators
            df = calculate_technical_indicators(df)
            
            # Create analysis result
            analysis_result = create_mock_analysis_result(symbol, df)
            
            test_cases.append({
                'symbol': symbol,
                'analysis': analysis_result,
                'current_price': df['close'].iloc[-1]
            })
        
        # Test each symbol
        logger.info("\n" + "="*80)
        logger.info("TESTING STRATEGY AGENT")
        logger.info("="*80)
        
        for i, test_case in enumerate(test_cases, 1):
            symbol = test_case['symbol']
            logger.info(f"\n{i}. Testing {symbol}...")
            
            # Process analysis result
            response = await strategy_agent.process({
                'analysis': test_case['analysis'],
                'current_price': test_case['current_price'],
                'portfolio_value': 100000.0  # $100k portfolio
            })
            
            if not response.success:
                logger.error(f"Error processing {symbol}: {response.error}")
                continue
            
            # Log the results
            data = response.data
            best_signal = data.get('best_signal')
            
            if best_signal and best_signal['signal'] != 'NO_SIGNAL':
                logger.info(f"  🚀 {symbol} - {best_signal['strategy']} - {best_signal['signal']}")
                logger.info(f"  Entry: ${best_signal['entry_price']:.2f}")
                logger.info(f"  Stop Loss: ${best_signal['stop_loss']:.2f} ({(best_signal['stop_loss']/best_signal['entry_price']-1)*100:.1f}%)")
                logger.info(f"  Take Profit: ${best_signal['take_profit']:.2f} ({(best_signal['take_profit']/best_signal['entry_price']-1)*100:.1f}%)")
                logger.info(f"  Position Size: {best_signal['size_percent']*100:.1f}%")
                logger.info(f"  Confidence: {best_signal['confidence']*100:.1f}%")
            else:
                logger.info(f"  ⏭️  {symbol} - No trading signal generated")
        
        logger.info("\nTest completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}", exc_info=True)
    finally:
        # Stop the agent
        await strategy_agent.stop()

if __name__ == "__main__":
    asyncio.run(test_strategy_agent())
