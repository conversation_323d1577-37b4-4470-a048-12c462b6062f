#!/usr/bin/env python3
"""
EVALUATION FRAMEWORK
===================

Comprehensive evaluation framework for compressed transformer models.
Includes accuracy measurement, benchmark testing, and performance analysis.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import numpy as np
import logging
import json
import time
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
from tqdm import tqdm
import math
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns

from transformer_compression_system import CompressionConfig, MemoryMonitor, TransformerCompressionError

logger = logging.getLogger(__name__)

@dataclass
class EvaluationMetrics:
    """Container for evaluation metrics"""
    accuracy: float
    perplexity: float
    bleu_score: Optional[float]
    rouge_scores: Optional[Dict[str, float]]
    inference_time: float
    memory_usage: float
    compression_ratio: float
    reconstruction_error: float

class BenchmarkDataset(Dataset):
    """Dataset for benchmark evaluation"""
    
    def __init__(self, data_path: str, tokenizer, max_length: int = 512, task_type: str = "language_modeling"):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.task_type = task_type
        
        # Load data based on task type
        if task_type == "language_modeling":
            self.data = self._load_language_modeling_data(data_path)
        elif task_type == "classification":
            self.data = self._load_classification_data(data_path)
        elif task_type == "question_answering":
            self.data = self._load_qa_data(data_path)
        else:
            raise ValueError(f"Unsupported task type: {task_type}")
    
    def _load_language_modeling_data(self, data_path: str) -> List[Dict[str, Any]]:
        """Load language modeling data"""
        data = []
        try:
            with open(data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        data.append({'text': line.strip()})
        except Exception as e:
            logger.warning(f"Failed to load data from {data_path}: {e}")
            # Create dummy data for testing
            data = [{'text': f"This is sample text number {i}."} for i in range(100)]
        
        return data
    
    def _load_classification_data(self, data_path: str) -> List[Dict[str, Any]]:
        """Load classification data"""
        # Placeholder implementation
        return [
            {'text': f"Sample classification text {i}", 'label': i % 2}
            for i in range(100)
        ]
    
    def _load_qa_data(self, data_path: str) -> List[Dict[str, Any]]:
        """Load question-answering data"""
        # Placeholder implementation
        return [
            {
                'question': f"What is question {i}?",
                'context': f"This is context for question {i}.",
                'answer': f"Answer {i}"
            }
            for i in range(100)
        ]
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        if self.task_type == "language_modeling":
            encoding = self.tokenizer(
                item['text'],
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
            return {
                'input_ids': encoding['input_ids'].squeeze(),
                'attention_mask': encoding['attention_mask'].squeeze(),
                'labels': encoding['input_ids'].squeeze()
            }
        
        elif self.task_type == "classification":
            encoding = self.tokenizer(
                item['text'],
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
            return {
                'input_ids': encoding['input_ids'].squeeze(),
                'attention_mask': encoding['attention_mask'].squeeze(),
                'labels': torch.tensor(item['label'], dtype=torch.long)
            }
        
        else:  # question_answering
            # Combine question and context
            text = f"{item['question']} {item['context']}"
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )
            return {
                'input_ids': encoding['input_ids'].squeeze(),
                'attention_mask': encoding['attention_mask'].squeeze(),
                'answer': item['answer']
            }

class ModelEvaluator:
    """Comprehensive model evaluation framework"""
    
    def __init__(self, config: CompressionConfig):
        self.config = config
        self.memory_monitor = MemoryMonitor(config.max_memory_gb)
        self.evaluation_results = {}
    
    def evaluate_model(self, model: nn.Module, dataloader: DataLoader, 
                      original_model: Optional[nn.Module] = None,
                      task_type: str = "language_modeling") -> EvaluationMetrics:
        """
        Comprehensive model evaluation
        
        Args:
            model: Model to evaluate
            dataloader: Evaluation data loader
            original_model: Original uncompressed model for comparison
            task_type: Type of task for evaluation
            
        Returns:
            EvaluationMetrics object with all metrics
        """
        try:
            logger.info(f"Starting evaluation for {task_type} task")
            
            model.eval()
            device = next(model.parameters()).device
            
            # Initialize metrics
            total_loss = 0.0
            total_samples = 0
            correct_predictions = 0
            inference_times = []
            memory_usages = []
            
            # Evaluation loop
            with torch.no_grad():
                with tqdm(dataloader, desc="Evaluating") as pbar:
                    for batch_idx, batch in enumerate(pbar):
                        try:
                            # Move batch to device
                            input_ids = batch['input_ids'].to(device)
                            attention_mask = batch['attention_mask'].to(device)
                            
                            # Memory monitoring
                            memory_before = self.memory_monitor.check_memory(f"eval batch {batch_idx}")
                            
                            # Inference timing
                            start_time = time.time()
                            
                            # Forward pass
                            outputs = model(input_ids, attention_mask)
                            
                            end_time = time.time()
                            inference_time = end_time - start_time
                            inference_times.append(inference_time)
                            
                            # Memory after inference
                            memory_after = self.memory_monitor.check_memory()
                            memory_usages.append(memory_after - memory_before)
                            
                            # Calculate task-specific metrics
                            if task_type == "language_modeling":
                                labels = batch['labels'].to(device)
                                loss = F.cross_entropy(
                                    outputs.view(-1, outputs.size(-1)),
                                    labels.view(-1),
                                    ignore_index=-100
                                )
                                total_loss += loss.item()
                                
                                # Calculate perplexity
                                predictions = torch.argmax(outputs, dim=-1)
                                correct_predictions += (predictions == labels).sum().item()
                                
                            elif task_type == "classification":
                                labels = batch['labels'].to(device)
                                # Use mean pooling for classification
                                pooled_output = outputs.mean(dim=1)
                                loss = F.cross_entropy(pooled_output, labels)
                                total_loss += loss.item()
                                
                                predictions = torch.argmax(pooled_output, dim=-1)
                                correct_predictions += (predictions == labels).sum().item()
                            
                            total_samples += input_ids.size(0)
                            
                            # Update progress
                            pbar.set_postfix({
                                'loss': total_loss / (batch_idx + 1),
                                'acc': correct_predictions / max(total_samples, 1)
                            })
                            
                        except Exception as e:
                            logger.warning(f"Evaluation batch {batch_idx} failed: {e}")
                            continue
            
            # Calculate final metrics
            avg_loss = total_loss / len(dataloader)
            accuracy = correct_predictions / max(total_samples, 1)
            perplexity = math.exp(avg_loss) if avg_loss < 10 else float('inf')
            avg_inference_time = np.mean(inference_times) if inference_times else 0.0
            avg_memory_usage = np.mean(memory_usages) if memory_usages else 0.0
            
            # Calculate compression metrics
            compression_metrics = self._calculate_compression_metrics(model, original_model)
            
            # Calculate reconstruction error if original model provided
            reconstruction_error = 0.0
            if original_model is not None:
                reconstruction_error = self._calculate_reconstruction_error(model, original_model, dataloader)
            
            # Create evaluation metrics
            metrics = EvaluationMetrics(
                accuracy=accuracy,
                perplexity=perplexity,
                bleu_score=None,  # TODO: Implement for generation tasks
                rouge_scores=None,  # TODO: Implement for summarization tasks
                inference_time=avg_inference_time,
                memory_usage=avg_memory_usage,
                compression_ratio=compression_metrics['compression_ratio'],
                reconstruction_error=reconstruction_error
            )
            
            logger.info(f"Evaluation completed:")
            logger.info(f"  Accuracy: {accuracy:.4f}")
            logger.info(f"  Perplexity: {perplexity:.2f}")
            logger.info(f"  Avg Inference Time: {avg_inference_time:.4f}s")
            logger.info(f"  Compression Ratio: {compression_metrics['compression_ratio']:.2f}×")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Model evaluation failed: {e}")
            raise TransformerCompressionError(f"Model evaluation failed: {e}")
    
    def _calculate_compression_metrics(self, model: nn.Module, original_model: Optional[nn.Module]) -> Dict[str, float]:
        """Calculate compression metrics"""
        
        model_params = sum(p.numel() for p in model.parameters())
        model_size = sum(p.numel() * p.element_size() for p in model.parameters())
        
        if original_model is not None:
            original_params = sum(p.numel() for p in original_model.parameters())
            original_size = sum(p.numel() * p.element_size() for p in original_model.parameters())
            
            param_compression = original_params / model_params
            size_compression = original_size / model_size
        else:
            param_compression = 1.0
            size_compression = 1.0
        
        return {
            'model_params': model_params,
            'model_size_mb': model_size / (1024 * 1024),
            'compression_ratio': size_compression,
            'param_compression_ratio': param_compression
        }
    
    def _calculate_reconstruction_error(self, compressed_model: nn.Module, 
                                      original_model: nn.Module, 
                                      dataloader: DataLoader) -> float:
        """Calculate reconstruction error between compressed and original models"""
        
        try:
            compressed_model.eval()
            original_model.eval()
            
            total_error = 0.0
            num_samples = 0
            
            with torch.no_grad():
                for batch_idx, batch in enumerate(dataloader):
                    if batch_idx >= 10:  # Limit to first 10 batches for efficiency
                        break
                    
                    device = next(compressed_model.parameters()).device
                    input_ids = batch['input_ids'].to(device)
                    attention_mask = batch['attention_mask'].to(device)
                    
                    # Get outputs from both models
                    compressed_outputs = compressed_model(input_ids, attention_mask)
                    original_outputs = original_model(input_ids, attention_mask)
                    
                    # Calculate MSE between outputs
                    error = F.mse_loss(compressed_outputs, original_outputs)
                    total_error += error.item()
                    num_samples += 1
            
            return total_error / max(num_samples, 1)
            
        except Exception as e:
            logger.warning(f"Reconstruction error calculation failed: {e}")
            return 0.0
    
    def benchmark_suite(self, model: nn.Module, benchmark_configs: List[Dict[str, Any]],
                       original_model: Optional[nn.Module] = None) -> Dict[str, EvaluationMetrics]:
        """
        Run comprehensive benchmark suite
        
        Args:
            model: Model to benchmark
            benchmark_configs: List of benchmark configurations
            original_model: Original model for comparison
            
        Returns:
            Dictionary of benchmark results
        """
        
        results = {}
        
        for config in benchmark_configs:
            try:
                logger.info(f"Running benchmark: {config['name']}")
                
                # Create dataset and dataloader
                dataset = BenchmarkDataset(
                    data_path=config.get('data_path', ''),
                    tokenizer=config['tokenizer'],
                    max_length=config.get('max_length', 512),
                    task_type=config.get('task_type', 'language_modeling')
                )
                
                dataloader = DataLoader(
                    dataset,
                    batch_size=config.get('batch_size', 8),
                    shuffle=False,
                    num_workers=0  # Avoid multiprocessing issues
                )
                
                # Run evaluation
                metrics = self.evaluate_model(
                    model=model,
                    dataloader=dataloader,
                    original_model=original_model,
                    task_type=config.get('task_type', 'language_modeling')
                )
                
                results[config['name']] = metrics
                
            except Exception as e:
                logger.error(f"Benchmark {config['name']} failed: {e}")
                continue
        
        return results
    
    def generate_evaluation_report(self, results: Dict[str, EvaluationMetrics], 
                                 output_path: str = "evaluation_report.json") -> None:
        """Generate comprehensive evaluation report"""
        
        try:
            # Convert metrics to serializable format
            report_data = {}
            
            for benchmark_name, metrics in results.items():
                report_data[benchmark_name] = {
                    'accuracy': metrics.accuracy,
                    'perplexity': metrics.perplexity,
                    'bleu_score': metrics.bleu_score,
                    'rouge_scores': metrics.rouge_scores,
                    'inference_time': metrics.inference_time,
                    'memory_usage': metrics.memory_usage,
                    'compression_ratio': metrics.compression_ratio,
                    'reconstruction_error': metrics.reconstruction_error
                }
            
            # Add summary statistics
            accuracies = [m.accuracy for m in results.values()]
            compression_ratios = [m.compression_ratio for m in results.values()]
            inference_times = [m.inference_time for m in results.values()]
            
            report_data['summary'] = {
                'num_benchmarks': len(results),
                'avg_accuracy': np.mean(accuracies) if accuracies else 0.0,
                'min_accuracy': np.min(accuracies) if accuracies else 0.0,
                'max_accuracy': np.max(accuracies) if accuracies else 0.0,
                'avg_compression_ratio': np.mean(compression_ratios) if compression_ratios else 0.0,
                'avg_inference_time': np.mean(inference_times) if inference_times else 0.0,
                'memory_stats': self.memory_monitor.get_memory_stats()
            }
            
            # Save report
            with open(output_path, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            logger.info(f"Evaluation report saved to {output_path}")
            
            # Print summary
            print("\n" + "="*60)
            print("EVALUATION SUMMARY")
            print("="*60)
            print(f"Benchmarks run: {len(results)}")
            print(f"Average accuracy: {np.mean(accuracies):.4f}" if accuracies else "No accuracy data")
            print(f"Average compression: {np.mean(compression_ratios):.2f}×" if compression_ratios else "No compression data")
            print(f"Average inference time: {np.mean(inference_times):.4f}s" if inference_times else "No timing data")
            print("="*60)
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
    
    def plot_evaluation_results(self, results: Dict[str, EvaluationMetrics], 
                              output_dir: str = "evaluation_plots") -> None:
        """Generate evaluation plots"""
        
        try:
            Path(output_dir).mkdir(exist_ok=True)
            
            # Extract data for plotting
            benchmark_names = list(results.keys())
            accuracies = [results[name].accuracy for name in benchmark_names]
            compression_ratios = [results[name].compression_ratio for name in benchmark_names]
            inference_times = [results[name].inference_time for name in benchmark_names]
            
            # Plot 1: Accuracy vs Compression Ratio
            plt.figure(figsize=(10, 6))
            plt.scatter(compression_ratios, accuracies, s=100, alpha=0.7)
            for i, name in enumerate(benchmark_names):
                plt.annotate(name, (compression_ratios[i], accuracies[i]), 
                           xytext=(5, 5), textcoords='offset points')
            plt.xlabel('Compression Ratio')
            plt.ylabel('Accuracy')
            plt.title('Accuracy vs Compression Ratio')
            plt.grid(True, alpha=0.3)
            plt.savefig(f"{output_dir}/accuracy_vs_compression.png", dpi=300, bbox_inches='tight')
            plt.close()
            
            # Plot 2: Inference Time Comparison
            plt.figure(figsize=(12, 6))
            plt.bar(benchmark_names, inference_times)
            plt.xlabel('Benchmark')
            plt.ylabel('Inference Time (seconds)')
            plt.title('Inference Time by Benchmark')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f"{output_dir}/inference_times.png", dpi=300, bbox_inches='tight')
            plt.close()
            
            # Plot 3: Performance Summary
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Accuracy
            axes[0, 0].bar(benchmark_names, accuracies)
            axes[0, 0].set_title('Accuracy by Benchmark')
            axes[0, 0].set_ylabel('Accuracy')
            axes[0, 0].tick_params(axis='x', rotation=45)
            
            # Compression Ratio
            axes[0, 1].bar(benchmark_names, compression_ratios)
            axes[0, 1].set_title('Compression Ratio by Benchmark')
            axes[0, 1].set_ylabel('Compression Ratio')
            axes[0, 1].tick_params(axis='x', rotation=45)
            
            # Inference Time
            axes[1, 0].bar(benchmark_names, inference_times)
            axes[1, 0].set_title('Inference Time by Benchmark')
            axes[1, 0].set_ylabel('Time (seconds)')
            axes[1, 0].tick_params(axis='x', rotation=45)
            
            # Efficiency (Accuracy / Inference Time)
            efficiency = [acc / max(time, 1e-6) for acc, time in zip(accuracies, inference_times)]
            axes[1, 1].bar(benchmark_names, efficiency)
            axes[1, 1].set_title('Efficiency (Accuracy/Time)')
            axes[1, 1].set_ylabel('Efficiency')
            axes[1, 1].tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            plt.savefig(f"{output_dir}/performance_summary.png", dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Evaluation plots saved to {output_dir}/")
            
        except Exception as e:
            logger.warning(f"Plot generation failed: {e}")

# Utility functions for creating benchmark configurations
def create_language_modeling_benchmark(name: str, data_path: str, tokenizer) -> Dict[str, Any]:
    """Create language modeling benchmark configuration"""
    return {
        'name': name,
        'task_type': 'language_modeling',
        'data_path': data_path,
        'tokenizer': tokenizer,
        'max_length': 512,
        'batch_size': 8
    }

def create_classification_benchmark(name: str, data_path: str, tokenizer) -> Dict[str, Any]:
    """Create classification benchmark configuration"""
    return {
        'name': name,
        'task_type': 'classification',
        'data_path': data_path,
        'tokenizer': tokenizer,
        'max_length': 256,
        'batch_size': 16
    }

def create_qa_benchmark(name: str, data_path: str, tokenizer) -> Dict[str, Any]:
    """Create question-answering benchmark configuration"""
    return {
        'name': name,
        'task_type': 'question_answering',
        'data_path': data_path,
        'tokenizer': tokenizer,
        'max_length': 384,
        'batch_size': 8
    }
