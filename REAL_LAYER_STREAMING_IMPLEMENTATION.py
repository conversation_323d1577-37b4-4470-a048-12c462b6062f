#!/usr/bin/env python3
"""
REAL LAYER STREAMING IMPLEMENTATION
===================================

Implement actual layer streaming for memory-efficient inference
Based on proven technique - load only active layers in RAM

Target: 5-10× RAM reduction through streaming
Current baseline: 2.58GB → 1.72GB (1.5×)
Goal: Further reduce to 200-400MB range
"""

import os
import torch
import psutil
import time
import json
import gc
from typing import Dict, Any, List, Optional, Tuple
from safetensors import safe_open
from transformers import AutoTokenizer
import numpy as np
from datetime import datetime

class RealLayerStreamer:
    """Real layer streaming implementation"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.ram_measurements = []
        self.layer_cache = {}
        self.max_layers_in_ram = 2  # Keep only 2 layers in RAM
        
        print(f"🔄 REAL LAYER STREAMING IMPLEMENTATION")
        print(f"📁 Model: {model_path}")
        print(f"🎯 Target: Keep only {self.max_layers_in_ram} layers in RAM")
        
    def measure_ram(self, description: str) -> float:
        """Measure RAM and return GB"""
        process = psutil.Process()
        ram_gb = process.memory_info().rss / (1024**3)
        
        measurement = {
            'timestamp': time.time(),
            'description': description,
            'ram_gb': ram_gb
        }
        self.ram_measurements.append(measurement)
        
        print(f"📊 RAM: {description} = {ram_gb:.3f}GB")
        return ram_gb
    
    def load_model_metadata(self) -> Dict[str, Any]:
        """Load model metadata without weights"""
        
        self.measure_ram("before_metadata")
        
        try:
            # Load tokenizer
            tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            
            # Load model index
            index_path = os.path.join(self.model_path, "model.safetensors.index.json")
            with open(index_path, 'r') as f:
                weight_index = json.load(f)
            
            # Load config
            config_path = os.path.join(self.model_path, "config.json")
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            self.measure_ram("after_metadata")
            
            # Organize layers by transformer layer number
            layer_groups = {}
            for weight_name in weight_index['weight_map'].keys():
                if 'layers.' in weight_name:
                    # Extract layer number (e.g., "model.layers.0.self_attn.q_proj.weight" -> 0)
                    parts = weight_name.split('.')
                    if len(parts) >= 3 and parts[1] == 'layers':
                        layer_num = int(parts[2])
                        if layer_num not in layer_groups:
                            layer_groups[layer_num] = []
                        layer_groups[layer_num].append(weight_name)
            
            metadata = {
                'tokenizer': tokenizer,
                'weight_index': weight_index,
                'config': config,
                'layer_groups': layer_groups,
                'num_layers': len(layer_groups),
                'total_weights': len(weight_index['weight_map'])
            }
            
            print(f"✅ Metadata loaded:")
            print(f"   Transformer layers: {metadata['num_layers']}")
            print(f"   Total weights: {metadata['total_weights']}")
            
            return metadata
            
        except Exception as e:
            print(f"❌ Error loading metadata: {e}")
            return {}
    
    def load_layer_group(self, layer_num: int, weight_index: Dict, layer_groups: Dict) -> Dict[str, torch.Tensor]:
        """Load all weights for a specific transformer layer"""
        
        if layer_num not in layer_groups:
            print(f"⚠️ Layer {layer_num} not found")
            return {}
        
        layer_weights = {}
        layer_names = layer_groups[layer_num]
        
        print(f"🔄 Loading layer {layer_num} ({len(layer_names)} weights)")
        
        for weight_name in layer_names:
            try:
                file_name = weight_index['weight_map'][weight_name]
                file_path = os.path.join(self.model_path, file_name)
                
                with safe_open(file_path, framework="pt", device="cpu") as f:
                    weight_tensor = f.get_tensor(weight_name)
                    layer_weights[weight_name] = weight_tensor.clone()
                    
            except Exception as e:
                print(f"⚠️ Error loading {weight_name}: {e}")
                continue
        
        print(f"✅ Loaded layer {layer_num}: {len(layer_weights)} weights")
        return layer_weights
    
    def unload_layer_group(self, layer_num: int):
        """Unload a layer group from cache"""
        
        keys_to_remove = []
        for key in self.layer_cache.keys():
            if f"layer_{layer_num}" in key:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.layer_cache[key]
        
        gc.collect()
        print(f"🗑️ Unloaded layer {layer_num}")
    
    def test_streaming_vs_full_load(self, max_test_layers: int = 5) -> Dict[str, Any]:
        """Test streaming approach vs loading all layers"""
        
        print(f"\n🧪 TESTING: Streaming vs Full Load")
        print("=" * 50)
        
        # Load metadata
        metadata = self.load_model_metadata()
        if not metadata:
            return {}
        
        weight_index = metadata['weight_index']
        layer_groups = metadata['layer_groups']
        
        # Test with first few layers
        test_layers = list(range(min(max_test_layers, metadata['num_layers'])))
        
        print(f"📊 Testing with layers: {test_layers}")
        
        # Test 1: Load all test layers simultaneously
        print(f"\n📈 FULL LOAD TEST")
        print("-" * 25)
        
        ram_before_full = self.measure_ram("before_full_load")
        
        all_layers = {}
        for layer_num in test_layers:
            layer_weights = self.load_layer_group(layer_num, weight_index, layer_groups)
            all_layers[f"layer_{layer_num}"] = layer_weights
        
        ram_after_full = self.measure_ram("after_full_load")
        full_load_increase = ram_after_full - ram_before_full
        
        # Clear memory
        all_layers.clear()
        gc.collect()
        
        # Test 2: Streaming approach
        print(f"\n🔄 STREAMING TEST")
        print("-" * 20)
        
        ram_before_streaming = self.measure_ram("before_streaming")
        
        max_streaming_ram = ram_before_streaming
        streaming_results = []
        
        for layer_num in test_layers:
            # Load current layer
            layer_weights = self.load_layer_group(layer_num, weight_index, layer_groups)
            self.layer_cache[f"layer_{layer_num}"] = layer_weights
            
            # Measure RAM with current layer
            current_ram = self.measure_ram(f"streaming_layer_{layer_num}")
            max_streaming_ram = max(max_streaming_ram, current_ram)
            
            # Simulate layer computation
            computation_successful = self.simulate_layer_computation(layer_weights, layer_num)
            
            streaming_results.append({
                'layer_num': layer_num,
                'weights_count': len(layer_weights),
                'ram_gb': current_ram,
                'computation_successful': computation_successful
            })
            
            # Keep only max_layers_in_ram in memory
            if len(self.layer_cache) > self.max_layers_in_ram:
                # Remove oldest layer
                oldest_layer = min(self.layer_cache.keys())
                oldest_num = int(oldest_layer.split('_')[1])
                self.unload_layer_group(oldest_num)
        
        ram_after_streaming = self.measure_ram("after_streaming")
        streaming_increase = max_streaming_ram - ram_before_streaming
        
        # Calculate efficiency
        ram_savings = full_load_increase - streaming_increase
        streaming_efficiency = full_load_increase / streaming_increase if streaming_increase > 0 else 1.0
        
        results = {
            'test_type': 'streaming_vs_full_load',
            'layers_tested': test_layers,
            'max_layers_in_ram': self.max_layers_in_ram,
            'full_load': {
                'ram_increase_gb': full_load_increase,
                'peak_ram_gb': ram_after_full
            },
            'streaming': {
                'ram_increase_gb': streaming_increase,
                'peak_ram_gb': max_streaming_ram,
                'layer_results': streaming_results
            },
            'efficiency': {
                'ram_savings_gb': ram_savings,
                'streaming_efficiency': streaming_efficiency,
                'streaming_better': ram_savings > 0
            }
        }
        
        print(f"\n📊 STREAMING RESULTS:")
        print(f"   Full load RAM: +{full_load_increase:.3f}GB")
        print(f"   Streaming RAM: +{streaming_increase:.3f}GB")
        print(f"   RAM savings: {ram_savings:.3f}GB")
        print(f"   Efficiency: {streaming_efficiency:.2f}×")
        print(f"   Streaming wins: {'✅ YES' if ram_savings > 0 else '❌ NO'}")
        
        return results
    
    def simulate_layer_computation(self, layer_weights: Dict[str, torch.Tensor], layer_num: int) -> bool:
        """Simulate computation on a layer"""
        
        try:
            # Find attention weights for computation test
            q_proj = None
            k_proj = None
            
            for name, weight in layer_weights.items():
                if 'q_proj.weight' in name:
                    q_proj = weight
                elif 'k_proj.weight' in name:
                    k_proj = weight
            
            if q_proj is not None and q_proj.dim() == 2:
                # Simulate attention computation
                batch_size, seq_len = 1, 128
                hidden_size = q_proj.shape[1]
                
                # Create test input
                test_input = torch.randn(batch_size, seq_len, hidden_size)
                
                # Simulate Q projection
                q_proj_f32 = q_proj.to(torch.float32)
                q_output = torch.matmul(test_input, q_proj_f32.t())
                
                print(f"   ✅ Layer {layer_num} computation: {test_input.shape} → {q_output.shape}")
                return True
            
            return False
            
        except Exception as e:
            print(f"   ⚠️ Layer {layer_num} computation failed: {e}")
            return False
    
    def test_inference_simulation(self, text: str = "The future of AI is") -> Dict[str, Any]:
        """Simulate streaming inference on text"""
        
        print(f"\n🧮 STREAMING INFERENCE SIMULATION")
        print(f"📝 Input: '{text}'")
        print("=" * 50)
        
        # Load metadata
        metadata = self.load_model_metadata()
        if not metadata:
            return {}
        
        tokenizer = metadata['tokenizer']
        weight_index = metadata['weight_index']
        layer_groups = metadata['layer_groups']
        
        # Tokenize input
        ram_before_tokenize = self.measure_ram("before_tokenization")
        
        inputs = tokenizer(text, return_tensors="pt")
        input_ids = inputs['input_ids']
        
        ram_after_tokenize = self.measure_ram("after_tokenization")
        
        print(f"📊 Tokenized: {input_ids.shape} tokens")
        
        # Simulate streaming inference through layers
        inference_results = []
        current_hidden_state = None
        
        # Load embedding layer
        embedding_name = 'model.embed_tokens.weight'
        if embedding_name in weight_index['weight_map']:
            
            ram_before_embed = self.measure_ram("before_embedding")
            
            file_name = weight_index['weight_map'][embedding_name]
            file_path = os.path.join(self.model_path, file_name)
            
            with safe_open(file_path, framework="pt", device="cpu") as f:
                embed_weight = f.get_tensor(embedding_name)
                
                # Perform embedding lookup
                current_hidden_state = torch.nn.functional.embedding(input_ids, embed_weight.to(torch.float32))
                
                ram_after_embed = self.measure_ram("after_embedding")
                
                inference_results.append({
                    'layer_type': 'embedding',
                    'input_shape': list(input_ids.shape),
                    'output_shape': list(current_hidden_state.shape),
                    'ram_gb': ram_after_embed
                })
                
                print(f"✅ Embedding: {input_ids.shape} → {current_hidden_state.shape}")
        
        # Stream through transformer layers
        num_layers_to_test = min(3, metadata['num_layers'])  # Test first 3 layers
        
        for layer_num in range(num_layers_to_test):
            print(f"\n🔄 Processing layer {layer_num}")
            
            # Load layer
            ram_before_layer = self.measure_ram(f"before_layer_{layer_num}")
            
            layer_weights = self.load_layer_group(layer_num, weight_index, layer_groups)
            
            if layer_weights:
                ram_after_load = self.measure_ram(f"after_load_layer_{layer_num}")
                
                # Simulate layer processing
                if current_hidden_state is not None:
                    # Simple simulation - just pass through
                    # In real implementation, this would be full transformer layer computation
                    layer_output = current_hidden_state  # Placeholder
                    
                    inference_results.append({
                        'layer_type': f'transformer_layer_{layer_num}',
                        'input_shape': list(current_hidden_state.shape),
                        'output_shape': list(layer_output.shape),
                        'weights_loaded': len(layer_weights),
                        'ram_gb': ram_after_load
                    })
                    
                    current_hidden_state = layer_output
                
                # Unload layer to free memory
                self.unload_layer_group(layer_num)
                
                ram_after_unload = self.measure_ram(f"after_unload_layer_{layer_num}")
                
                print(f"   RAM: {ram_before_layer:.3f}GB → {ram_after_load:.3f}GB → {ram_after_unload:.3f}GB")
        
        final_ram = self.measure_ram("inference_complete")
        
        results = {
            'test_type': 'streaming_inference_simulation',
            'input_text': text,
            'tokenized_length': input_ids.shape[1],
            'layers_processed': num_layers_to_test,
            'inference_results': inference_results,
            'final_output_shape': list(current_hidden_state.shape) if current_hidden_state is not None else None,
            'total_ram_measurements': len(self.ram_measurements)
        }
        
        print(f"\n📊 INFERENCE SIMULATION COMPLETE:")
        print(f"   Layers processed: {num_layers_to_test}")
        print(f"   Final RAM: {final_ram:.3f}GB")
        
        return results

def main():
    """Test real layer streaming implementation"""
    
    print("🚀 REAL LAYER STREAMING IMPLEMENTATION")
    print("=" * 70)
    print("GOAL: Implement proven layer streaming technique")
    print("TARGET: 5-10× RAM reduction through streaming")
    print("BASELINE: 2.58GB → 1.72GB (1.5×)")
    print()
    
    # Model path
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Initialize streaming system
    streamer = RealLayerStreamer(model_path)
    
    # Test 1: Streaming vs Full Load
    streaming_results = streamer.test_streaming_vs_full_load(max_test_layers=4)
    
    # Test 2: Inference simulation
    inference_results = streamer.test_inference_simulation("The future of artificial intelligence")
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"real_layer_streaming_results_{timestamp}.json"
    
    complete_results = {
        'timestamp': time.time(),
        'implementation': 'real_layer_streaming',
        'model_path': model_path,
        'max_layers_in_ram': streamer.max_layers_in_ram,
        'streaming_test': streaming_results,
        'inference_test': inference_results,
        'ram_measurements': streamer.ram_measurements
    }
    
    with open(results_file, 'w') as f:
        json.dump(complete_results, f, indent=2, default=str)
    
    print(f"\n✅ LAYER STREAMING IMPLEMENTATION COMPLETE")
    print(f"📄 Results saved: {results_file}")
    
    # Summary
    if streaming_results and 'efficiency' in streaming_results:
        eff = streaming_results['efficiency']
        print(f"\n📊 STREAMING EFFICIENCY:")
        print(f"   RAM savings: {eff['ram_savings_gb']:.3f}GB")
        print(f"   Efficiency: {eff['streaming_efficiency']:.2f}×")
        print(f"   Method works: {'✅ YES' if eff['streaming_better'] else '❌ NO'}")
        
        # Project to full model
        if eff['streaming_efficiency'] > 1:
            baseline_ram = 2.58  # GB
            projected_ram = baseline_ram / eff['streaming_efficiency']
            
            print(f"\n🚀 PROJECTION TO FULL MODEL:")
            print(f"   Current baseline: {baseline_ram:.2f}GB")
            print(f"   With streaming: {projected_ram:.3f}GB")
            print(f"   Total improvement: {baseline_ram / projected_ram:.2f}×")

if __name__ == "__main__":
    main()
