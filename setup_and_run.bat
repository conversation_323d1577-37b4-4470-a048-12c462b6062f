@echo off
REM Setup and run script for Phase 4 Continuous Loop

echo Setting up Python environment...

REM Create a new virtual environment
python -m venv .venv
call .venv\Scripts\activate.bat

REM Upgrade pip and setuptools
python -m pip install --upgrade pip setuptools

REM Install required packages
echo Installing required packages...
pip install -r requirements.txt

REM Run Phase 4
echo Starting Phase 4 Continuous Loop...
python phase_4_continuous_loop_cycles.py

pause
