{"timestamp": 1749112404.3140306, "description": "original_model", "directory": "../downloaded_models/mistral-7b-v0.1", "total_files": 27, "total_size_bytes": 17558169913, "total_size_mb": 16744.77568912506, "total_size_gb": 16.352320008911192, "file_sizes": {".gitattributes": {"size_bytes": 1519, "size_mb": 0.0014486312866210938, "size_gb": 1.4146789908409119e-06}, "config.json": {"size_bytes": 571, "size_mb": 0.0005445480346679688, "size_gb": 5.317851901054382e-07}, "generation_config.json": {"size_bytes": 116, "size_mb": 0.000110626220703125, "size_gb": 1.0803341865539551e-07}, "model-00001-of-00002.safetensors": {"size_bytes": 9942981696, "size_mb": 9482.366271972656, "size_gb": 9.260123312473297}, "model-00002-of-00002.safetensors": {"size_bytes": 4540516344, "size_mb": 4330.1738204956055, "size_gb": 4.22868537157774}, "model.safetensors.index.json": {"size_bytes": 25125, "size_mb": 0.02396106719970703, "size_gb": 2.3399479687213898e-05}, "pytorch_model.bin.index.json": {"size_bytes": 23950, "size_mb": 0.022840499877929688, "size_gb": 2.230517566204071e-05}, "README.md": {"size_bytes": 1555, "size_mb": 0.0014829635620117188, "size_gb": 1.448206603527069e-06}, "special_tokens_map.json": {"size_bytes": 414, "size_mb": 0.0003948211669921875, "size_gb": 3.855675458908081e-07}, "tokenizer.json": {"size_bytes": 1795188, "size_mb": 1.7120246887207031, "size_gb": 0.0016718991100788116}, "tokenizer.model": {"size_bytes": 493443, "size_mb": 0.4705839157104492, "size_gb": 0.00045955460518598557}, "tokenizer_config.json": {"size_bytes": 996, "size_mb": 0.000949859619140625, "size_gb": 9.275972843170166e-07}, ".cache\\huggingface\\.gitignore": {"size_bytes": 1, "size_mb": 9.5367431640625e-07, "size_gb": 9.313225746154785e-10}, ".cache\\huggingface\\download\\.gitattributes.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\config.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\fPHULxv55kAe7RSfHmmL42LIc1I=.67b1ea77d83cf017d6aa2fd9aadc6ad043a0cb3233a1cf9c422916b88350991f.incomplete": {"size_bytes": 1384120320, "size_mb": 1320.0, "size_gb": 1.2890625}, ".cache\\huggingface\\download\\generation_config.json.metadata": {"size_bytes": 102, "size_mb": 9.72747802734375e-05, "size_gb": 9.499490261077881e-08}, ".cache\\huggingface\\download\\HnkwBfZ0kY-ttHuN02vuxl1p6V0=.1feecce04754087e8e9a320847916ed57c6539ed0e5e2cd0ebdc7a816cc3773e.incomplete": {"size_bytes": 1688207360, "size_mb": 1610.0, "size_gb": 1.572265625}, ".cache\\huggingface\\download\\model-00001-of-00002.safetensors.metadata": {"size_bytes": 128, "size_mb": 0.0001220703125, "size_gb": 1.1920928955078125e-07}, ".cache\\huggingface\\download\\model-00002-of-00002.safetensors.metadata": {"size_bytes": 128, "size_mb": 0.0001220703125, "size_gb": 1.1920928955078125e-07}, ".cache\\huggingface\\download\\model.safetensors.index.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\pytorch_model.bin.index.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\README.md.metadata": {"size_bytes": 103, "size_mb": 9.822845458984375e-05, "size_gb": 9.592622518539429e-08}, ".cache\\huggingface\\download\\special_tokens_map.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\tokenizer.json.metadata": {"size_bytes": 103, "size_mb": 9.822845458984375e-05, "size_gb": 9.592622518539429e-08}, ".cache\\huggingface\\download\\tokenizer.model.metadata": {"size_bytes": 128, "size_mb": 0.0001220703125, "size_gb": 1.1920928955078125e-07}, ".cache\\huggingface\\download\\tokenizer_config.json.metadata": {"size_bytes": 103, "size_mb": 9.822845458984375e-05, "size_gb": 9.592622518539429e-08}}}