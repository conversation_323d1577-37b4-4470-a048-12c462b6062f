2025-06-12 16:06:54,335 - asyncio - DEBUG - Using proactor: IocpProactor
2025-06-12 16:06:54,337 - __main__ - INFO - Starting DataCollectionAgent test...
2025-06-12 16:06:54,337 - agent.data - INFO - Initialized data agent
2025-06-12 16:06:54,337 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 16:06:54,337 - __main__ - INFO - Starting agent...
2025-06-12 16:06:54,338 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 16:06:54,338 - __main__ - INFO - 
================================================================================
2025-06-12 16:06:54,339 - __main__ - INFO - Testing AAPL with interval=1d, period=1mo
2025-06-12 16:06:54,339 - __main__ - INFO - ================================================================================
2025-06-12 16:06:54,339 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=AAPL, interval=1d, period=1mo, start=None, end=None, max_retries=3, retry_delay=1.0, timeout=30)
2025-06-12 16:06:54,339 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Using period: 1mo
2025-06-12 16:06:54,340 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Fetching data for AAPL (attempt 1/3)...
2025-06-12 16:06:54,340 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:06:54
2025-06-12 16:06:54,340 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Created yf.Ticker for AAPL
2025-06-12 16:06:54,341 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=30s
2025-06-12 16:06:54,341 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1d', 'period': '1mo', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:06:54,342 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:06:54
2025-06-12 16:06:54,342 - yfinance - DEBUG - Entering history()
2025-06-12 16:06:54,343 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-06-12 16:06:54,344 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-06-12 16:06:54,375 - yfinance - DEBUG -  Entering history()
2025-06-12 16:06:54,375 - yfinance - DEBUG - AAPL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:06:54,375 - yfinance - DEBUG -   Entering get()
2025-06-12 16:06:54,376 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 16:06:54,376 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-06-12 16:06:54,376 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:06:54,376 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 16:06:54,377 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 16:06:54,377 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 16:06:54,378 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 16:06:54,378 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-06-12 16:06:54,379 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-06-12 16:06:54,380 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-06-12 16:06:54,380 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-06-12 16:06:54,380 - yfinance - DEBUG - reusing persistent cookie
2025-06-12 16:06:54,381 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 16:06:54,383 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 16:06:54,383 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-06-12 16:06:54,383 - yfinance - DEBUG - reusing cookie
2025-06-12 16:06:54,383 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-06-12 16:06:54,787 - yfinance - DEBUG - crumb = 'iDku.3/e0AN'
2025-06-12 16:06:54,788 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 16:06:54,789 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 16:06:54,789 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 16:06:55,029 - yfinance - DEBUG - response code=200
2025-06-12 16:06:55,030 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 16:06:55,030 - yfinance - DEBUG -   Exiting get()
2025-06-12 16:06:55,032 - yfinance - DEBUG - AAPL: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 16:06:55,034 - yfinance - DEBUG - AAPL: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 16:06:55,038 - yfinance - DEBUG - AAPL: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:06:55,042 - yfinance - DEBUG - AAPL: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:06:55,043 - yfinance - DEBUG -  Exiting history()
2025-06-12 16:06:55,043 - yfinance - DEBUG - Exiting history()
2025-06-12 16:06:55,043 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.70s
2025-06-12 16:06:55,044 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Fetched 22 rows for AAPL
2025-06-12 16:06:55,044 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 22
2025-06-12 16:06:55,044 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:06:55,047 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Caching result with key: AAPL_1d_1mo_None_None
2025-06-12 16:06:55,047 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Successfully fetched 22 data points for AAPL in 0.71s
2025-06-12 16:06:55,047 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:06:55,048 - __main__ - INFO - Successfully fetched 22 data points for AAPL
2025-06-12 16:06:55,048 - __main__ - INFO - Date range: 1747022400 to 1749614400
2025-06-12 16:06:55,048 - __main__ - INFO - Latest close: $198.78
2025-06-12 16:06:55,048 - __main__ - INFO - Volume: 60,820,200
2025-06-12 16:06:55,048 - __main__ - INFO - 
================================================================================
2025-06-12 16:06:55,048 - __main__ - INFO - Testing MSFT with interval=1h, period=5d
2025-06-12 16:06:55,048 - __main__ - INFO - ================================================================================
2025-06-12 16:06:55,049 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=MSFT, interval=1h, period=5d, start=None, end=None, max_retries=3, retry_delay=1.0, timeout=30)
2025-06-12 16:06:55,049 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Using period: 5d
2025-06-12 16:06:55,049 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Fetching data for MSFT (attempt 1/3)...
2025-06-12 16:06:55,049 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:06:55
2025-06-12 16:06:55,049 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Created yf.Ticker for MSFT
2025-06-12 16:06:55,049 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=30s
2025-06-12 16:06:55,050 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1h', 'period': '5d', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:06:55,050 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:06:55
2025-06-12 16:06:55,050 - yfinance - DEBUG - Entering history()
2025-06-12 16:06:55,051 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-06-12 16:06:55,051 - yfinance - DEBUG -  Entering history()
2025-06-12 16:06:55,051 - yfinance - DEBUG - MSFT: Yahoo GET parameters: {'range': '5d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:06:55,052 - yfinance - DEBUG -   Entering get()
2025-06-12 16:06:55,052 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 16:06:55,052 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-06-12 16:06:55,052 - yfinance - DEBUG - params={'range': '5d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:06:55,052 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 16:06:55,052 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 16:06:55,053 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 16:06:55,053 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 16:06:55,053 - yfinance - DEBUG - reusing cookie
2025-06-12 16:06:55,053 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 16:06:55,053 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 16:06:55,053 - yfinance - DEBUG - reusing crumb
2025-06-12 16:06:55,054 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 16:06:55,054 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 16:06:55,054 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 16:06:55,240 - yfinance - DEBUG - response code=200
2025-06-12 16:06:55,242 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 16:06:55,242 - yfinance - DEBUG -   Exiting get()
2025-06-12 16:06:55,243 - yfinance - DEBUG - MSFT: yfinance received OHLC data: 2025-06-05 13:30:00 -> 2025-06-11 20:00:00
2025-06-12 16:06:55,247 - yfinance - DEBUG - MSFT: OHLC after cleaning: 2025-06-05 09:30:00-04:00 -> 2025-06-11 15:30:00-04:00
2025-06-12 16:06:55,248 - yfinance - DEBUG - MSFT: OHLC after combining events: 2025-06-05 09:30:00-04:00 -> 2025-06-11 15:30:00-04:00
2025-06-12 16:06:55,252 - yfinance - DEBUG - MSFT: yfinance returning OHLC: 2025-06-05 09:30:00-04:00 -> 2025-06-11 15:30:00-04:00
2025-06-12 16:06:55,252 - yfinance - DEBUG -  Exiting history()
2025-06-12 16:06:55,252 - yfinance - DEBUG - Exiting history()
2025-06-12 16:06:55,252 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.20s
2025-06-12 16:06:55,253 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Fetched 35 rows for MSFT
2025-06-12 16:06:55,253 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 35
2025-06-12 16:06:55,254 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:06:55,255 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Caching result with key: MSFT_1h_5d_None_None
2025-06-12 16:06:55,255 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Successfully fetched 35 data points for MSFT in 0.21s
2025-06-12 16:06:55,256 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:06:55,256 - __main__ - INFO - Successfully fetched 35 data points for MSFT
2025-06-12 16:06:55,256 - __main__ - INFO - Date range: 1749130200 to 1749670200
2025-06-12 16:06:55,256 - __main__ - INFO - Latest close: $472.90
2025-06-12 16:06:55,256 - __main__ - INFO - Volume: 1,634,641
2025-06-12 16:06:55,256 - __main__ - INFO - 
================================================================================
2025-06-12 16:06:55,256 - __main__ - INFO - Testing GOOGL with interval=1d, period=1y
2025-06-12 16:06:55,256 - __main__ - INFO - ================================================================================
2025-06-12 16:06:55,256 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=GOOGL, interval=1d, period=1y, start=None, end=None, max_retries=3, retry_delay=1.0, timeout=30)
2025-06-12 16:06:55,258 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Using period: 1y
2025-06-12 16:06:55,258 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Fetching data for GOOGL (attempt 1/3)...
2025-06-12 16:06:55,258 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:06:55
2025-06-12 16:06:55,258 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Created yf.Ticker for GOOGL
2025-06-12 16:06:55,259 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=30s
2025-06-12 16:06:55,259 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1d', 'period': '1y', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:06:55,259 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:06:55
2025-06-12 16:06:55,259 - yfinance - DEBUG - Entering history()
2025-06-12 16:06:55,260 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-06-12 16:06:55,260 - yfinance - DEBUG -  Entering history()
2025-06-12 16:06:55,260 - yfinance - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1y', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:06:55,260 - yfinance - DEBUG -   Entering get()
2025-06-12 16:06:55,260 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 16:06:55,261 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-06-12 16:06:55,261 - yfinance - DEBUG - params={'range': '1y', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:06:55,261 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 16:06:55,261 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 16:06:55,261 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 16:06:55,261 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 16:06:55,261 - yfinance - DEBUG - reusing cookie
2025-06-12 16:06:55,262 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 16:06:55,262 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 16:06:55,262 - yfinance - DEBUG - reusing crumb
2025-06-12 16:06:55,262 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 16:06:55,262 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 16:06:55,262 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 16:06:55,349 - yfinance - DEBUG - response code=200
2025-06-12 16:06:55,350 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 16:06:55,350 - yfinance - DEBUG -   Exiting get()
2025-06-12 16:06:55,351 - yfinance - DEBUG - GOOGL: yfinance received OHLC data: 2024-06-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 16:06:55,353 - yfinance - DEBUG - GOOGL: OHLC after cleaning: 2024-06-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 16:06:55,357 - yfinance - DEBUG - GOOGL: OHLC after combining events: 2024-06-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:06:55,360 - yfinance - DEBUG - GOOGL: yfinance returning OHLC: 2024-06-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:06:55,361 - yfinance - DEBUG -  Exiting history()
2025-06-12 16:06:55,361 - yfinance - DEBUG - Exiting history()
2025-06-12 16:06:55,361 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.10s
2025-06-12 16:06:55,361 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Fetched 250 rows for GOOGL
2025-06-12 16:06:55,362 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 250
2025-06-12 16:06:55,362 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:06:55,364 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Caching result with key: GOOGL_1d_1y_None_None
2025-06-12 16:06:55,364 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Successfully fetched 250 data points for GOOGL in 0.11s
2025-06-12 16:06:55,365 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:06:55,365 - __main__ - INFO - Successfully fetched 250 data points for GOOGL
2025-06-12 16:06:55,365 - __main__ - INFO - Date range: 1718164800 to 1749614400
2025-06-12 16:06:55,365 - __main__ - INFO - Latest close: $177.35
2025-06-12 16:06:55,366 - __main__ - INFO - Volume: 31,607,800
2025-06-12 16:06:55,366 - __main__ - INFO - Stopping agent...
2025-06-12 16:06:55,366 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 16:06:55,366 - __main__ - INFO - Agent stopped.
