#!/usr/bin/env python3
"""
KNOWLEDGE DISTILLATION FRAMEWORK
================================

Complete teacher-student distillation framework for transformer compression.
Includes gradient matching, multiple loss functions, and memory-efficient training.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from tqdm import tqdm
import math

from transformer_compression_system import (
    CompressionConfig, TransformerArchitecture, MemoryMonitor, 
    TransformerCompressionError
)

logger = logging.getLogger(__name__)

class DistillationDataset(Dataset):
    """Dataset for knowledge distillation training"""
    
    def __init__(self, texts: List[str], tokenizer, max_length: int = 512):
        self.texts = texts
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze()
        }

class KnowledgeDistillationFramework:
    """Complete knowledge distillation framework"""
    
    def __init__(self, config: CompressionConfig):
        self.config = config
        self.memory_monitor = MemoryMonitor(config.max_memory_gb)
        
        # Initialize models
        self.teacher_model = None
        self.student_model = None
        
        # Training components
        self.optimizer = None
        self.scheduler = None
        self.criterion = DistillationLoss(config)
        
        # Metrics tracking
        self.training_metrics = {
            'distillation_losses': [],
            'reconstruction_losses': [],
            'gradient_matching_losses': [],
            'total_losses': [],
            'learning_rates': []
        }
    
    def setup_models(self, teacher_config: Dict[str, Any], student_config: Optional[Dict[str, Any]] = None):
        """
        Setup teacher and student models
        
        Args:
            teacher_config: Configuration for teacher model
            student_config: Configuration for student model (auto-generated if None)
        """
        try:
            # Create teacher model
            self.teacher_model = TransformerArchitecture(**teacher_config)
            
            # Auto-generate student config if not provided
            if student_config is None:
                student_config = self._generate_student_config(teacher_config)
            
            # Create student model
            self.student_model = TransformerArchitecture(**student_config)
            
            # Move to device
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.teacher_model = self.teacher_model.to(device)
            self.student_model = self.student_model.to(device)
            
            # Enable gradient checkpointing for memory efficiency
            if self.config.gradient_checkpointing:
                self.teacher_model.gradient_checkpointing_enable()
                self.student_model.gradient_checkpointing_enable()
            
            # Setup optimizer and scheduler
            self.optimizer = optim.AdamW(
                self.student_model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=0.01
            )
            
            total_steps = self.config.num_epochs * 1000  # Estimate
            self.scheduler = optim.lr_scheduler.OneCycleLR(
                self.optimizer,
                max_lr=self.config.learning_rate,
                total_steps=total_steps,
                pct_start=0.1
            )
            
            logger.info(f"Teacher model: {sum(p.numel() for p in self.teacher_model.parameters())/1e6:.1f}M params")
            logger.info(f"Student model: {sum(p.numel() for p in self.student_model.parameters())/1e6:.1f}M params")
            
        except Exception as e:
            logger.error(f"Model setup failed: {e}")
            raise TransformerCompressionError(f"Model setup failed: {e}")
    
    def _generate_student_config(self, teacher_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate student model configuration based on teacher"""
        
        student_config = teacher_config.copy()
        
        # Reduce model size based on student_hidden_ratio
        ratio = self.config.student_hidden_ratio
        
        student_config['hidden_size'] = int(teacher_config['hidden_size'] * ratio)
        student_config['intermediate_size'] = int(teacher_config['intermediate_size'] * ratio)
        student_config['num_layers'] = max(1, int(teacher_config['num_layers'] * ratio))
        student_config['num_heads'] = max(1, int(teacher_config['num_heads'] * ratio))
        
        # Ensure hidden_size is divisible by num_heads
        while student_config['hidden_size'] % student_config['num_heads'] != 0:
            student_config['num_heads'] -= 1
            if student_config['num_heads'] == 0:
                student_config['num_heads'] = 1
                break
        
        return student_config
    
    def distill_model(self, train_dataloader: DataLoader, val_dataloader: Optional[DataLoader] = None) -> Dict[str, Any]:
        """
        Perform knowledge distillation training
        
        Args:
            train_dataloader: Training data loader
            val_dataloader: Validation data loader (optional)
            
        Returns:
            Dictionary with training results and metrics
        """
        try:
            if self.teacher_model is None or self.student_model is None:
                raise TransformerCompressionError("Models not initialized. Call setup_models() first.")
            
            # Set teacher to eval mode
            self.teacher_model.eval()
            
            logger.info("Starting knowledge distillation training...")
            
            best_val_loss = float('inf')
            best_student_state = None
            
            for epoch in range(self.config.num_epochs):
                logger.info(f"Epoch {epoch + 1}/{self.config.num_epochs}")
                
                # Training phase
                train_metrics = self._train_epoch(train_dataloader, epoch)
                
                # Validation phase
                if val_dataloader is not None:
                    val_metrics = self._validate_epoch(val_dataloader, epoch)
                    
                    # Save best model
                    if val_metrics['total_loss'] < best_val_loss:
                        best_val_loss = val_metrics['total_loss']
                        best_student_state = self.student_model.state_dict().copy()
                        logger.info(f"New best validation loss: {best_val_loss:.6f}")
                
                # Memory cleanup
                self.memory_monitor.check_memory("end of epoch")
                gc.collect()
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            # Load best model if available
            if best_student_state is not None:
                self.student_model.load_state_dict(best_student_state)
                logger.info("Loaded best student model from validation")
            
            # Calculate final compression metrics
            compression_metrics = self._calculate_compression_metrics()
            
            results = {
                'training_metrics': self.training_metrics,
                'compression_metrics': compression_metrics,
                'memory_stats': self.memory_monitor.get_memory_stats(),
                'best_val_loss': best_val_loss if val_dataloader is not None else None
            }
            
            logger.info("Knowledge distillation completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Distillation training failed: {e}")
            raise TransformerCompressionError(f"Distillation training failed: {e}")
    
    def _train_epoch(self, dataloader: DataLoader, epoch: int) -> Dict[str, float]:
        """Train for one epoch"""
        
        self.student_model.train()
        
        epoch_metrics = {
            'distillation_loss': 0.0,
            'reconstruction_loss': 0.0,
            'gradient_matching_loss': 0.0,
            'total_loss': 0.0
        }
        
        num_batches = len(dataloader)
        
        with tqdm(dataloader, desc=f"Training Epoch {epoch + 1}") as pbar:
            for batch_idx, batch in enumerate(pbar):
                try:
                    # Move batch to device
                    device = next(self.student_model.parameters()).device
                    input_ids = batch['input_ids'].to(device)
                    attention_mask = batch['attention_mask'].to(device)
                    
                    # Memory check
                    self.memory_monitor.check_memory(f"batch {batch_idx}")
                    
                    # Forward pass through teacher (no gradients)
                    with torch.no_grad():
                        teacher_outputs = self.teacher_model(input_ids, attention_mask)
                    
                    # Forward pass through student
                    student_outputs = self.student_model(input_ids, attention_mask)
                    
                    # Calculate distillation loss
                    loss_dict = self.criterion(
                        student_outputs=student_outputs,
                        teacher_outputs=teacher_outputs,
                        labels=input_ids,  # For language modeling
                        student_model=self.student_model,
                        teacher_model=self.teacher_model
                    )
                    
                    total_loss = loss_dict['total_loss']
                    
                    # Backward pass
                    self.optimizer.zero_grad()
                    total_loss.backward()
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.student_model.parameters(), max_norm=1.0)
                    
                    self.optimizer.step()
                    self.scheduler.step()
                    
                    # Update metrics
                    for key in epoch_metrics:
                        if key in loss_dict:
                            epoch_metrics[key] += loss_dict[key].item()
                    
                    # Update progress bar
                    pbar.set_postfix({
                        'loss': total_loss.item(),
                        'lr': self.scheduler.get_last_lr()[0]
                    })
                    
                except Exception as e:
                    logger.warning(f"Batch {batch_idx} failed: {e}")
                    continue
        
        # Average metrics
        for key in epoch_metrics:
            epoch_metrics[key] /= num_batches
        
        # Store metrics
        self.training_metrics['distillation_losses'].append(epoch_metrics['distillation_loss'])
        self.training_metrics['reconstruction_losses'].append(epoch_metrics['reconstruction_loss'])
        self.training_metrics['gradient_matching_losses'].append(epoch_metrics['gradient_matching_loss'])
        self.training_metrics['total_losses'].append(epoch_metrics['total_loss'])
        self.training_metrics['learning_rates'].append(self.scheduler.get_last_lr()[0])
        
        logger.info(f"Training - Total Loss: {epoch_metrics['total_loss']:.6f}, "
                   f"Distillation: {epoch_metrics['distillation_loss']:.6f}")
        
        return epoch_metrics
    
    def _validate_epoch(self, dataloader: DataLoader, epoch: int) -> Dict[str, float]:
        """Validate for one epoch"""
        
        self.student_model.eval()
        
        epoch_metrics = {
            'distillation_loss': 0.0,
            'reconstruction_loss': 0.0,
            'gradient_matching_loss': 0.0,
            'total_loss': 0.0
        }
        
        num_batches = len(dataloader)
        
        with torch.no_grad():
            with tqdm(dataloader, desc=f"Validation Epoch {epoch + 1}") as pbar:
                for batch_idx, batch in enumerate(pbar):
                    try:
                        # Move batch to device
                        device = next(self.student_model.parameters()).device
                        input_ids = batch['input_ids'].to(device)
                        attention_mask = batch['attention_mask'].to(device)
                        
                        # Forward passes
                        teacher_outputs = self.teacher_model(input_ids, attention_mask)
                        student_outputs = self.student_model(input_ids, attention_mask)
                        
                        # Calculate loss
                        loss_dict = self.criterion(
                            student_outputs=student_outputs,
                            teacher_outputs=teacher_outputs,
                            labels=input_ids,
                            student_model=self.student_model,
                            teacher_model=self.teacher_model
                        )
                        
                        # Update metrics
                        for key in epoch_metrics:
                            if key in loss_dict:
                                epoch_metrics[key] += loss_dict[key].item()
                        
                        # Update progress bar
                        pbar.set_postfix({'val_loss': loss_dict['total_loss'].item()})
                        
                    except Exception as e:
                        logger.warning(f"Validation batch {batch_idx} failed: {e}")
                        continue
        
        # Average metrics
        for key in epoch_metrics:
            epoch_metrics[key] /= num_batches
        
        logger.info(f"Validation - Total Loss: {epoch_metrics['total_loss']:.6f}")
        
        return epoch_metrics
    
    def _calculate_compression_metrics(self) -> Dict[str, float]:
        """Calculate compression metrics"""
        
        teacher_params = sum(p.numel() for p in self.teacher_model.parameters())
        student_params = sum(p.numel() for p in self.student_model.parameters())
        
        teacher_size = sum(p.numel() * p.element_size() for p in self.teacher_model.parameters())
        student_size = sum(p.numel() * p.element_size() for p in self.student_model.parameters())
        
        return {
            'teacher_params': teacher_params,
            'student_params': student_params,
            'param_compression_ratio': teacher_params / student_params,
            'teacher_size_mb': teacher_size / (1024 * 1024),
            'student_size_mb': student_size / (1024 * 1024),
            'size_compression_ratio': teacher_size / student_size
        }

class DistillationLoss(nn.Module):
    """Multi-component distillation loss"""
    
    def __init__(self, config: CompressionConfig):
        super().__init__()
        self.config = config
        self.temperature = config.temperature
        self.alpha = config.distillation_alpha
        
    def forward(self, student_outputs: torch.Tensor, teacher_outputs: torch.Tensor,
                labels: torch.Tensor, student_model: nn.Module, teacher_model: nn.Module) -> Dict[str, torch.Tensor]:
        """
        Calculate multi-component distillation loss
        
        Args:
            student_outputs: Student model outputs
            teacher_outputs: Teacher model outputs  
            labels: Ground truth labels
            student_model: Student model for gradient matching
            teacher_model: Teacher model for gradient matching
            
        Returns:
            Dictionary of loss components
        """
        
        # 1. Knowledge Distillation Loss (KL divergence)
        student_log_probs = F.log_softmax(student_outputs / self.temperature, dim=-1)
        teacher_probs = F.softmax(teacher_outputs / self.temperature, dim=-1)
        
        distillation_loss = F.kl_div(
            student_log_probs,
            teacher_probs,
            reduction='batchmean'
        ) * (self.temperature ** 2)
        
        # 2. Reconstruction Loss (standard cross-entropy)
        reconstruction_loss = F.cross_entropy(
            student_outputs.view(-1, student_outputs.size(-1)),
            labels.view(-1),
            ignore_index=-100
        )
        
        # 3. Gradient Matching Loss
        gradient_matching_loss = self._calculate_gradient_matching_loss(
            student_model, teacher_model, student_outputs, teacher_outputs
        )
        
        # 4. Feature Matching Loss (if models have similar architectures)
        feature_matching_loss = self._calculate_feature_matching_loss(
            student_model, teacher_model
        )
        
        # Combine losses
        total_loss = (
            self.alpha * distillation_loss +
            (1 - self.alpha) * reconstruction_loss +
            0.1 * gradient_matching_loss +
            0.05 * feature_matching_loss
        )
        
        return {
            'distillation_loss': distillation_loss,
            'reconstruction_loss': reconstruction_loss,
            'gradient_matching_loss': gradient_matching_loss,
            'feature_matching_loss': feature_matching_loss,
            'total_loss': total_loss
        }
    
    def _calculate_gradient_matching_loss(self, student_model: nn.Module, teacher_model: nn.Module,
                                        student_outputs: torch.Tensor, teacher_outputs: torch.Tensor) -> torch.Tensor:
        """Calculate gradient matching loss"""
        
        try:
            # Create dummy loss for gradient computation
            student_dummy_loss = student_outputs.mean()
            teacher_dummy_loss = teacher_outputs.mean()
            
            # Compute gradients
            student_grads = torch.autograd.grad(
                student_dummy_loss, student_model.parameters(),
                create_graph=True, retain_graph=True
            )
            
            with torch.no_grad():
                teacher_grads = torch.autograd.grad(
                    teacher_dummy_loss, teacher_model.parameters(),
                    create_graph=False, retain_graph=False
                )
            
            # Calculate gradient matching loss
            grad_loss = 0.0
            num_layers = 0
            
            for s_grad, t_grad in zip(student_grads, teacher_grads):
                if s_grad is not None and t_grad is not None:
                    # Normalize gradients
                    s_grad_norm = F.normalize(s_grad.flatten(), dim=0)
                    t_grad_norm = F.normalize(t_grad.flatten(), dim=0)
                    
                    # Cosine similarity loss
                    grad_loss += 1.0 - F.cosine_similarity(s_grad_norm, t_grad_norm, dim=0)
                    num_layers += 1
            
            return grad_loss / max(num_layers, 1)
            
        except Exception as e:
            logger.warning(f"Gradient matching loss calculation failed: {e}")
            return torch.tensor(0.0, device=student_outputs.device)
    
    def _calculate_feature_matching_loss(self, student_model: nn.Module, teacher_model: nn.Module) -> torch.Tensor:
        """Calculate feature matching loss between intermediate layers"""
        
        try:
            feature_loss = 0.0
            num_matches = 0
            
            # Compare layer norms (as proxy for intermediate features)
            student_norms = [module for module in student_model.modules() if isinstance(module, nn.LayerNorm)]
            teacher_norms = [module for module in teacher_model.modules() if isinstance(module, nn.LayerNorm)]
            
            # Match layers proportionally
            if len(student_norms) > 0 and len(teacher_norms) > 0:
                for i, student_norm in enumerate(student_norms):
                    # Map student layer to corresponding teacher layer
                    teacher_idx = int(i * len(teacher_norms) / len(student_norms))
                    teacher_norm = teacher_norms[teacher_idx]
                    
                    # Compare normalized weights
                    if hasattr(student_norm, 'weight') and hasattr(teacher_norm, 'weight'):
                        s_weight = F.normalize(student_norm.weight, dim=0)
                        t_weight = F.normalize(teacher_norm.weight, dim=0)
                        
                        # Ensure same size (pad or truncate)
                        min_size = min(s_weight.size(0), t_weight.size(0))
                        s_weight = s_weight[:min_size]
                        t_weight = t_weight[:min_size]
                        
                        feature_loss += F.mse_loss(s_weight, t_weight)
                        num_matches += 1
            
            return feature_loss / max(num_matches, 1)
            
        except Exception as e:
            logger.warning(f"Feature matching loss calculation failed: {e}")
            return torch.tensor(0.0, device=next(student_model.parameters()).device)
