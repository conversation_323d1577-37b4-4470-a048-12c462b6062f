"""
Direct test of DataCollectionAgent with absolute imports
"""
import asyncio
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the agent directly from the agents module
from agents.data_agent import DataCollectionAgent

async def test_agent():
    print("Testing DataCollectionAgent...")
    agent = DataCollectionAgent()
    
    try:
        # Start the agent
        print("Starting agent...")
        await agent.start()
        
        # Test basic data fetch
        print("\nFetching AAPL data...")
        ohlcv = await agent.fetch_ohlcv(
            symbol="AAPL",
            interval="1d",
            period="1mo"
        )
        
        if ohlcv:
            print(f"Success! Got {len(ohlcv.timestamp)} data points for {ohlcv.symbol}")
            print(f"Sample data (first 5 close prices): {ohlcv.close[:5]}")
        else:
            print("Failed to fetch data")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.stop()
        print("\nAgent stopped")

if __name__ == "__main__":
    asyncio.run(test_agent())
