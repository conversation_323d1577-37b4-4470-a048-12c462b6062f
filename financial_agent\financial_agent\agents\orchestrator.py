import asyncio
from typing import Dict, Any, Optional, List, Type
from dataclasses import dataclass
import logging
from datetime import datetime
import json

from .base_agent import BaseAgent, AgentResponse
from ..llm.mistral_wrapper import MistralWrapper, GenerationConfig

logger = logging.getLogger(__name__)

@dataclass
class AgentConfig:
    """Configuration for an agent"""
    agent_class: Type[BaseAgent]
    config: Dict[str, Any]
    enabled: bool = True
    dependencies: List[str] = None

class OrchestratorAgent(BaseAgent):
    """
    Orchestrator agent that manages the financial agent system.
    Coordinates between different specialized agents and handles the overall workflow.
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize the orchestrator agent
        
        Args:
            config_path: Path to configuration file
        """
        super().__init__("orchestrator")
        
        # Initialize LLM wrapper
        self.llm = MistralWrapper()
        
        # Agent registry
        self.agents = {}
        self.agent_configs = {}
        
        # System state
        self.market_state = {}
        self.portfolio_state = {}
        self.risk_metrics = {}
        
        # Load configuration
        self.config_path = config_path or "config/settings.json"
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                
            # Initialize LLM with config
            llm_config = config.get('llm', {})
            self.llm = MistralWrapper(
                model_name=llm_config.get('model_name', 'mistralai/Mistral-7B-v0.1'),
                device=llm_config.get('device')
            )
            
            # Load generation config
            gen_config = llm_config.get('generation', {})
            self.llm.generation_config = GenerationConfig(
                max_new_tokens=gen_config.get('max_new_tokens', 512),
                temperature=gen_config.get('temperature', 0.7),
                top_p=gen_config.get('top_p', 0.9),
                top_k=gen_config.get('top_k', 50),
                repetition_penalty=gen_config.get('repetition_penalty', 1.1),
                do_sample=gen_config.get('do_sample', True)
            )
            
            # Load agent configurations
            self.agent_configs = config.get('agents', {})
            
            logger.info(f"Loaded configuration from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            raise
    
    async def initialize_agents(self):
        """Initialize all configured agents"""
        # Import agent classes here to avoid circular imports
        from .data_agent import DataCollectionAgent
        from .analysis_agent import AnalysisAgent
        from .strategy_agent import StrategyAgent
        from .execution_agent import ExecutionAgent
        from .risk_agent import RiskManagementAgent
        
        agent_classes = {
            'data': DataCollectionAgent,
            'analysis': AnalysisAgent,
            'strategy': StrategyAgent,
            'execution': ExecutionAgent,
            'risk': RiskManagementAgent
        }
        
        # Initialize each agent
        for agent_name, agent_config in self.agent_configs.items():
            if not agent_config.get('enabled', True):
                logger.info(f"Skipping disabled agent: {agent_name}")
                continue
                
            agent_class = agent_classes.get(agent_name)
            if not agent_class:
                logger.warning(f"Unknown agent type: {agent_name}")
                continue
                
            try:
                # Create agent instance with LLM wrapper
                agent = agent_class(
                    name=agent_name,
                    llm_wrapper=self.llm,
                    **agent_config.get('config', {})
                )
                
                # Store agent instance
                self.agents[agent_name] = agent
                logger.info(f"Initialized agent: {agent_name}")
                
            except Exception as e:
                logger.error(f"Failed to initialize agent {agent_name}: {str(e)}")
    
    async def start(self) -> AgentResponse:
        """Start the orchestrator and all agents"""
        if self.is_running:
            return AgentResponse(
                success=False,
                error="Orchestrator is already running"
            )
        
        try:
            # Load and initialize the LLM
            logger.info("Loading Mistral LLM...")
            self.llm.load_model(quantize=True)
            
            # Initialize all agents
            logger.info("Initializing agents...")
            await self.initialize_agents()
            
            # Start all agents
            for agent_name, agent in self.agents.items():
                await agent.start()
                logger.info(f"Started agent: {agent_name}")
            
            self.is_running = True
            logger.info("Orchestrator started successfully")
            return AgentResponse(success=True, data={"status": "started"})
            
        except Exception as e:
            error_msg = f"Failed to start orchestrator: {str(e)}"
            logger.error(error_msg)
            return AgentResponse(success=False, error=error_msg)
    
    async def stop(self) -> AgentResponse:
        """Stop the orchestrator and all agents"""
        if not self.is_running:
            return AgentResponse(
                success=False,
                error="Orchestrator is not running"
            )
        
        try:
            # Stop all agents
            for agent_name, agent in self.agents.items():
                await agent.stop()
                logger.info(f"Stopped agent: {agent_name}")
            
            self.is_running = False
            logger.info("Orchestrator stopped successfully")
            return AgentResponse(success=True, data={"status": "stopped"})
            
        except Exception as e:
            error_msg = f"Error stopping orchestrator: {str(e)}"
            logger.error(error_msg)
            return AgentResponse(success=False, error=error_msg)
    
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        Process input through the agent system
        
        Args:
            input_data: Dictionary containing input data
            
        Returns:
            AgentResponse with processing results
        """
        if not self.is_running:
            return AgentResponse(
                success=False,
                error="Orchestrator is not running"
            )
        
        try:
            # Update market state with new data
            if 'market_data' in input_data:
                self.market_state.update(input_data['market_data'])
            
            # Process through pipeline: data -> analysis -> strategy -> execution -> risk
            results = {}
            
            # 1. Data collection and processing
            if 'data' in self.agents:
                data_result = await self.agents['data'].process(input_data)
                if not data_result.success:
                    return data_result
                results['data'] = data_result.data
            
            # 2. Market analysis
            if 'analysis' in self.agents:
                analysis_result = await self.agents['analysis'].process({
                    'market_data': self.market_state,
                    'portfolio': self.portfolio_state
                })
                if not analysis_result.success:
                    return analysis_result
                results['analysis'] = analysis_result.data
            
            # 3. Strategy formulation
            if 'strategy' in self.agents:
                strategy_result = await self.agents['strategy'].process({
                    'market_analysis': results.get('analysis', {}),
                    'portfolio': self.portfolio_state,
                    'risk_metrics': self.risk_metrics
                })
                if not strategy_result.success:
                    return strategy_result
                results['strategy'] = strategy_result.data
            
            # 4. Order execution
            if 'execution' in self.agents and 'strategy' in results:
                execution_result = await self.agents['execution'].process({
                    'strategy': results['strategy'],
                    'portfolio': self.portfolio_state
                })
                if not execution_result.success:
                    return execution_result
                results['execution'] = execution_result.data
                
                # Update portfolio state
                if 'portfolio_updates' in execution_result.data:
                    self._update_portfolio(execution_result.data['portfolio_updates'])
            
            # 5. Risk management
            if 'risk' in self.agents:
                risk_result = await self.agents['risk'].process({
                    'market_data': self.market_state,
                    'portfolio': self.portfolio_state,
                    'pending_orders': results.get('execution', {}).get('pending_orders', [])
                })
                if not risk_result.success:
                    return risk_result
                results['risk'] = risk_result.data
                self.risk_metrics = risk_result.data.get('metrics', {})
            
            return AgentResponse(
                success=True,
                data={
                    'results': results,
                    'timestamp': datetime.utcnow().isoformat()
                }
            )
            
        except Exception as e:
            error_msg = f"Error in processing pipeline: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return AgentResponse(success=False, error=error_msg)
    
    def _update_portfolio(self, updates: Dict[str, Any]):
        """Update portfolio state with execution results"""
        # This is a simplified example - in a real system, you'd want
        # to handle this more carefully with proper locking and validation
        for asset, change in updates.items():
            if asset in self.portfolio_state:
                self.portfolio_state[asset] += change
            else:
                self.portfolio_state[asset] = change
                
            # Remove assets with zero balance
            if self.portfolio_state[asset] == 0:
                del self.portfolio_state[asset]
    
    async def run_continuous_loop(self, interval_seconds: int = 60):
        """
        Run the agent system in a continuous loop
        
        Args:
            interval_seconds: Time between iterations (seconds)
        """
        logger.info(f"Starting continuous loop with {interval_seconds}s interval")
        
        while self.is_running:
            try:
                iteration_start = datetime.utcnow()
                
                # Process one iteration
                result = await self.process({})
                if not result.success:
                    logger.error(f"Iteration failed: {result.error}")
                
                # Calculate sleep time to maintain interval
                process_time = (datetime.utcnow() - iteration_start).total_seconds()
                sleep_time = max(0, interval_seconds - process_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    
            except asyncio.CancelledError:
                logger.info("Continuous loop cancelled")
                break
                
            except Exception as e:
                logger.error(f"Error in continuous loop: {str(e)}", exc_info=True)
                # Prevent tight loop on repeated errors
                await asyncio.sleep(min(30, interval_seconds))
        
        logger.info("Continuous loop stopped")
