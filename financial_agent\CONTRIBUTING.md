# Contributing to Financial Agent

Thank you for your interest in contributing to the Financial Agent project! We welcome contributions from the community to help improve this project.

## How to Contribute

### Reporting Issues
- Check if the issue has already been reported in the [Issues](https://github.com/yourusername/financial-agent/issues) section
- If not, create a new issue with a clear title and description
- Include steps to reproduce the issue and any relevant error messages
- Specify your environment (OS, Python version, etc.)

### Feature Requests
- Open an issue with the "enhancement" label
- Describe the feature and why it would be valuable
- Include any relevant examples or references

### Code Contributions
1. Fork the repository
2. Create a new branch for your feature or bugfix
3. Make your changes following the coding standards
4. Add tests for your changes
5. Run the test suite and ensure all tests pass
6. Submit a pull request with a clear description of your changes

## Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/financial-agent.git
   cd financial-agent
   ```

2. **Set up a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

4. **Install pre-commit hooks**
   ```bash
   pre-commit install
   ```

## Coding Standards

- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide
- Use type hints for all function signatures
- Write docstrings for all public functions and classes
- Keep functions small and focused on a single responsibility
- Write meaningful commit messages following [Conventional Commits](https://www.conventionalcommits.org/)

## Testing

- Write tests for all new functionality
- Run tests before submitting a pull request:
  ```bash
  pytest tests/
  ```
- Ensure test coverage remains high (minimum 80%)

## Documentation

- Update documentation when adding new features or changing behavior
- Keep docstrings up to date
- Add examples for complex functionality

## Pull Request Process

1. Update the README.md with details of changes if needed
2. Ensure your code passes all tests
3. Request review from at least one maintainer
4. Address any feedback from code review
5. Once approved, squash your commits into a single logical change
6. A maintainer will merge your PR

## Code of Conduct

Please note that this project is released with a [Contributor Code of Conduct](CODE_OF_CONDUCT.md). By participating in this project you agree to abide by its terms.

## License

By contributing, you agree that your contributions will be licensed under the [MIT License](LICENSE).
