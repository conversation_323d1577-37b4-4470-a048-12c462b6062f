#!/usr/bin/env python3
"""
Financial Agent System

A continuously running financial agent system powered by Mistral LLM for intelligent
decision making in financial markets.
"""
import asyncio
import json
import logging
import signal
import sys
from pathlib import Path
from typing import Dict, Any

from agents.orchestrator import OrchestratorAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('financial_agent.log')
    ]
)

logger = logging.getLogger(__name__)


class FinancialAgentSystem:
    """Main financial agent system class"""
    
    def __init__(self, config_path: str = "config/settings.json"):
        """
        Initialize the financial agent system
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        self.orchestrator = None
        self.loop = None
        self.shutdown_event = asyncio.Event()
    
    async def initialize(self):
        """Initialize the system and all components"""
        try:
            logger.info("Initializing Financial Agent System...")
            
            # Create necessary directories
            Path("logs").mkdir(exist_ok=True)
            
            # Initialize orchestrator
            self.orchestrator = OrchestratorAgent(self.config_path)
            
            # Start the orchestrator
            result = await self.orchestrator.start()
            if not result.success:
                raise RuntimeError(f"Failed to start orchestrator: {result.error}")
            
            logger.info("Financial Agent System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Initialization failed: {str(e)}", exc_info=True)
            return False
    
    async def run(self):
        """Run the main event loop"""
        if not self.orchestrator:
            raise RuntimeError("System not initialized. Call initialize() first.")
        
        logger.info("Starting Financial Agent System...")
        
        # Set up signal handlers
        for sig in (signal.SIGINT, signal.SIGTERM):
            self.loop.add_signal_handler(
                sig, lambda s=sig: asyncio.create_task(self.shutdown(s))
            )
        
        try:
            # Start the continuous processing loop in the background
            self.loop.create_task(self.orchestrator.run_continuous_loop())
            
            # Keep the main thread alive
            await self.shutdown_event.wait()
            
        except asyncio.CancelledError:
            logger.info("Main loop cancelled")
        except Exception as e:
            logger.error(f"Error in main loop: {str(e)}", exc_info=True)
        finally:
            await self.cleanup()
    
    async def shutdown(self, sig=None):
        """Gracefully shutdown the system"""
        if sig:
            logger.info(f"Received exit signal {sig.name}...")
        else:
            logger.info("Shutting down...")
        
        # Signal shutdown
        self.shutdown_event.set()
        
        # Stop all agents
        if self.orchestrator:
            await self.orchestrator.stop()
    
    async def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up resources...")
        # Additional cleanup if needed
        logger.info("Cleanup complete")


def main():
    """Main entry point"""
    # Create and run the system
    system = FinancialAgentSystem()
    
    try:
        # Initialize the system
        if not asyncio.run(system.initialize()):
            logger.error("Failed to initialize system")
            return 1
        
        # Run the main loop
        system.loop = asyncio.get_event_loop()
        system.loop.run_until_complete(system.run())
        
    except KeyboardInterrupt:
        logger.info("Shutdown requested...")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}", exc_info=True)
        return 1
    finally:
        # Ensure clean shutdown
        if system.loop:
            system.loop.run_until_complete(system.cleanup())
            if not system.loop.is_closed():
                system.loop.close()
    
    logger.info("Financial Agent System stopped")
    return 0


if __name__ == "__main__":
    sys.exit(main())
