# 🚀 LOOP AGI Setup Guide

## System Requirements

- **Python:** 3.8 or higher
- **RAM:** 8GB recommended (uses < 200MB typically)
- **Storage:** 5GB free space
- **OS:** Windows 10+, Linux, macOS

## Installation Steps

### 1. Clone Repository
```bash
git clone https://github.com/rockstaaa/loop-agi.git
cd loop-agi
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Verify Installation
```bash
python loop.py --single-cycle
```

### 4. Run Full System
```bash
python loop.py
```

## Configuration

Edit `config.yaml` to customize:
- Resource limits
- Safety policies  
- Cycle parameters
- Logging levels

## Troubleshooting

### Common Issues

**Import Errors:**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

**Permission Errors:**
- Ensure write permissions in directory
- Run with appropriate user privileges

**Memory Issues:**
- Reduce `max_cycles` in config
- Increase `cycle_interval`

## Advanced Usage

### Stress Testing
```bash
python stress_test.py
```

### Research Mode
```bash
python -c "from autonomous_researcher import AutonomousResearcher; r = AutonomousResearcher(); r.autonomous_research_cycle()"
```

### Custom Configuration
```python
from loop import LoopAGI

# Custom configuration
agi = LoopAGI("custom_config.yaml")
agi.run_autonomous_loop()
```

## Support

- **Issues:** GitHub Issues
- **Documentation:** README.md
- **Examples:** sample_data/
