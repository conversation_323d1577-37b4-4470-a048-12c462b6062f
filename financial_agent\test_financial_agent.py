"""
Test script for financial_agent package
"""
import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

# Import the agent from the package
from financial_agent.agents.data_agent import DataCollectionAgent

async def main():
    print("Testing DataCollectionAgent from financial_agent package...")
    agent = DataCollectionAgent()
    
    try:
        # Start the agent
        print("Starting agent...")
        await agent.start()
        
        # Test basic data fetch
        print("\nFetching AAPL data...")
        ohlcv = await agent.fetch_ohlcv(
            symbol="AAPL",
            interval="1d",
            period="1mo"
        )
        
        if ohlcv:
            print(f"Success! Got {len(ohlcv.timestamp)} data points for {ohlcv.symbol}")
            print(f"Sample data (first 5 close prices): {ohlcv.close[:5]}")
        else:
            print("Failed to fetch data")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.stop()
        print("\nAgent stopped")

if __name__ == "__main__":
    asyncio.run(main())
