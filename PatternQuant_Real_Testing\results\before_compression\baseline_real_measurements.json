{"timestamp": 1749113288.4891162, "model_path": "../downloaded_models/mistral-7b-v0.1", "real_parameters": 7241732096, "real_parameters_b": 7.241732096, "config": {"vocab_size": 32000, "max_position_embeddings": 32768, "hidden_size": 4096, "intermediate_size": 14336, "num_hidden_layers": 32, "num_attention_heads": 32, "sliding_window": 4096, "head_dim": 128, "num_key_value_heads": 8, "hidden_act": "silu", "initializer_range": 0.02, "rms_norm_eps": 1e-05, "use_cache": true, "rope_theta": 10000.0, "attention_dropout": 0.0, "return_dict": true, "output_hidden_states": false, "output_attentions": false, "torchscript": false, "torch_dtype": "bfloat16", "use_bfloat16": false, "tf_legacy_loss": false, "pruned_heads": {}, "tie_word_embeddings": false, "chunk_size_feed_forward": 0, "is_encoder_decoder": false, "is_decoder": false, "cross_attention_hidden_size": null, "add_cross_attention": false, "tie_encoder_decoder": false, "max_length": 20, "min_length": 0, "do_sample": false, "early_stopping": false, "num_beams": 1, "num_beam_groups": 1, "diversity_penalty": 0.0, "temperature": 1.0, "top_k": 50, "top_p": 1.0, "typical_p": 1.0, "repetition_penalty": 1.0, "length_penalty": 1.0, "no_repeat_ngram_size": 0, "encoder_no_repeat_ngram_size": 0, "bad_words_ids": null, "num_return_sequences": 1, "output_scores": false, "return_dict_in_generate": false, "forced_bos_token_id": null, "forced_eos_token_id": null, "remove_invalid_values": false, "exponential_decay_length_penalty": null, "suppress_tokens": null, "begin_suppress_tokens": null, "architectures": ["MistralForCausalLM"], "finetuning_task": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "tokenizer_class": null, "prefix": null, "bos_token_id": 1, "pad_token_id": null, "eos_token_id": 2, "sep_token_id": null, "decoder_start_token_id": null, "task_specific_params": null, "problem_type": null, "_name_or_path": "../downloaded_models/mistral-7b-v0.1", "_attn_implementation_autoset": false, "transformers_version": "4.51.3", "model_type": "mistral"}, "file_measurements": {"timestamp": 1749113118.0991158, "description": "original_model", "directory": "../downloaded_models/mistral-7b-v0.1", "total_files": 27, "total_size_bytes": 17558169913, "total_size_mb": 16744.77568912506, "total_size_gb": 16.352320008911192, "file_sizes": {".gitattributes": {"size_bytes": 1519, "size_mb": 0.0014486312866210938, "size_gb": 1.4146789908409119e-06}, "config.json": {"size_bytes": 571, "size_mb": 0.0005445480346679688, "size_gb": 5.317851901054382e-07}, "generation_config.json": {"size_bytes": 116, "size_mb": 0.000110626220703125, "size_gb": 1.0803341865539551e-07}, "model-00001-of-00002.safetensors": {"size_bytes": 9942981696, "size_mb": 9482.366271972656, "size_gb": 9.260123312473297}, "model-00002-of-00002.safetensors": {"size_bytes": 4540516344, "size_mb": 4330.1738204956055, "size_gb": 4.22868537157774}, "model.safetensors.index.json": {"size_bytes": 25125, "size_mb": 0.02396106719970703, "size_gb": 2.3399479687213898e-05}, "pytorch_model.bin.index.json": {"size_bytes": 23950, "size_mb": 0.022840499877929688, "size_gb": 2.230517566204071e-05}, "README.md": {"size_bytes": 1555, "size_mb": 0.0014829635620117188, "size_gb": 1.448206603527069e-06}, "special_tokens_map.json": {"size_bytes": 414, "size_mb": 0.0003948211669921875, "size_gb": 3.855675458908081e-07}, "tokenizer.json": {"size_bytes": 1795188, "size_mb": 1.7120246887207031, "size_gb": 0.0016718991100788116}, "tokenizer.model": {"size_bytes": 493443, "size_mb": 0.4705839157104492, "size_gb": 0.00045955460518598557}, "tokenizer_config.json": {"size_bytes": 996, "size_mb": 0.000949859619140625, "size_gb": 9.275972843170166e-07}, ".cache\\huggingface\\.gitignore": {"size_bytes": 1, "size_mb": 9.5367431640625e-07, "size_gb": 9.313225746154785e-10}, ".cache\\huggingface\\download\\.gitattributes.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\config.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\fPHULxv55kAe7RSfHmmL42LIc1I=.67b1ea77d83cf017d6aa2fd9aadc6ad043a0cb3233a1cf9c422916b88350991f.incomplete": {"size_bytes": 1384120320, "size_mb": 1320.0, "size_gb": 1.2890625}, ".cache\\huggingface\\download\\generation_config.json.metadata": {"size_bytes": 102, "size_mb": 9.72747802734375e-05, "size_gb": 9.499490261077881e-08}, ".cache\\huggingface\\download\\HnkwBfZ0kY-ttHuN02vuxl1p6V0=.1feecce04754087e8e9a320847916ed57c6539ed0e5e2cd0ebdc7a816cc3773e.incomplete": {"size_bytes": 1688207360, "size_mb": 1610.0, "size_gb": 1.572265625}, ".cache\\huggingface\\download\\model-00001-of-00002.safetensors.metadata": {"size_bytes": 128, "size_mb": 0.0001220703125, "size_gb": 1.1920928955078125e-07}, ".cache\\huggingface\\download\\model-00002-of-00002.safetensors.metadata": {"size_bytes": 128, "size_mb": 0.0001220703125, "size_gb": 1.1920928955078125e-07}, ".cache\\huggingface\\download\\model.safetensors.index.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\pytorch_model.bin.index.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\README.md.metadata": {"size_bytes": 103, "size_mb": 9.822845458984375e-05, "size_gb": 9.592622518539429e-08}, ".cache\\huggingface\\download\\special_tokens_map.json.metadata": {"size_bytes": 104, "size_mb": 9.918212890625e-05, "size_gb": 9.685754776000977e-08}, ".cache\\huggingface\\download\\tokenizer.json.metadata": {"size_bytes": 103, "size_mb": 9.822845458984375e-05, "size_gb": 9.592622518539429e-08}, ".cache\\huggingface\\download\\tokenizer.model.metadata": {"size_bytes": 128, "size_mb": 0.0001220703125, "size_gb": 1.1920928955078125e-07}, ".cache\\huggingface\\download\\tokenizer_config.json.metadata": {"size_bytes": 103, "size_mb": 9.822845458984375e-05, "size_gb": 9.592622518539429e-08}}}, "memory_measurements": {"before_load": {"timestamp": 1749113118.0924742, "description": "before_model_load", "process_memory_mb": 312.6953125, "process_memory_gb": 0.30536651611328125, "system_total_gb": 15.692127227783203, "system_used_gb": 9.278247833251953, "system_available_gb": 6.41387939453125, "system_percent": 59.1}, "after_tokenizer": {"timestamp": 1749113118.3326552, "description": "after_tokenizer_load", "process_memory_mb": 326.56640625, "process_memory_gb": 0.3189125061035156, "system_total_gb": 15.692127227783203, "system_used_gb": 9.324295043945312, "system_available_gb": 6.367832183837891, "system_percent": 59.4}, "model_measurements": [{"timestamp": 1749113212.5541065, "description": "after_model_load", "process_memory_mb": 237.7890625, "process_memory_gb": 0.23221588134765625, "system_total_gb": 15.692127227783203, "system_used_gb": 15.344783782958984, "system_available_gb": 0.34734344482421875, "system_percent": 97.8}, {"timestamp": 1749113288.1526866, "description": "after_inference", "process_memory_mb": 2645.265625, "process_memory_gb": 2.5832672119140625, "system_total_gb": 15.692127227783203, "system_used_gb": 15.148139953613281, "system_available_gb": 0.5439872741699219, "system_percent": 96.5}]}, "inference_time_s": 74.34739899635315, "system_info": {"timestamp": "2025-06-05T14:15:17.978009", "system": "Windows", "platform": "Windows-10-10.0.26100-SP0", "processor": "Intel64 Family 6 Model 154 Stepping 4, GenuineIntel", "python_version": "3.11.0", "total_ram_gb": 15.692127227783203, "available_ram_gb": 6.426258087158203, "used_ram_gb": 9.265869140625, "ram_percent": 59.0, "cpu_count": 12, "cpu_count_logical": 12}}