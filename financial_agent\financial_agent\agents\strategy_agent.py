"""
Strategy Agent for generating trading strategies based on analysis signals.
"""
import logging
from typing import Dict, List, Optional, Any, Tu<PERSON>, Union
from dataclasses import dataclass, field
from enum import Enum, auto
import numpy as np
import logging

from .base_agent import BaseAgent, AgentResponse
from .analysis_agent import AnalysisResult, SignalType, Indicator, IndicatorType

# Configure logging
logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Types of trading strategies."""
    MEAN_REVERSION = auto()
    TREND_FOLLOWING = auto()
    BREAKOUT = auto()
    MOMENTUM = auto()
    SCALPING = auto()
    SWING = auto()
    POSITION = auto()
    ETF_STRATEGY = auto()  # New strategy type for ETF trading

@dataclass
class PositionSizing:
    """Position sizing parameters for a strategy."""
    size_percent: float  # % of portfolio to allocate
    max_position_size: float  # Maximum position size as % of daily volume
    max_portfolio_risk: float  # Max % of portfolio to risk on a single trade
    stop_loss_pct: float  # Stop loss as percentage of entry price
    take_profit_pct: float  # Take profit as percentage of entry price

@dataclass
class TradeSignal:
    """A complete trade signal generated by the strategy."""
    symbol: str
    signal_type: SignalType
    strategy_type: StrategyType
    entry_price: float
    stop_loss: float
    take_profit: float
    size_percent: float
    confidence: float
    timestamp: int
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class StrategyConfig:
    """Configuration for a trading strategy."""
    strategy_type: StrategyType
    position_sizing: PositionSizing
    indicators: List[str]  # List of required indicators
    timeframes: List[str]  # List of timeframes to analyze
    max_positions: int  # Maximum number of concurrent positions
    max_drawdown: float  # Maximum allowed drawdown as percentage
    risk_free_rate: float = 0.02  # Risk-free rate for performance metrics

class StrategyAgent(BaseAgent):
    """Agent responsible for generating trading strategies based on analysis."""
    
    def __init__(self, llm_wrapper=None, config: Optional[Dict[str, Any]] = None, name: str = 'strategy'):
        """Initialize the StrategyAgent.
        
        Args:
            llm_wrapper: Optional LLM wrapper for advanced strategy generation
            config: Configuration dictionary
            name: Name of the agent (default: 'strategy')
        """
        super().__init__(name=name, llm_wrapper=llm_wrapper)
        self.config = config or {}
        self._setup_default_config()
        self.active_strategies: Dict[str, StrategyConfig] = {}
        self.portfolio = self.config.get('initial_portfolio', 100000.0)  # Default $100k portfolio
        self.positions: Dict[str, Dict] = {}
        
    def _setup_default_config(self):
        """Set up default configuration if not provided."""
        defaults = {
            'default_position_size': 0.02,  # 2% of portfolio per trade
            'max_position_size_pct': 0.1,  # Max 10% of daily volume
            'max_portfolio_risk': 0.02,  # Max 2% risk per trade
            'default_stop_loss_pct': 0.05,  # 5% stop loss
            'default_take_profit_pct': 0.10,  # 10% take profit
            'max_drawdown': 0.10,  # 10% max drawdown
            'risk_free_rate': 0.02,  # 2% risk-free rate
            'max_positions': 10,  # Max 10 concurrent positions
            'initial_portfolio': 100000.0,  # $100k initial portfolio
            # ETF Strategy specific config
            'etf_sectors': [
                'XLF', 'XLK', 'XLE', 'XLV', 'XLI', 'XLB', 'XLY', 'XLP', 'XTL', 'XLU', 'XLRE', 'XLC'
            ],  # Popular sector ETFs
            'top_etfs_count': 3,  # Number of top ETFs to select
            'etf_rebalance_days': 21,  # Rebalance approximately monthly (21 trading days)
            'etf_min_volume': 1000000,  # Minimum average daily volume (shares)
            'etf_min_price': 10.0,  # Minimum price to consider
            'etf_max_expense_ratio': 0.002,  # Maximum expense ratio (0.2%)
            'etf_min_assets': 100000000,  # $100M minimum assets under management
            'etf_indicators': ['sma_50', 'sma_200', 'rsi_14', 'macd', 'bb_20_2'],
            'etf_timeframes': ['1d'],  # Daily timeframe for ETF analysis
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
    
    async def start(self):
        """Start the strategy agent."""
        if self.is_running:
            logger.warning("Strategy agent is already running")
            return
            
        logger.info("Starting strategy agent...")
        self.is_running = True
        
        # Initialize ETF strategy if not already present
        if 'etf_strategy' not in self.active_strategies:
            self._initialize_etf_strategy()
            
        logger.info(f"Strategy agent started with {len(self.active_strategies)} active strategies")
        
        # Initialize default strategies if none are active
        if not self.active_strategies:
            await self._initialize_default_strategies()
            
        logger.info("Strategy agent started")
    
    async def stop(self):
        """Stop the strategy agent."""
        if not self.is_running:
            logger.warning("Strategy agent is not running")
            return
            
        logger.info("Stopping strategy agent...")
        self.is_running = False
        logger.info("Strategy agent stopped")
    
    def _initialize_etf_strategy(self):
        """Initialize the ETF trading strategy."""
        etf_strategy = {
            'name': 'etf_strategy',
            'type': StrategyType.ETF_STRATEGY,
            'position_sizing': PositionSizing(
                size_percent=0.05,  # 5% of portfolio per ETF
                max_position_size=0.1,  # Max 10% of daily volume
                max_portfolio_risk=0.02,  # 2% risk per ETF
                stop_loss_pct=self.config['default_stop_loss_pct'],
                take_profit_pct=self.config['default_take_profit_pct']
            ),
            'indicators': self.config['etf_indicators'],
            'timeframes': self.config['etf_timeframes'],
            'max_positions': self.config.get('top_etfs_count', 3),
            'max_drawdown': self.config['max_drawdown'],
            'last_rebalance': None,
            'current_holdings': {}
        }
        
        self.active_strategies['etf_strategy'] = StrategyConfig(**{
            k: v for k, v in etf_strategy.items() 
            if k in StrategyConfig.__annotations__
        })
        
        # Store additional ETF-specific data
        self.etf_data = {
            'last_rebalance': None,
            'current_holdings': {},
            'sector_scores': {},
            'etf_metrics': {}
        }
        
        logger.info("Initialized ETF trading strategy")
    
    async def _initialize_default_strategies(self):
        """Initialize default trading strategies."""
        # Add default strategies if none are configured
        default_strategies = [
            {
                'name': 'trend_following',
                'type': StrategyType.TREND_FOLLOWING,
                'position_sizing': PositionSizing(
                    size_percent=0.05,  # 5% of portfolio
                    max_position_size=0.2,  # Max 20% of daily volume
                    max_portfolio_risk=0.02,  # 2% risk per trade
                    stop_loss_pct=0.05,  # 5% stop loss
                    take_profit_pct=0.15  # 15% take profit
                ),
                'indicators': ['sma_50', 'sma_200', 'rsi_14', 'macd'],
                'timeframes': ['1d', '4h'],
                'max_positions': 5,
                'max_drawdown': 0.10
            },
            {
                'name': 'mean_reversion',
                'type': StrategyType.MEAN_REVERSION,
                'position_sizing': PositionSizing(
                    size_percent=0.03,  # 3% of portfolio
                    max_position_size=0.1,  # Max 10% of daily volume
                    max_portfolio_risk=0.01,  # 1% risk per trade
                    stop_loss_pct=0.03,  # 3% stop loss
                    take_profit_pct=0.06  # 6% take profit
                ),
                'indicators': ['rsi_14', 'bollinger_bands', 'stoch'],
                'timeframes': ['4h', '1h'],
                'max_positions': 8,
                'max_drawdown': 0.07
            }
        ]
        
        for strategy in default_strategies:
            self.active_strategies[strategy['name']] = StrategyConfig(
                strategy_type=strategy['type'],
                position_sizing=strategy['position_sizing'],
                indicators=strategy['indicators'],
                timeframes=strategy['timeframes'],
                max_positions=strategy['max_positions'],
                max_drawdown=strategy['max_drawdown']
            )
        
        logger.info(f"Initialized {len(default_strategies)} default strategies")
    
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """Process analysis results and generate trading signals.
        
        Args:
            input_data: Dictionary containing 'analysis' key with AnalysisResult
            
        Returns:
            AgentResponse containing trading signals or error
        """
        if 'analysis' not in input_data or not isinstance(input_data['analysis'], AnalysisResult):
            return AgentResponse(
                success=False,
                error="Invalid input: 'analysis' key with AnalysisResult object is required"
            )
        
        try:
            analysis_result = input_data['analysis']
            symbol = analysis_result.symbol
            
            # Get current price from OHLCV data (use close price of the latest candle)
            current_price = analysis_result.metadata.get('close_prices', [0])[-1] if analysis_result.metadata else 0
            
            # Generate signals from all active strategies
            signals = []
            for strategy_name, strategy_config in self.active_strategies.items():
                signal = await self._generate_signal(
                    analysis_result=analysis_result,
                    strategy_config=strategy_config,
                    current_price=current_price
                )
                if signal:
                    signals.append(signal)
            
            # Select the best signal based on confidence and strategy priority
            best_signal = self._select_best_signal(signals) if signals else None
            
            # Prepare response
            response_data = {
                'symbol': symbol,
                'timestamp': analysis_result.timestamp,
                'signals': [
                    {
                        'strategy': signal.strategy_type.name,
                        'signal': signal.signal_type.name,
                        'entry_price': signal.entry_price,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit,
                        'size_percent': signal.size_percent,
                        'confidence': signal.confidence
                    }
                    for signal in signals
                ],
                'best_signal': {
                    'strategy': best_signal.strategy_type.name if best_signal else None,
                    'signal': best_signal.signal_type.name if best_signal else 'NO_SIGNAL',
                    'entry_price': best_signal.entry_price if best_signal else None,
                    'stop_loss': best_signal.stop_loss if best_signal else None,
                    'take_profit': best_signal.take_profit if best_signal else None,
                    'size_percent': best_signal.size_percent if best_signal else None,
                    'confidence': best_signal.confidence if best_signal else 0.0
                } if best_signal else None
            }
            
            return AgentResponse(success=True, data=response_data)
            
        except Exception as e:
            self.logger.error(f"Error in process: {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                error=f"Strategy generation failed: {str(e)}"
            )
    
    async def _generate_signal(
        self,
        analysis_result: AnalysisResult,
        strategy_config: StrategyConfig,
        current_price: float
    ) -> Optional[TradeSignal]:
        """Generate a trading signal based on analysis and strategy."""
        try:
            # Handle ETF strategy separately
            if strategy_config.strategy_type == StrategyType.ETF_STRATEGY:
                return await self._generate_etf_signal(analysis_result, strategy_config)
                
            # Skip if we don't have a valid price
            if current_price <= 0:
                return None
            
            # Get required indicators from analysis
            indicators = {ind.name: ind for ind in analysis_result.indicators}
            
            # Check if we have all required indicators for this strategy
            required_indicators = set(strategy_config.indicators)
            available_indicators = set(indicators.keys())
            
            if not required_indicators.issubset(available_indicators):
                missing = required_indicators - available_indicators
                logger.warning(f"Missing indicators for {strategy_config.strategy_type.name}: {missing}")
                return None
            
            # Generate signal based on strategy type
            signal = None
            
            if strategy_config.strategy_type == StrategyType.MEAN_REVERSION:
                signal = self._generate_mean_reversion_signal(
                    analysis_result, indicators, current_price, strategy_config
                )
            elif strategy_config.strategy_type == StrategyType.TREND_FOLLOWING:
                signal = self._generate_trend_following_signal(
                    analysis_result, indicators, current_price, strategy_config
                )
            # Add other strategy types here...
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating {strategy_config.strategy_type.name} signal: {str(e)}", exc_info=True)
            return None
            
    async def _generate_etf_signal(
        self,
        analysis_result: AnalysisResult,
        strategy_config: StrategyConfig
    ) -> Optional[TradeSignal]:
        """Generate ETF trading signal based on sector analysis and ETF metrics."""
        try:
            symbol = analysis_result.symbol
            
            # Check if it's time to rebalance (monthly)
            if not self._should_rebalance_etfs():
                # Check if we already hold this ETF
                if symbol in self.etf_data['current_holdings']:
                    # Generate hold signal for existing position
                    return self._create_etf_hold_signal(symbol, analysis_result, strategy_config)
                return None
            
            # Score the ETF based on various factors
            etf_score = await self._score_etf(analysis_result)
            if etf_score is None:
                return None
                
            # Store the score for sector analysis
            sector = self._get_etf_sector(symbol)
            if sector not in self.etf_data['sector_scores']:
                self.etf_data['sector_scores'][sector] = {}
            self.etf_data['sector_scores'][symbol] = etf_score
            
            # If this is the last ETF in our universe, select top ETFs
            if self._is_last_etf(symbol):
                return await self._select_top_etfs_and_generate_signals(strategy_config)
                
            return None
            
        except Exception as e:
            logger.error(f"Error generating ETF signal for {symbol}: {str(e)}", exc_info=True)
            return None
    
    def _generate_mean_reversion_signal(
        self,
        analysis_result: AnalysisResult,
        indicators: Dict[str, Any],
        current_price: float,
        config: StrategyConfig
    ) -> Optional[TradeSignal]:
        """Generate mean reversion trading signal."""
        # Get indicators
        rsi = indicators.get('RSI')
        bb_upper = analysis_result.metadata.get('bb_upper', [0])[-1]
        bb_lower = analysis_result.metadata.get('bb_lower', [0])[-1]
        
        # Check for oversold condition (potential buy)
        if rsi.value < 30 and current_price < bb_lower:
            entry_price = current_price
            stop_loss = entry_price * (1 - config.position_sizing.stop_loss_pct)
            take_profit = entry_price * (1 + (config.position_sizing.take_profit_pct * 0.8))  # Smaller target for mean reversion
            
            return TradeSignal(
                symbol=analysis_result.symbol,
                signal_type=SignalType.BUY,
                strategy_type=StrategyType.MEAN_REVERSION,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                size_percent=config.position_sizing.size_percent,
                confidence=min(0.8, (30 - rsi.value) / 30 * 0.8),  # Scale confidence with RSI
                timestamp=analysis_result.timestamp,
                metadata={
                    'rsi': rsi.value,
                    'bb_upper': bb_upper,
                    'bb_lower': bb_lower
                }
            )
        
        # Check for overbought condition (potential sell)
        elif rsi.value > 70 and current_price > bb_upper:
            entry_price = current_price
            stop_loss = entry_price * (1 + config.position_sizing.stop_loss_pct)
            take_profit = entry_price * (1 - (config.position_sizing.take_profit_pct * 0.8))  # Smaller target for mean reversion
            
            return TradeSignal(
                symbol=analysis_result.symbol,
                signal_type=SignalType.SELL,
                strategy_type=StrategyType.MEAN_REVERSION,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                size_percent=config.position_sizing.size_percent,
                confidence=min(0.8, (rsi.value - 70) / 30 * 0.8),  # Scale confidence with RSI
                timestamp=analysis_result.timestamp,
                metadata={
                    'rsi': rsi.value,
                    'bb_upper': bb_upper,
                    'bb_lower': bb_lower
                }
            )
        
        return None
    
    def _generate_trend_following_signal(
        self,
        analysis_result: AnalysisResult,
        indicators: Dict[str, Any],
        current_price: float,
        config: StrategyConfig
    ) -> Optional[TradeSignal]:
        """Generate trend following trading signal."""
        # Get indicators
        ma_fast = indicators.get('MA_50')
        ma_slow = indicators.get('MA_200')
        macd = indicators.get('MACD')
        macd_signal = indicators.get('MACD_SIGNAL')
        
        # Check for uptrend (potential buy)
        if (ma_fast.value > ma_slow.value and 
            macd.value > macd_signal.value and
            current_price > ma_fast.value):
            
            entry_price = current_price
            stop_loss = entry_price * (1 - config.position_sizing.stop_loss_pct)
            take_profit = entry_price * (1 + config.position_sizing.take_profit_pct)
            
            # Calculate trend strength (0-1)
            trend_strength = min(1.0, (ma_fast.value / ma_slow.value - 1) * 100)  # Fast MA % above slow MA
            confidence = 0.5 + (trend_strength * 0.5)  # 0.5-1.0 confidence based on trend strength
            
            return TradeSignal(
                symbol=analysis_result.symbol,
                signal_type=SignalType.BUY,
                strategy_type=StrategyType.TREND_FOLLOWING,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                size_percent=config.position_sizing.size_percent,
                confidence=min(0.9, confidence),  # Cap confidence at 0.9
                timestamp=analysis_result.timestamp,
                metadata={
                    'ma_fast': ma_fast.value,
                    'ma_slow': ma_slow.value,
                    'macd': macd.value,
                    'macd_signal': macd_signal.value,
                    'trend_strength': trend_strength
                }
            )
        
        # Check for downtrend (potential sell)
        elif (ma_fast.value < ma_slow.value and 
              macd.value < macd_signal.value and
              current_price < ma_fast.value):
            
            entry_price = current_price
            stop_loss = entry_price * (1 + config.position_sizing.stop_loss_pct)
            take_profit = entry_price * (1 - config.position_sizing.take_profit_pct)
            
            # Calculate trend strength (0-1)
            trend_strength = min(1.0, (1 - ma_fast.value / ma_slow.value) * 100)  # Fast MA % below slow MA
            confidence = 0.5 + (trend_strength * 0.5)  # 0.5-1.0 confidence based on trend strength
            
            return TradeSignal(
                symbol=analysis_result.symbol,
                signal_type=SignalType.SELL,
                strategy_type=StrategyType.TREND_FOLLOWING,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                size_percent=config.position_sizing.size_percent,
                confidence=min(0.9, confidence),  # Cap confidence at 0.9
                timestamp=analysis_result.timestamp,
                metadata={
                    'ma_fast': ma_fast.value,
                    'ma_slow': ma_slow.value,
                    'macd': macd.value,
                    'macd_signal': macd_signal.value,
                    'trend_strength': trend_strength
                }
            )
        
        return None
        
    def _should_rebalance_etfs(self) -> bool:
        """Check if it's time to rebalance the ETF portfolio."""
        if not hasattr(self, 'etf_data') or 'last_rebalance' not in self.etf_data:
            return True
            
        from datetime import datetime, timedelta
        days_since_rebalance = (datetime.now() - self.etf_data['last_rebalance']).days
        return days_since_rebalance >= self.config.get('etf_rebalance_days', 21)
        
    async def _score_etf(self, analysis_result: AnalysisResult) -> Optional[float]:
        """Score an ETF based on various factors."""
        try:
            symbol = analysis_result.symbol
            indicators = {ind.name.lower(): ind for ind in analysis_result.indicators}
            
            # Basic validation
            if not indicators or 'close' not in indicators:
                return None
                
            # Get price data
            close_price = indicators['close'].value
            if close_price < self.config.get('etf_min_price', 10.0):
                return None
                
            # Calculate momentum score (0-100)
            momentum_score = self._calculate_momentum_score(indicators)
            
            # Calculate volume score (0-100)
            volume_score = self._calculate_volume_score(indicators)
            
            # Calculate trend score (0-100)
            trend_score = self._calculate_trend_score(indicators)
            
            # Calculate volatility score (0-100, higher is better - less volatile)
            volatility_score = self._calculate_volatility_score(indicators)
            
            # Combine scores with weights
            weights = {
                'momentum': 0.4,
                'volume': 0.2,
                'trend': 0.3,
                'volatility': 0.1
            }
            
            total_score = (
                momentum_score * weights['momentum'] +
                volume_score * weights['volume'] +
                trend_score * weights['trend'] +
                volatility_score * weights['volatility']
            )
            
            # Store metrics for analysis
            if not hasattr(self, 'etf_data'):
                self.etf_data = {'etf_metrics': {}}
            elif 'etf_metrics' not in self.etf_data:
                self.etf_data['etf_metrics'] = {}
                
            self.etf_data['etf_metrics'][symbol] = {
                'momentum_score': momentum_score,
                'volume_score': volume_score,
                'trend_score': trend_score,
                'volatility_score': volatility_score,
                'total_score': total_score,
                'timestamp': datetime.now().isoformat()
            }
            
            return total_score
            
        except Exception as e:
            logger.error(f"Error scoring ETF {symbol}: {str(e)}", exc_info=True)
            return None
            
    def _calculate_momentum_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate momentum score (0-100) based on RSI and MACD."""
        score = 50  # Neutral score
        
        # RSI component (0-50 points)
        if 'rsi' in indicators:
            rsi = indicators['rsi'].value
            # Score from 0-50 based on RSI position between 30-70
            rsi_score = max(0, min(50, (rsi - 30) * 1.25))
            score = (score * 0.5) + (rsi_score * 0.5)
        
        # MACD component (0-50 points)
        if 'macd' in indicators and 'macd_signal' in indicators:
            macd = indicators['macd'].value
            macd_signal = indicators['macd_signal'].value
            if macd > macd_signal:
                score += 25  # Bullish MACD crossover
        
        return min(100, max(0, score))  # Clamp between 0-100
        
    def _calculate_volume_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate volume score (0-100) based on volume indicators."""
        if 'volume' not in indicators or 'volume_ma' not in indicators:
            return 50
            
        volume = indicators['volume'].value
        volume_ma = indicators['volume_ma'].value
        
        if volume_ma == 0:
            return 50
            
        volume_ratio = volume / volume_ma
        
        # Score from 0-100 based on volume ratio (0.5x to 2x average)
        if volume_ratio < 0.5:
            return 0
        elif volume_ratio > 2.0:
            return 100
        else:
            return (volume_ratio - 0.5) * (100 / 1.5)
            
    def _calculate_trend_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate trend score (0-100) based on moving averages."""
        score = 50
        
        # Check 50/200 MA crossover
        if 'sma_50' in indicators and 'sma_200' in indicators:
            sma_50 = indicators['sma_50'].value
            sma_200 = indicators['sma_200'].value
            
            if sma_50 > sma_200:
                score += 25  # Uptrend
            else:
                score -= 15  # Downtrend
                
        # Check price relative to MAs
        if 'close' in indicators and 'sma_50' in indicators:
            close = indicators['close'].value
            sma_50 = indicators['sma_50'].value
            
            if close > sma_50:
                score += 15
            else:
                score -= 10
                
        return min(100, max(0, score))  # Clamp between 0-100
        
    def _calculate_volatility_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate volatility score (0-100, higher is better - less volatile)."""
        if 'atr' not in indicators or 'close' not in indicators:
            return 50
            
        atr = indicators['atr'].value
        close = indicators['close'].value
        
        if close == 0:
            return 50
            
        atr_pct = (atr / close) * 100
        
        # Lower ATR% is better (less volatile)
        if atr_pct < 1.0:
            return 100
        elif atr_pct > 5.0:
            return 0
        else:
            return 100 - ((atr_pct - 1.0) * 25)  # Scale from 1-5% to 100-0
            
    def _get_etf_sector(self, symbol: str) -> str:
        """Get the sector for an ETF symbol."""
        # In a real implementation, this would come from a data source
        # For now, we'll use a simple mapping of common sector ETFs
        sector_map = {
            'XLF': 'Financials',
            'XLK': 'Technology',
            'XLE': 'Energy',
            'XLV': 'Healthcare',
            'XLI': 'Industrials',
            'XLB': 'Materials',
            'XLY': 'Consumer Discretionary',
            'XLP': 'Consumer Staples',
            'XTL': 'Telecom',
            'XLU': 'Utilities',
            'XLRE': 'Real Estate',
            'XLC': 'Communication Services'
        }
        return sector_map.get(symbol, 'Other')
        
    def _is_last_etf(self, symbol: str) -> bool:
        """Check if this is the last ETF in our universe to be processed."""
        if not hasattr(self, 'etf_data') or 'sector_scores' not in self.etf_data:
            return False
            
        processed_etfs = set()
        for sector in self.etf_data['sector_scores'].values():
            processed_etfs.update(sector.keys())
            
        etf_universe = set(self.config.get('etf_sectors', []))
        remaining_etfs = etf_universe - processed_etfs
        
        return len(remaining_etfs) <= 1  # Last one or none left
        
    async def _select_top_etfs_and_generate_signals(
        self,
        strategy_config: StrategyConfig
    ) -> Optional[TradeSignal]:
        """Select top ETFs across sectors and generate trade signals."""
        try:
            if not hasattr(self, 'etf_data') or 'sector_scores' not in self.etf_data:
                logger.warning("No ETF data available for selection")
                return None
                
            # Flatten all ETF scores
            all_etf_scores = []
            for sector, etfs in self.etf_data['sector_scores'].items():
                for symbol, score in etfs.items():
                    all_etf_scores.append((symbol, score, sector))
            
            if not all_etf_scores:
                logger.warning("No ETFs to select from")
                return None
                
            # Sort by score (descending)
            all_etf_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Select top N ETFs, ensuring sector diversification
            selected_etfs = []
            sectors_used = set()
            
            for symbol, score, sector in all_etf_scores:
                if sector not in sectors_used:
                    selected_etfs.append((symbol, score, sector))
                    sectors_used.add(sector)
                    
                    if len(selected_etfs) >= strategy_config.max_positions:
                        break
            
            # If we didn't fill all slots, add remaining highest scoring ETFs
            if len(selected_etfs) < strategy_config.max_positions:
                remaining_slots = strategy_config.max_positions - len(selected_etfs)
                for symbol, score, sector in all_etf_scores:
                    if (symbol, score, sector) not in selected_etfs:
                        selected_etfs.append((symbol, score, sector))
                        remaining_slots -= 1
                        if remaining_slots <= 0:
                            break
            
            logger.info(f"Selected {len(selected_etfs)} top ETFs: {selected_etfs}")
            
            # Generate trade signals for selected ETFs
            signals = []
            for symbol, score, sector in selected_etfs:
                signal = await self._create_etf_buy_signal(
                    symbol, score, strategy_config
                )
                if signal:
                    signals.append(signal)
            
            # Update rebalance timestamp
            from datetime import datetime
            self.etf_data['last_rebalance'] = datetime.now()
            
            # Clear sector scores for next rebalance
            self.etf_data['sector_scores'] = {}
            
            # Return the first signal (others will be processed in subsequent calls)
            return signals[0] if signals else None
            
        except Exception as e:
            logger.error(f"Error selecting top ETFs: {str(e)}", exc_info=True)
            return None
            
    async def _create_etf_buy_signal(
        self,
        symbol: str,
        score: float,
        strategy_config: StrategyConfig
    ) -> Optional[TradeSignal]:
        """Create a buy signal for an ETF."""
        try:
            # In a real implementation, this would come from the data agent
            current_price = 100.0  # Placeholder - would come from market data
            atr = 2.0  # Placeholder - would come from indicators
            
            # Calculate position size based on portfolio allocation
            position_size_pct = min(
                strategy_config.position_sizing.size_percent,
                strategy_config.position_sizing.max_position_size
            )
            
            # Calculate stop loss and take profit
            stop_loss_pct = strategy_config.position_sizing.stop_loss_pct
            take_profit_pct = strategy_config.position_sizing.take_profit_pct
            
            # Adjust position size based on score (higher score = larger position)
            score_multiplier = score / 100.0  # 0.0 to 1.0
            position_size_pct *= (0.5 + (score_multiplier * 0.5))  # 50-100% of target
            
            # Create the trade signal
            signal = TradeSignal(
                symbol=symbol,
                signal_type=SignalType.BUY,
                strategy_type=StrategyType.ETF_STRATEGY,
                entry_price=current_price,
                stop_loss=current_price * (1 - stop_loss_pct),
                take_profit=current_price * (1 + take_profit_pct),
                size_percent=position_size_pct,
                confidence=score / 100.0,  # Convert 0-100 to 0.0-1.0
                timestamp=int(datetime.now().timestamp()),
                metadata={
                    'strategy': 'etf_sector_rotation',
                    'score': score,
                    'sector': self._get_etf_sector(symbol),
                    'atr': atr,
                    'stop_loss_type': 'atr',
                    'take_profit_type': 'fixed_percent'
                }
            )
            
            # Update current holdings
            if not hasattr(self, 'etf_data'):
                self.etf_data = {'current_holdings': {}}
            elif 'current_holdings' not in self.etf_data:
                self.etf_data['current_holdings'] = {}
                
            self.etf_data['current_holdings'][symbol] = {
                'entry_price': current_price,
                'entry_time': datetime.now().isoformat(),
                'size_pct': position_size_pct,
                'score': score,
                'metadata': signal.metadata
            }
            
            logger.info(f"Generated BUY signal for {symbol} (Score: {score:.1f}, Size: {position_size_pct*100:.1f}%)")
            return signal
            
        except Exception as e:
            logger.error(f"Error creating ETF buy signal for {symbol}: {str(e)}", exc_info=True)
            return None
            
    def _create_etf_hold_signal(
        self,
        symbol: str,
        analysis_result: AnalysisResult,
        strategy_config: StrategyConfig
    ) -> Optional[TradeSignal]:
        """Create a hold signal for an existing ETF position."""
        try:
            if not hasattr(self, 'etf_data') or 'current_holdings' not in self.etf_data:
                return None
                
            if symbol not in self.etf_data['current_holdings']:
                return None
                
            holding = self.etf_data['current_holdings'][symbol]
            current_price = analysis_result.current_price
            
            # Calculate current P&L
            entry_price = holding['entry_price']
            pnl_pct = (current_price - entry_price) / entry_price if entry_price > 0 else 0
            
            # Update holding with current metrics
            holding['current_price'] = current_price
            holding['pnl_pct'] = pnl_pct
            holding['last_updated'] = datetime.now().isoformat()
            
            # Create a HOLD signal with current metrics
            signal = TradeSignal(
                symbol=symbol,
                signal_type=SignalType.HOLD,
                strategy_type=StrategyType.ETF_STRATEGY,
                entry_price=entry_price,
                current_price=current_price,
                stop_loss=entry_price * (1 - strategy_config.position_sizing.stop_loss_pct),
                take_profit=entry_price * (1 + strategy_config.position_sizing.take_profit_pct),
                size_percent=holding['size_pct'],
                confidence=holding.get('score', 50) / 100.0,
                timestamp=int(datetime.now().timestamp()),
                metadata={
                    **holding.get('metadata', {}),
                    'current_pnl_pct': pnl_pct,
                    'days_held': (datetime.now() - datetime.fromisoformat(holding['entry_time'])).days,
                    'signal_reason': 'Holding position',
                    'last_analysis': analysis_result.to_dict() if hasattr(analysis_result, 'to_dict') else {}
                }
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error creating HOLD signal for {symbol}: {str(e)}", exc_info=True)
            return None
    
    def _select_best_signal(self, signals: List[TradeSignal]) -> Optional[TradeSignal]:
        """Select the best signal from a list of signals."""
        if not signals:
            return None
            
        # Sort by confidence (descending) and strategy priority
        sorted_signals = sorted(
            signals,
            key=lambda x: (
                x.confidence,
                1 if x.strategy_type == StrategyType.TREND_FOLLOWING else 0.5,  # Prefer trend following
            ),
            reverse=True
        )
        
        return sorted_signals[0] if sorted_signals else None
    
    async def update_portfolio_value(self, new_value: float):
        """Update the current portfolio value."""
        if new_value > 0:
            self.portfolio = new_value
            logger.info(f"Updated portfolio value to ${self.portfolio:,.2f}")
    
    async def get_active_positions(self) -> Dict[str, Dict]:
        """Get current active positions."""
        return self.positions.copy()
    
    async def add_position(self, symbol: str, position: Dict):
        """Add a new position."""
        self.positions[symbol] = position
        logger.info(f"Added position: {symbol} - {position}")
    
    async def remove_position(self, symbol: str):
        """Remove a position."""
        if symbol in self.positions:
            del self.positions[symbol]
            logger.info(f"Removed position: {symbol}")
