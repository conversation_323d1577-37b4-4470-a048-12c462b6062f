#!/usr/bin/env python3
"""
Quick test to verify Loop 7B SW project structure and imports.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all imports work correctly"""
    print("🧪 Testing Loop 7B SW imports...")
    
    try:
        # Test main package import
        import loop_7b_sw
        print("✅ Main package import: OK")
        
        # Test version info
        version_info = loop_7b_sw.get_version_info()
        print(f"✅ Version: {version_info['version']}")
        print(f"✅ Features: {len(version_info['features'])} listed")
        
        # Test system requirements
        requirements = loop_7b_sw.check_system_requirements()
        print(f"✅ System check: {requirements['total_ram_gb']:.1f}GB RAM available")
        
        # Test core classes
        from loop_7b_sw import StreamingInference
        print("✅ StreamingInference import: OK")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_project_structure():
    """Test that all required files exist"""
    print("\n📁 Testing project structure...")
    
    required_files = [
        "README.md",
        "LICENSE", 
        "requirements.txt",
        "setup.py",
        "src/loop_7b_sw/__init__.py",
        "src/loop_7b_sw/streaming_inference.py",
        "scripts/run_inference.py",
        "docs/BENCHMARKS.md"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_exist = False
    
    if all_exist:
        print("\n🎉 All required files present!")
    else:
        print("\n⚠️ Some files missing!")
    
    return all_exist

def main():
    """Run all tests"""
    print("🧬 Loop 7B SW - Project Test")
    print("=" * 40)
    
    structure_ok = test_project_structure()
    imports_ok = test_imports()
    
    if structure_ok and imports_ok:
        print("\n🚀 PROJECT READY FOR GITHUB!")
        print("   All tests passed ✅")
        print("   Structure complete ✅") 
        print("   Imports working ✅")
        print("   Documentation ready ✅")
        return True
    else:
        print("\n⚠️ Project needs fixes before upload")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
