#!/usr/bin/env python3
"""
DIRECT COMPRESSION TEST
======================

Direct test of compression techniques on Mistral 7B
No complex frameworks - just measure what works
"""

import torch
import psutil
import time
import json
import gc
import os
from safetensors import safe_open

def get_ram_gb():
    """Get current RAM usage in GB"""
    process = psutil.Process()
    return process.memory_info().rss / (1024**3)

def main():
    """Direct compression test"""
    
    print("🚀 DIRECT COMPRESSION TEST")
    print("=" * 40)
    
    # Check if model exists
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    print(f"✅ Model found: {model_path}")
    
    # Initial RAM
    ram_start = get_ram_gb()
    print(f"📊 Starting RAM: {ram_start:.3f}GB")
    
    try:
        # Load model index
        index_path = os.path.join(model_path, "model.safetensors.index.json")
        
        if not os.path.exists(index_path):
            print(f"❌ Index not found: {index_path}")
            return
        
        with open(index_path, 'r') as f:
            weight_index = json.load(f)
        
        print(f"✅ Model index loaded")
        print(f"📊 Total weights: {len(weight_index['weight_map'])}")
        
        # Find a test layer
        test_layer = None
        for layer_name in weight_index['weight_map'].keys():
            if 'q_proj.weight' in layer_name:
                test_layer = layer_name
                break
        
        if not test_layer:
            print("❌ No suitable test layer found")
            return
        
        print(f"📊 Test layer: {test_layer}")
        
        # Load the layer
        file_name = weight_index['weight_map'][test_layer]
        file_path = os.path.join(model_path, file_name)
        
        print(f"📁 Loading from: {file_name}")
        
        ram_before_load = get_ram_gb()
        
        with safe_open(file_path, framework="pt", device="cpu") as f:
            tensor = f.get_tensor(test_layer)
            
            ram_after_load = get_ram_gb()
            
            print(f"✅ Layer loaded successfully")
            print(f"📊 Shape: {tensor.shape}")
            print(f"📊 Dtype: {tensor.dtype}")
            print(f"📊 RAM increase: {ram_after_load - ram_before_load:.3f}GB")
            
            # Calculate original size
            original_size_mb = tensor.numel() * tensor.element_size() / (1024**2)
            print(f"📊 Original size: {original_size_mb:.1f}MB")
            
            # Test 1: Simple 1-bit quantization
            print(f"\n⚡ TESTING 1-BIT QUANTIZATION")
            print("-" * 30)
            
            # Convert to float32 for processing
            tensor_f32 = tensor.to(torch.float32)
            
            # Calculate statistics
            tensor_mean = torch.mean(tensor_f32)
            tensor_std = torch.std(tensor_f32)
            
            print(f"📊 Mean: {tensor_mean:.6f}")
            print(f"📊 Std: {tensor_std:.6f}")
            
            # Apply 1-bit quantization (sign-based)
            centered = tensor_f32 - tensor_mean
            binary_weights = torch.sign(centered)  # -1 or +1
            
            # Convert to uint8 for storage
            binary_uint8 = ((binary_weights + 1) / 2).to(torch.uint8)  # 0 or 1
            
            # Calculate compression
            compressed_size_mb = binary_uint8.numel() * binary_uint8.element_size() / (1024**2)
            compression_ratio = original_size_mb / compressed_size_mb
            
            print(f"✅ Quantization complete")
            print(f"📊 Compressed size: {compressed_size_mb:.1f}MB")
            print(f"📊 Compression ratio: {compression_ratio:.1f}×")
            
            # Quality test
            print(f"\n🔍 QUALITY ASSESSMENT")
            print("-" * 25)
            
            # Reconstruct
            reconstructed = (binary_uint8.to(torch.float32) * 2 - 1) * tensor_std + tensor_mean
            
            # Calculate errors
            mse_error = torch.mean((tensor_f32 - reconstructed) ** 2).item()
            mae_error = torch.mean(torch.abs(tensor_f32 - reconstructed)).item()
            
            tensor_range = torch.max(tensor_f32) - torch.min(tensor_f32)
            relative_error = mae_error / tensor_range.item() if tensor_range > 0 else 0
            
            print(f"📊 MSE error: {mse_error:.8f}")
            print(f"📊 MAE error: {mae_error:.8f}")
            print(f"📊 Relative error: {relative_error*100:.2f}%")
            
            # Test computation
            if tensor.dim() == 2 and tensor.shape[1] <= 4096:  # Reasonable size for test
                print(f"\n🧮 COMPUTATION TEST")
                print("-" * 20)

                test_input = torch.randn(1, tensor.shape[1])

                # Original computation (convert tensor to float32 for compatibility)
                tensor_f32_for_compute = tensor.to(torch.float32)
                original_output = torch.matmul(test_input, tensor_f32_for_compute.t())

                # Quantized computation
                quantized_output = torch.matmul(test_input, reconstructed.t())
                
                # Compare outputs
                output_diff = torch.abs(original_output - quantized_output)
                max_diff = torch.max(output_diff).item()
                mean_diff = torch.mean(output_diff).item()
                
                original_magnitude = torch.mean(torch.abs(original_output)).item()
                relative_output_error = mean_diff / original_magnitude if original_magnitude > 0 else 0
                
                print(f"📊 Max output difference: {max_diff:.6f}")
                print(f"📊 Mean output difference: {mean_diff:.6f}")
                print(f"📊 Relative output error: {relative_output_error*100:.2f}%")
            
            # Test 2: Memory streaming simulation
            print(f"\n🔄 MEMORY STREAMING TEST")
            print("-" * 30)
            
            # Simulate loading multiple layers vs streaming
            ram_before_multi = get_ram_gb()
            
            # Load 2 more layers to simulate multi-layer loading
            additional_layers = []
            layer_count = 0
            
            for layer_name in weight_index['weight_map'].keys():
                if 'k_proj.weight' in layer_name or 'v_proj.weight' in layer_name:
                    additional_layers.append(layer_name)
                    layer_count += 1
                    if layer_count >= 2:
                        break
            
            loaded_tensors = [tensor]  # Already have one loaded
            
            for layer_name in additional_layers:
                file_name = weight_index['weight_map'][layer_name]
                file_path = os.path.join(model_path, file_name)
                
                with safe_open(file_path, framework="pt", device="cpu") as f:
                    additional_tensor = f.get_tensor(layer_name)
                    loaded_tensors.append(additional_tensor)
            
            ram_multi_loaded = get_ram_gb()
            multi_load_increase = ram_multi_loaded - ram_before_multi
            
            print(f"📊 Multi-layer RAM increase: {multi_load_increase:.3f}GB")
            
            # Clear and test streaming
            loaded_tensors.clear()
            gc.collect()
            
            ram_after_clear = get_ram_gb()
            
            # Simulate streaming (load one, process, unload)
            max_streaming_ram = ram_after_clear
            
            for layer_name in [test_layer] + additional_layers:
                file_name = weight_index['weight_map'][layer_name]
                file_path = os.path.join(model_path, file_name)
                
                with safe_open(file_path, framework="pt", device="cpu") as f:
                    stream_tensor = f.get_tensor(layer_name)
                    
                    current_ram = get_ram_gb()
                    max_streaming_ram = max(max_streaming_ram, current_ram)
                    
                    # Simulate processing
                    if stream_tensor.dim() == 2:
                        test_input = torch.randn(1, min(stream_tensor.shape[1], 1000))
                        if test_input.shape[1] == stream_tensor.shape[1]:
                            _ = torch.matmul(test_input, stream_tensor.t())
                    
                    del stream_tensor
                    gc.collect()
            
            streaming_increase = max_streaming_ram - ram_after_clear
            
            print(f"📊 Streaming RAM increase: {streaming_increase:.3f}GB")
            
            if multi_load_increase > 0 and streaming_increase > 0:
                streaming_efficiency = multi_load_increase / streaming_increase
                print(f"📊 Streaming efficiency: {streaming_efficiency:.2f}×")
            
            # Final results
            print(f"\n📊 FINAL RESULTS")
            print("=" * 20)
            print(f"1-bit compression: {compression_ratio:.1f}×")
            print(f"Quality loss: {relative_error*100:.2f}%")
            
            if 'streaming_efficiency' in locals():
                print(f"Streaming efficiency: {streaming_efficiency:.2f}×")
                
                # Combined projection
                combined_compression = compression_ratio * streaming_efficiency
                print(f"Combined compression: {combined_compression:.1f}×")
                
                # Project to full model
                baseline_7b = 2.58  # GB from our verified baseline
                compressed_7b = baseline_7b / combined_compression
                
                print(f"\n🚀 PROJECTION:")
                print(f"7B model: {baseline_7b:.2f}GB → {compressed_7b:.3f}GB")
                
                # Scale to 675B
                scaling_factor = 675 / 7
                projected_675b = compressed_7b * scaling_factor
                
                print(f"675B model: {projected_675b:.1f}GB")
                print(f"Fits 8GB laptop: {'✅ YES' if projected_675b <= 6.0 else '❌ NO'}")
            
            # Save results
            results = {
                'timestamp': time.time(),
                'test_layer': test_layer,
                'original_size_mb': original_size_mb,
                'compression_ratio': compression_ratio,
                'quality_loss_percent': relative_error * 100,
                'streaming_efficiency': locals().get('streaming_efficiency', 1.0),
                'combined_compression': locals().get('combined_compression', compression_ratio)
            }
            
            with open('direct_compression_results.json', 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\n✅ Results saved: direct_compression_results.json")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        ram_end = get_ram_gb()
        print(f"\n📊 Final RAM: {ram_end:.3f}GB")
        print(f"📊 Total increase: {ram_end - ram_start:.3f}GB")

if __name__ == "__main__":
    main()
