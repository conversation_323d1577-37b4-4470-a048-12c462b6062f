"""
Test script for DataCollectionAgent's data fetching functionality.
"""
import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_data_fetch.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

async def test_fetch_single_symbol():
    """Test fetching data for a single symbol."""
    logger.info("Starting test_fetch_single_symbol...")
    
    # Initialize the agent with mock LLM
    mock_llm = MistralWrapper()
    agent = DataCollectionAgent(llm_wrapper=mock_llm)
    
    try:
        # Start the agent
        logger.info("Starting agent...")
        await agent.start()
        
        # Test fetching data for a single symbol
        symbol = "AAPL"
        logger.info(f"Fetching data for {symbol}...")
        
        # Fetch with a short timeout for testing
        ohlcv = await agent.fetch_ohlcv(
            symbol=symbol,
            interval="1d",
            period="1mo",
            timeout=10  # 10 second timeout for testing
        )
        
        if ohlcv:
            logger.info(f"Successfully fetched {len(ohlcv.timestamp)} data points for {symbol}")
            logger.info(f"Date range: {ohlcv.timestamp[0]} to {ohlcv.timestamp[-1]}")
            logger.info(f"Latest close: ${ohlcv.close[-1]:.2f}")
            logger.info(f"Volume: {ohlcv.volume[-1]:,}")
        else:
            logger.error(f"Failed to fetch data for {symbol}")
            
    except Exception as e:
        logger.error(f"Error in test_fetch_single_symbol: {str(e)}", exc_info=True)
    finally:
        # Stop the agent
        if agent.is_running:
            logger.info("Stopping agent...")
            await agent.stop()
            logger.info("Agent stopped.")

async def main():
    """Run the test."""
    await test_fetch_single_symbol()

if __name__ == "__main__":
    asyncio.run(main())
