#!/usr/bin/env python3
"""
Tool Augmentation System
Adds calculator, search, and knowledge tools to enhance intelligence
Goal: Boost intelligence from 50.9% to 65-70%
"""

import math
import re
import time
import requests
from typing import Dict, Any, List, Optional

class CalculatorTool:
    """Advanced calculator tool for mathematical operations"""
    
    def __init__(self):
        self.calculation_history = []
    
    def calculate(self, expression: str) -> Dict[str, Any]:
        """Safely evaluate mathematical expressions"""
        
        try:
            # Clean the expression
            cleaned_expr = self._clean_expression(expression)
            
            # Safe evaluation
            result = self._safe_eval(cleaned_expr)
            
            calculation_record = {
                'expression': expression,
                'cleaned_expression': cleaned_expr,
                'result': result,
                'timestamp': time.time(),
                'success': True
            }
            
            self.calculation_history.append(calculation_record)
            
            return {
                'result': result,
                'expression': cleaned_expr,
                'success': True,
                'tool': 'calculator'
            }
            
        except Exception as e:
            error_record = {
                'expression': expression,
                'error': str(e),
                'timestamp': time.time(),
                'success': False
            }
            
            self.calculation_history.append(error_record)
            
            return {
                'result': None,
                'error': str(e),
                'success': False,
                'tool': 'calculator'
            }
    
    def _clean_expression(self, expr: str) -> str:
        """Clean and prepare expression for evaluation"""
        
        # Remove common text and keep only mathematical parts
        expr = expr.lower()
        
        # Replace common mathematical terms
        replacements = {
            'x': '*',
            'times': '*',
            'plus': '+',
            'minus': '-',
            'divided by': '/',
            'divide': '/',
            'squared': '**2',
            'cubed': '**3',
            'sqrt': 'math.sqrt',
            'sin': 'math.sin',
            'cos': 'math.cos',
            'tan': 'math.tan',
            'log': 'math.log',
            'ln': 'math.log',
            'exp': 'math.exp',
            'pi': 'math.pi',
            'e': 'math.e'
        }
        
        for old, new in replacements.items():
            expr = expr.replace(old, new)
        
        # Extract mathematical expression
        math_pattern = r'[0-9+\-*/().\s\w]+'
        matches = re.findall(math_pattern, expr)
        
        if matches:
            return matches[0].strip()
        
        return expr.strip()
    
    def _safe_eval(self, expr: str) -> float:
        """Safely evaluate mathematical expression"""
        
        # Allowed names for evaluation
        allowed_names = {
            "__builtins__": {},
            "math": math,
            "abs": abs,
            "round": round,
            "min": min,
            "max": max,
            "sum": sum,
            "pow": pow
        }
        
        # Evaluate safely
        result = eval(expr, allowed_names)
        
        return float(result)
    
    def solve_equation(self, equation: str) -> Dict[str, Any]:
        """Solve simple algebraic equations"""
        
        try:
            # Handle simple linear equations like "2x + 5 = 17"
            if '=' in equation:
                left, right = equation.split('=')
                left = left.strip()
                right = right.strip()
                
                # Extract coefficient and constant from left side
                # Simple pattern: ax + b = c
                if 'x' in left:
                    # Parse left side
                    parts = left.replace('-', '+-').split('+')
                    coefficient = 0
                    constant = 0
                    
                    for part in parts:
                        part = part.strip()
                        if 'x' in part:
                            coef_str = part.replace('x', '').strip()
                            if coef_str == '' or coef_str == '+':
                                coefficient = 1
                            elif coef_str == '-':
                                coefficient = -1
                            else:
                                coefficient = float(coef_str)
                        elif part:
                            constant += float(part)
                    
                    # Solve: ax + b = c -> x = (c - b) / a
                    right_value = float(right)
                    if coefficient != 0:
                        x = (right_value - constant) / coefficient
                        
                        return {
                            'equation': equation,
                            'solution': f'x = {x}',
                            'x_value': x,
                            'success': True,
                            'tool': 'equation_solver'
                        }
            
            return {
                'equation': equation,
                'error': 'Could not solve equation',
                'success': False,
                'tool': 'equation_solver'
            }
            
        except Exception as e:
            return {
                'equation': equation,
                'error': str(e),
                'success': False,
                'tool': 'equation_solver'
            }

class KnowledgeTool:
    """Knowledge tool for factual information"""
    
    def __init__(self):
        self.knowledge_base = {
            # Mathematical constants and facts
            'pi': 3.14159265359,
            'e': 2.71828182846,
            'golden_ratio': 1.61803398875,
            
            # Mathematical formulas
            'derivative_power_rule': 'd/dx(x^n) = n*x^(n-1)',
            'integral_power_rule': '∫x^n dx = x^(n+1)/(n+1) + C',
            'quadratic_formula': 'x = (-b ± √(b²-4ac)) / 2a',
            
            # Logical principles
            'modus_ponens': 'If P implies Q, and P is true, then Q is true',
            'modus_tollens': 'If P implies Q, and Q is false, then P is false',
            'syllogism': 'If A implies B, and B implies C, then A implies C',
            
            # Common facts
            'speed_of_light': 299792458,  # m/s
            'gravity': 9.81,  # m/s²
            'avogadro_number': 6.022e23,
        }
        
        self.query_history = []
    
    def lookup(self, query: str) -> Dict[str, Any]:
        """Look up information in knowledge base"""
        
        query_lower = query.lower()
        
        # Search for relevant knowledge
        matches = []
        for key, value in self.knowledge_base.items():
            if any(word in key.lower() for word in query_lower.split()):
                matches.append({
                    'key': key,
                    'value': value,
                    'relevance': self._calculate_relevance(query_lower, key)
                })
        
        # Sort by relevance
        matches.sort(key=lambda x: x['relevance'], reverse=True)
        
        query_record = {
            'query': query,
            'matches_found': len(matches),
            'timestamp': time.time()
        }
        
        self.query_history.append(query_record)
        
        if matches:
            best_match = matches[0]
            return {
                'query': query,
                'result': best_match['value'],
                'key': best_match['key'],
                'confidence': best_match['relevance'],
                'success': True,
                'tool': 'knowledge_lookup'
            }
        else:
            return {
                'query': query,
                'result': None,
                'error': 'No relevant information found',
                'success': False,
                'tool': 'knowledge_lookup'
            }
    
    def _calculate_relevance(self, query: str, key: str) -> float:
        """Calculate relevance score between query and knowledge key"""
        
        query_words = set(query.split())
        key_words = set(key.split('_'))
        
        if not query_words or not key_words:
            return 0.0
        
        intersection = query_words.intersection(key_words)
        union = query_words.union(key_words)
        
        return len(intersection) / len(union)

class ToolAugmentedReasoning:
    """Tool-augmented reasoning system"""
    
    def __init__(self, model):
        self.model = model
        self.calculator = CalculatorTool()
        self.knowledge = KnowledgeTool()
        self.tool_usage_history = []
    
    def solve_with_tools(self, problem: str, problem_type: str = "general") -> Dict[str, Any]:
        """Solve problem using available tools"""
        
        print(f"🛠️ Solving with tool augmentation: {problem[:50]}...")
        
        # Analyze what tools might be needed
        tools_needed = self._analyze_tool_requirements(problem, problem_type)
        
        # Use tools to gather information
        tool_results = {}
        for tool_name in tools_needed:
            if tool_name == 'calculator':
                calc_result = self._use_calculator_for_problem(problem)
                if calc_result['success']:
                    tool_results['calculator'] = calc_result
            
            elif tool_name == 'knowledge':
                knowledge_result = self._use_knowledge_for_problem(problem)
                if knowledge_result['success']:
                    tool_results['knowledge'] = knowledge_result
        
        # Generate solution using model + tool results
        enhanced_solution = self._generate_tool_enhanced_solution(problem, tool_results, problem_type)
        
        # Record tool usage
        usage_record = {
            'problem': problem,
            'problem_type': problem_type,
            'tools_used': list(tool_results.keys()),
            'tool_results': tool_results,
            'final_solution': enhanced_solution,
            'timestamp': time.time()
        }
        
        self.tool_usage_history.append(usage_record)
        
        return enhanced_solution
    
    def _analyze_tool_requirements(self, problem: str, problem_type: str) -> List[str]:
        """Analyze what tools are needed for the problem"""
        
        tools_needed = []
        problem_lower = problem.lower()
        
        # Check if calculator is needed
        math_indicators = [
            'solve', 'calculate', 'find', 'equation', 'derivative', 'integral',
            '+', '-', '*', '/', '=', 'x', 'y', 'limit', 'sin', 'cos', 'log'
        ]
        
        if any(indicator in problem_lower for indicator in math_indicators):
            tools_needed.append('calculator')
        
        # Check if knowledge lookup is needed
        knowledge_indicators = [
            'what is', 'define', 'formula', 'rule', 'principle', 'constant',
            'speed of light', 'gravity', 'pi', 'derivative', 'integral'
        ]
        
        if any(indicator in problem_lower for indicator in knowledge_indicators):
            tools_needed.append('knowledge')
        
        return tools_needed
    
    def _use_calculator_for_problem(self, problem: str) -> Dict[str, Any]:
        """Use calculator tool for mathematical aspects of problem"""
        
        # Try to extract mathematical expressions
        math_expressions = re.findall(r'[0-9+\-*/().\s]+[=]?[0-9+\-*/().\s]*', problem)
        
        for expr in math_expressions:
            if '=' in expr:
                # It's an equation
                result = self.calculator.solve_equation(expr)
                if result['success']:
                    return result
            else:
                # It's an expression
                result = self.calculator.calculate(expr)
                if result['success']:
                    return result
        
        # Try the whole problem as a mathematical expression
        return self.calculator.calculate(problem)
    
    def _use_knowledge_for_problem(self, problem: str) -> Dict[str, Any]:
        """Use knowledge tool for factual aspects of problem"""
        
        return self.knowledge.lookup(problem)
    
    def _generate_tool_enhanced_solution(self, problem: str, tool_results: Dict[str, Any], problem_type: str) -> Dict[str, Any]:
        """Generate solution using model enhanced with tool results"""
        
        # Create enhanced prompt with tool information
        tool_info = ""
        if tool_results:
            tool_info = "\n\nTool Results:\n"
            for tool_name, result in tool_results.items():
                if result['success']:
                    tool_info += f"- {tool_name}: {result.get('result', result.get('solution', 'N/A'))}\n"
        
        enhanced_prompt = f"""
        Problem: {problem}
        Type: {problem_type}
        {tool_info}
        
        Using the tool results above, solve this problem step by step.
        Show your reasoning and provide a clear final answer.
        """
        
        try:
            response = self.model.generate(enhanced_prompt, max_length=200)
            
            return {
                'problem': problem,
                'final_answer': response,
                'tools_used': list(tool_results.keys()),
                'tool_results': tool_results,
                'confidence': 0.8,  # Higher confidence with tool augmentation
                'enhancement': 'tool_augmented',
                'success': True
            }
            
        except Exception as e:
            return {
                'problem': problem,
                'error': str(e),
                'tools_used': list(tool_results.keys()),
                'tool_results': tool_results,
                'success': False
            }
    
    def get_tool_usage_statistics(self) -> Dict[str, Any]:
        """Get statistics on tool usage"""
        
        if not self.tool_usage_history:
            return {'total_problems': 0}
        
        total_problems = len(self.tool_usage_history)
        
        tool_usage_count = {}
        for record in self.tool_usage_history:
            for tool in record['tools_used']:
                tool_usage_count[tool] = tool_usage_count.get(tool, 0) + 1
        
        successful_tool_usage = len([r for r in self.tool_usage_history if r['final_solution']['success']])
        
        return {
            'total_problems': total_problems,
            'successful_tool_usage': successful_tool_usage,
            'success_rate': successful_tool_usage / total_problems if total_problems > 0 else 0,
            'tool_usage_count': tool_usage_count,
            'calculator_calculations': len(self.calculator.calculation_history),
            'knowledge_queries': len(self.knowledge.query_history)
        }
