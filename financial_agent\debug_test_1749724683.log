2025-06-12 16:08:04,447 - asyncio - DEBUG - Using proactor: IocpProactor
2025-06-12 16:08:04,448 - root - INFO - Starting debug test...
2025-06-12 16:08:04,448 - root - INFO - Log file: D:\Loop\financial_agent\debug_test_1749724683.log
2025-06-12 16:08:04,449 - agent.data - INFO - Initialized data agent
2025-06-12 16:08:04,449 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 16:08:04,449 - root - INFO - Starting agent...
2025-06-12 16:08:04,450 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 16:08:04,450 - root - INFO - 
================================================================================
2025-06-12 16:08:04,452 - root - INFO - TESTING AAPL - Interval: 1d, Period: 1mo
2025-06-12 16:08:04,452 - root - INFO - ================================================================================
2025-06-12 16:08:04,452 - root - INFO - Fetching data for AAPL...
2025-06-12 16:08:04,452 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=AAPL, interval=1d, period=1mo, start=None, end=None, max_retries=2, retry_delay=1.0, timeout=45)
2025-06-12 16:08:04,453 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Using period: 1mo
2025-06-12 16:08:04,453 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Fetching data for AAPL (attempt 1/2)...
2025-06-12 16:08:04,453 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:08:04
2025-06-12 16:08:04,454 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Created yf.Ticker for AAPL
2025-06-12 16:08:04,454 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=45s
2025-06-12 16:08:04,455 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1d', 'period': '1mo', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:08:04,455 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:08:04
2025-06-12 16:08:04,455 - yfinance - DEBUG - Entering history()
2025-06-12 16:08:04,458 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-06-12 16:08:04,458 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-06-12 16:08:04,495 - yfinance - DEBUG -  Entering history()
2025-06-12 16:08:04,496 - yfinance - DEBUG - AAPL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:08:04,496 - yfinance - DEBUG -   Entering get()
2025-06-12 16:08:04,496 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 16:08:04,496 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-06-12 16:08:04,497 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:08:04,497 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 16:08:04,497 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 16:08:04,497 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 16:08:04,498 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 16:08:04,498 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-06-12 16:08:04,499 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-06-12 16:08:04,500 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-06-12 16:08:04,501 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-06-12 16:08:04,501 - yfinance - DEBUG - reusing persistent cookie
2025-06-12 16:08:04,501 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 16:08:04,501 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 16:08:04,502 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-06-12 16:08:04,502 - yfinance - DEBUG - reusing cookie
2025-06-12 16:08:04,502 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-06-12 16:08:04,774 - yfinance - DEBUG - crumb = 'iDku.3/e0AN'
2025-06-12 16:08:04,774 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 16:08:04,775 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 16:08:04,775 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 16:08:05,013 - yfinance - DEBUG - response code=200
2025-06-12 16:08:05,014 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 16:08:05,014 - yfinance - DEBUG -   Exiting get()
2025-06-12 16:08:05,015 - yfinance - DEBUG - AAPL: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 16:08:05,017 - yfinance - DEBUG - AAPL: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 16:08:05,025 - yfinance - DEBUG - AAPL: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:08:05,028 - yfinance - DEBUG - AAPL: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:08:05,028 - yfinance - DEBUG -  Exiting history()
2025-06-12 16:08:05,030 - yfinance - DEBUG - Exiting history()
2025-06-12 16:08:05,030 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.57s
2025-06-12 16:08:05,030 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Fetched 22 rows for AAPL
2025-06-12 16:08:05,031 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 22
2025-06-12 16:08:05,031 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:08:05,033 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Caching result with key: AAPL_1d_1mo_None_None
2025-06-12 16:08:05,033 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Successfully fetched 22 data points for AAPL in 0.58s
2025-06-12 16:08:05,033 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:08:05,034 - root - INFO - 
FETCH SUCCESSFUL
2025-06-12 16:08:05,034 - root - INFO - ----------------------------------------
2025-06-12 16:08:05,034 - root - INFO - Symbol: AAPL
2025-06-12 16:08:05,034 - root - INFO - Data points: 22
2025-06-12 16:08:05,034 - root - INFO - Date range: 1747022400 to 1749614400
2025-06-12 16:08:05,035 - root - INFO - Latest close: $198.78
2025-06-12 16:08:05,035 - root - INFO - Volume: 60,820,200
2025-06-12 16:08:05,035 - root - INFO - Time taken: 0.58 seconds
2025-06-12 16:08:06,041 - root - INFO - 
================================================================================
2025-06-12 16:08:06,041 - root - INFO - TESTING MSFT - Interval: 1h, Period: 5d
2025-06-12 16:08:06,042 - root - INFO - ================================================================================
2025-06-12 16:08:06,043 - root - INFO - Fetching data for MSFT...
2025-06-12 16:08:06,043 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=MSFT, interval=1h, period=5d, start=None, end=None, max_retries=2, retry_delay=1.0, timeout=45)
2025-06-12 16:08:06,043 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Using period: 5d
2025-06-12 16:08:06,043 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Fetching data for MSFT (attempt 1/2)...
2025-06-12 16:08:06,043 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:08:06
2025-06-12 16:08:06,043 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Created yf.Ticker for MSFT
2025-06-12 16:08:06,043 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=45s
2025-06-12 16:08:06,048 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1h', 'period': '5d', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:08:06,049 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:08:06
2025-06-12 16:08:06,049 - yfinance - DEBUG - Entering history()
2025-06-12 16:08:06,049 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['MSFT', 1, 0])
2025-06-12 16:08:06,050 - yfinance - DEBUG -  Entering history()
2025-06-12 16:08:06,050 - yfinance - DEBUG - MSFT: Yahoo GET parameters: {'range': '5d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:08:06,051 - yfinance - DEBUG -   Entering get()
2025-06-12 16:08:06,051 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 16:08:06,051 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/MSFT
2025-06-12 16:08:06,051 - yfinance - DEBUG - params={'range': '5d', 'interval': '1h', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:08:06,052 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 16:08:06,052 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 16:08:06,052 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 16:08:06,052 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 16:08:06,052 - yfinance - DEBUG - reusing cookie
2025-06-12 16:08:06,052 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 16:08:06,052 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 16:08:06,052 - yfinance - DEBUG - reusing crumb
2025-06-12 16:08:06,052 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 16:08:06,053 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 16:08:06,053 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 16:08:06,138 - yfinance - DEBUG - response code=200
2025-06-12 16:08:06,138 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 16:08:06,138 - yfinance - DEBUG -   Exiting get()
2025-06-12 16:08:06,138 - yfinance - DEBUG - MSFT: yfinance received OHLC data: 2025-06-05 13:30:00 -> 2025-06-11 20:00:00
2025-06-12 16:08:06,142 - yfinance - DEBUG - MSFT: OHLC after cleaning: 2025-06-05 09:30:00-04:00 -> 2025-06-11 15:30:00-04:00
2025-06-12 16:08:06,147 - yfinance - DEBUG - MSFT: OHLC after combining events: 2025-06-05 09:30:00-04:00 -> 2025-06-11 15:30:00-04:00
2025-06-12 16:08:06,151 - yfinance - DEBUG - MSFT: yfinance returning OHLC: 2025-06-05 09:30:00-04:00 -> 2025-06-11 15:30:00-04:00
2025-06-12 16:08:06,151 - yfinance - DEBUG -  Exiting history()
2025-06-12 16:08:06,151 - yfinance - DEBUG - Exiting history()
2025-06-12 16:08:06,152 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.10s
2025-06-12 16:08:06,152 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Fetched 35 rows for MSFT
2025-06-12 16:08:06,152 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 35
2025-06-12 16:08:06,153 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:08:06,154 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Caching result with key: MSFT_1h_5d_None_None
2025-06-12 16:08:06,154 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Successfully fetched 35 data points for MSFT in 0.11s
2025-06-12 16:08:06,155 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:08:06,155 - root - INFO - 
FETCH SUCCESSFUL
2025-06-12 16:08:06,155 - root - INFO - ----------------------------------------
2025-06-12 16:08:06,155 - root - INFO - Symbol: MSFT
2025-06-12 16:08:06,155 - root - INFO - Data points: 35
2025-06-12 16:08:06,156 - root - INFO - Date range: 1749130200 to 1749670200
2025-06-12 16:08:06,156 - root - INFO - Latest close: $472.90
2025-06-12 16:08:06,156 - root - INFO - Volume: 1,634,641
2025-06-12 16:08:06,156 - root - INFO - Time taken: 0.11 seconds
2025-06-12 16:08:07,167 - root - INFO - 
================================================================================
2025-06-12 16:08:07,167 - root - INFO - TESTING GOOGL - Interval: 1d, Period: 1y
2025-06-12 16:08:07,167 - root - INFO - ================================================================================
2025-06-12 16:08:07,167 - root - INFO - Fetching data for GOOGL...
2025-06-12 16:08:07,167 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Entering fetch_ohlcv(symbol=GOOGL, interval=1d, period=1y, start=None, end=None, max_retries=2, retry_delay=1.0, timeout=45)
2025-06-12 16:08:07,167 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Using period: 1y
2025-06-12 16:08:07,170 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Fetching data for GOOGL (attempt 1/2)...
2025-06-12 16:08:07,170 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Attempt 1 starting at 16:08:07
2025-06-12 16:08:07,171 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Created yf.Ticker for GOOGL
2025-06-12 16:08:07,171 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting asyncio.wait_for with timeout=45s
2025-06-12 16:08:07,171 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() params: {'interval': '1d', 'period': '1y', 'actions': False, 'auto_adjust': True, 'prepost': False}
2025-06-12 16:08:07,172 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Starting yfinance.history() at 16:08:07
2025-06-12 16:08:07,172 - yfinance - DEBUG - Entering history()
2025-06-12 16:08:07,172 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['GOOGL', 1, 0])
2025-06-12 16:08:07,172 - yfinance - DEBUG -  Entering history()
2025-06-12 16:08:07,173 - yfinance - DEBUG - GOOGL: Yahoo GET parameters: {'range': '1y', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:08:07,173 - yfinance - DEBUG -   Entering get()
2025-06-12 16:08:07,174 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 16:08:07,174 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/GOOGL
2025-06-12 16:08:07,174 - yfinance - DEBUG - params={'range': '1y', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 16:08:07,175 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 16:08:07,175 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 16:08:07,175 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 16:08:07,175 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 16:08:07,176 - yfinance - DEBUG - reusing cookie
2025-06-12 16:08:07,176 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 16:08:07,176 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 16:08:07,176 - yfinance - DEBUG - reusing crumb
2025-06-12 16:08:07,177 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 16:08:07,177 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 16:08:07,177 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 16:08:07,300 - yfinance - DEBUG - response code=200
2025-06-12 16:08:07,300 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 16:08:07,301 - yfinance - DEBUG -   Exiting get()
2025-06-12 16:08:07,303 - yfinance - DEBUG - GOOGL: yfinance received OHLC data: 2024-06-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 16:08:07,306 - yfinance - DEBUG - GOOGL: OHLC after cleaning: 2024-06-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 16:08:07,312 - yfinance - DEBUG - GOOGL: OHLC after combining events: 2024-06-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:08:07,316 - yfinance - DEBUG - GOOGL: yfinance returning OHLC: 2024-06-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 16:08:07,316 - yfinance - DEBUG -  Exiting history()
2025-06-12 16:08:07,316 - yfinance - DEBUG - Exiting history()
2025-06-12 16:08:07,316 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] yfinance.history() completed in 0.14s
2025-06-12 16:08:07,316 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Fetched 250 rows for GOOGL
2025-06-12 16:08:07,318 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Data fetch completed, rows: 250
2025-06-12 16:08:07,318 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Converting to OHLCVData...
2025-06-12 16:08:07,320 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Caching result with key: GOOGL_1d_1y_None_None
2025-06-12 16:08:07,321 - financial_agent.agents.data_agent - INFO - [DataCollectionAgent] Successfully fetched 250 data points for GOOGL in 0.15s
2025-06-12 16:08:07,321 - financial_agent.agents.data_agent - DEBUG - [DataCollectionAgent] Exiting fetch_ohlcv() - success
2025-06-12 16:08:07,321 - root - INFO - 
FETCH SUCCESSFUL
2025-06-12 16:08:07,321 - root - INFO - ----------------------------------------
2025-06-12 16:08:07,321 - root - INFO - Symbol: GOOGL
2025-06-12 16:08:07,321 - root - INFO - Data points: 250
2025-06-12 16:08:07,322 - root - INFO - Date range: 1718164800 to 1749614400
2025-06-12 16:08:07,322 - root - INFO - Latest close: $177.35
2025-06-12 16:08:07,322 - root - INFO - Volume: 31,607,800
2025-06-12 16:08:07,322 - root - INFO - Time taken: 0.15 seconds
2025-06-12 16:08:08,325 - root - INFO - 
================================================================================
2025-06-12 16:08:08,326 - root - INFO - TEST SUMMARY
2025-06-12 16:08:08,326 - root - INFO - ================================================================================
2025-06-12 16:08:08,326 - root - INFO - AAPL (1d, 1mo): PASSED
2025-06-12 16:08:08,328 - root - INFO - MSFT (1h, 5d): PASSED
2025-06-12 16:08:08,328 - root - INFO - GOOGL (1d, 1y): PASSED
2025-06-12 16:08:08,329 - root - INFO - 
Debug test completed.
2025-06-12 16:08:08,329 - root - INFO - Log file: D:\Loop\financial_agent\debug_test_1749724683.log
2025-06-12 16:08:08,330 - root - INFO - Stopping agent...
2025-06-12 16:08:08,330 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 16:08:08,330 - root - INFO - Agent stopped.
