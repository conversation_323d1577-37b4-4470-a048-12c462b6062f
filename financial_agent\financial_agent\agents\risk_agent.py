"""
Risk Management Agent for monitoring and controlling trading risks.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime, timedelta

from .base_agent import BaseAgent, AgentResponse
from .strategy_agent import TradeSignal, PositionSizing

# Configure logging
logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk assessment levels."""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

@dataclass
class RiskParameters:
    """Risk management parameters."""
    max_drawdown: float = 0.10  # 10% max portfolio drawdown
    max_position_risk: float = 0.02  # 2% max risk per position
    max_sector_exposure: float = 0.30  # 30% max exposure to any sector
    max_leverage: float = 2.0  # Maximum allowed leverage
    daily_loss_limit: float = 0.05  # 5% max daily loss
    position_concentration: float = 0.20  # 20% max in any single position
    volatility_threshold: float = 0.30  # 30% annualized volatility threshold
    min_volume: int = 100000  # Minimum average daily volume (shares)
    min_price: float = 5.0  # Minimum price threshold

@dataclass
class RiskAssessment:
    """Result of a risk assessment."""
    is_acceptable: bool
    risk_level: RiskLevel
    reasons: List[str]
    metrics: Dict[str, Any] = field(default_factory=dict)

class RiskManagementAgent(BaseAgent):
    """Agent responsible for managing and controlling trading risks."""
    
    def __init__(self, llm_wrapper=None, config: Optional[Dict[str, Any]] = None, name: str = 'risk'):
        """Initialize the RiskManagementAgent.
        
        Args:
            llm_wrapper: Optional LLM wrapper for advanced risk analysis
            config: Configuration dictionary
            name: Name of the agent (default: 'risk')
        """
        super().__init__(name=name, llm_wrapper=llm_wrapper)
        self.config = config or {}
        self.risk_params = RiskParameters(**self.config.get('risk_parameters', {}))
        self.portfolio_state = {
            'current_value': self.config.get('initial_portfolio', 100000.0),
            'peak_value': self.config.get('initial_portfolio', 100000.0),
            'positions': {},
            'daily_pnl': {},
            'sector_exposure': {},
            'last_updated': datetime.now()
        }
    
    async def start(self) -> None:
        """Start the risk management agent."""
        logger.info(f"Starting {self.name} agent")
        self._load_historical_data()
    
    async def stop(self) -> None:
        """Stop the risk management agent."""
        logger.info(f"Stopping {self.name} agent")
        self._save_risk_metrics()
    
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """Process a trade signal and assess its risk.
        
        Args:
            input_data: Dictionary containing 'signal' key with TradeSignal
            
        Returns:
            AgentResponse containing risk assessment or error
        """
        if 'signal' not in input_data or not isinstance(input_data['signal'], TradeSignal):
            return AgentResponse(
                success=False,
                error="Invalid input: 'signal' key with TradeSignal object is required"
            )
        
        try:
            signal = input_data['signal']
            
            # Perform risk assessment
            assessment = await self.assess_risk(signal)
            
            # If risk is acceptable, update portfolio state
            if assessment.is_acceptable:
                self._update_portfolio_state(signal)
            
            # Prepare response
            return AgentResponse(
                success=True,
                data={
                    'is_acceptable': assessment.is_acceptable,
                    'risk_level': assessment.risk_level.value,
                    'reasons': assessment.reasons,
                    'metrics': assessment.metrics
                }
            )
            
        except Exception as e:
            error_msg = f"Error in risk assessment: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return AgentResponse(success=False, error=error_msg)
    
    async def assess_risk(self, signal: TradeSignal) -> RiskAssessment:
        """Assess the risk of a trade signal."""
        reasons = []
        metrics = {}
        
        # 1. Check position size risk
        position_risk_ok, position_metrics = self._check_position_risk(signal)
        metrics.update(position_metrics)
        if not position_risk_ok:
            reasons.append(f"Position size exceeds risk limits: {position_metrics}")
        
        # 2. Check portfolio drawdown
        drawdown_ok, drawdown_metrics = self._check_drawdown()
        metrics.update(drawdown_metrics)
        if not drawdown_ok:
            reasons.append(f"Portfolio drawdown exceeds limit: {drawdown_metrics}")
        
        # 3. Check sector exposure
        exposure_ok, exposure_metrics = self._check_sector_exposure(signal)
        metrics.update(exposure_metrics)
        if not exposure_ok:
            reasons.append(f"Sector exposure exceeds limit: {exposure_metrics}")
        
        # 4. Check daily loss limit
        daily_loss_ok, daily_metrics = self._check_daily_loss()
        metrics.update(daily_metrics)
        if not daily_loss_ok:
            reasons.append(f"Daily loss limit exceeded: {daily_metrics}")
        
        # 5. Check liquidity and price
        liquidity_ok, liquidity_metrics = await self._check_liquidity(signal)
        metrics.update(liquidity_metrics)
        if not liquidity_ok:
            reasons.append(f"Liquidity/price check failed: {liquidity_metrics}")
        
        # Determine overall risk level and acceptance
        is_acceptable = all([
            position_risk_ok,
            drawdown_ok,
            exposure_ok,
            daily_loss_ok,
            liquidity_ok
        ])
        
        risk_level = self._determine_risk_level(reasons, metrics)
        
        return RiskAssessment(
            is_acceptable=is_acceptable,
            risk_level=risk_level,
            reasons=reasons,
            metrics=metrics
        )
    
    def _check_position_risk(self, signal: TradeSignal) -> Tuple[bool, Dict[str, Any]]:
        """Check if position size is within risk limits."""
        position_value = self.portfolio_state['current_value'] * (signal.size_percent / 100.0)
        max_position_value = self.portfolio_state['current_value'] * self.risk_params.position_concentration
        
        metrics = {
            'position_value': position_value,
            'max_allowed_position': max_position_value,
            'position_pct': signal.size_percent,
            'max_position_pct': self.risk_params.position_concentration * 100
        }
        
        if position_value > max_position_value:
            return False, metrics
        return True, metrics
    
    def _check_drawdown(self) -> Tuple[bool, Dict[str, Any]]:
        """Check if portfolio drawdown is within limits."""
        current_value = self.portfolio_state['current_value']
        peak_value = self.portfolio_state['peak_value']
        
        if current_value > peak_value:
            self.portfolio_state['peak_value'] = current_value
            peak_value = current_value
        
        drawdown = (peak_value - current_value) / peak_value if peak_value > 0 else 0
        
        metrics = {
            'current_value': current_value,
            'peak_value': peak_value,
            'drawdown_pct': drawdown * 100,
            'max_drawdown_pct': self.risk_params.max_drawdown * 100
        }
        
        if drawdown > self.risk_params.max_drawdown:
            return False, metrics
        return True, metrics
    
    def _check_sector_exposure(self, signal: TradeSignal) -> Tuple[bool, Dict[str, Any]]:
        """Check if sector exposure is within limits."""
        # This would be implemented based on your sector classification
        # For now, we'll use a placeholder
        sector = "Technology"  # Would come from signal metadata or symbol mapping
        
        current_exposure = self.portfolio_state['sector_exposure'].get(sector, 0.0)
        new_exposure = current_exposure + (signal.size_percent / 100.0)
        
        metrics = {
            'sector': sector,
            'current_exposure_pct': current_exposure * 100,
            'new_exposure_pct': new_exposure * 100,
            'max_exposure_pct': self.risk_params.max_sector_exposure * 100
        }
        
        if new_exposure > self.risk_params.max_sector_exposure:
            return False, metrics
        return True, metrics
    
    def _check_daily_loss(self) -> Tuple[bool, Dict[str, Any]]:
        """Check if daily loss limit is being approached."""
        today = datetime.now().date()
        daily_pnl = self.portfolio_state['daily_pnl'].get(today.isoformat(), 0.0)
        
        metrics = {
            'date': today.isoformat(),
            'daily_pnl_pct': (daily_pnl / self.portfolio_state['current_value']) * 100,
            'daily_loss_limit_pct': self.risk_params.daily_loss_limit * 100
        }
        
        if daily_pnl < -self.portfolio_state['current_value'] * self.risk_params.daily_loss_limit:
            return False, metrics
        return True, metrics
    
    async def _check_liquidity(self, signal: TradeSignal) -> Tuple[bool, Dict[str, Any]]:
        """Check if the instrument has sufficient liquidity."""
        # In a real implementation, this would fetch market data
        # For now, we'll use placeholders
        avg_volume = 500000  # Would come from market data
        current_price = 100.0  # Would come from market data
        
        metrics = {
            'symbol': signal.symbol,
            'avg_volume': avg_volume,
            'current_price': current_price,
            'min_volume': self.risk_params.min_volume,
            'min_price': self.risk_params.min_price
        }
        
        if avg_volume < self.risk_params.min_volume:
            return False, metrics
        if current_price < self.risk_params.min_price:
            return False, metrics
        return True, metrics
    
    def _determine_risk_level(self, reasons: List[str], metrics: Dict[str, Any]) -> RiskLevel:
        """Determine the overall risk level based on the assessment."""
        if not reasons:
            return RiskLevel.LOW
        
        # Count critical metrics that failed
        critical_failures = sum(1 for reason in reasons if 'exceeds limit' in reason.lower())
        
        if critical_failures > 2:
            return RiskLevel.CRITICAL
        elif critical_failures > 0:
            return RiskLevel.HIGH
        else:
            return RiskLevel.MEDIUM
    
    def _update_portfolio_state(self, signal: TradeSignal) -> None:
        """Update the portfolio state after a trade is executed."""
        # This would be implemented to update positions, P&L, etc.
        # For now, we'll just update the last_updated timestamp
        self.portfolio_state['last_updated'] = datetime.now()
    
    def _load_historical_data(self) -> None:
        """Load historical risk metrics and portfolio state."""
        # This would load from a database or file
        pass
    
    def _save_risk_metrics(self) -> None:
        """Save current risk metrics and portfolio state."""
        # This would save to a database or file
        pass
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics."""
        return {
            'portfolio_value': self.portfolio_state['current_value'],
            'peak_value': self.portfolio_state['peak_value'],
            'drawdown': (self.portfolio_state['peak_value'] - self.portfolio_state['current_value']) / self.portfolio_state['peak_value'] * 100 if self.portfolio_state['peak_value'] > 0 else 0,
            'positions': len(self.portfolio_state['positions']),
            'last_updated': self.portfolio_state['last_updated'].isoformat()
        }
