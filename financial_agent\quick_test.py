"""
Quick test script for DataCollectionAgent
"""
import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

# Now import the agent
from agents.data_agent import DataCollectionAgent

async def main():
    print("Testing DataCollectionAgent...")
    agent = DataCollectionAgent()
    
    try:
        # Start the agent
        await agent.start()
        print("Agent started successfully")
        
        # Test basic data fetch
        print("\nFetching AAPL data...")
        ohlcv = await agent.fetch_ohlcv(
            symbol="AAPL",
            interval="1d",
            period="1mo"
        )
        
        if ohlcv:
            print(f"Success! Got {len(ohlcv.timestamp)} data points for {ohlcv.symbol}")
            print(f"Sample data: {ohlcv.close[:5]}...")
        else:
            print("Failed to fetch data")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await agent.stop()
        print("\nAgent stopped")

if __name__ == "__main__":
    asyncio.run(main())
