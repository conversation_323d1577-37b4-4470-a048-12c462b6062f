def knowledge_distillation_training(teacher_model, student_model, train_loader, epochs=5, learning_rate=0.001, alpha=0.5, temperature=2.0, device='cuda'):
    '''
    Real knowledge distillation with gradient matching

    Args:
        teacher_model: Original model (compressed or not)
        student_model: Smaller student model
        train_loader: Training data loader
        epochs: Number of training epochs
        learning_rate: Learning rate for the student model
        alpha: Weighting factor between student loss and distillation loss
        temperature: Temperature for softening the teacher's probabilities
        device: Device to run the training on ('cuda' or 'cpu')

    Returns:
        dict: {
            'trained_student': Trained student model,
            'distillation_loss': Final distillation loss,
            'accuracy_retention': Accuracy compared to teacher,
            'training_history': Loss history during training
        }
    '''
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F

    teacher_model.eval().to(device)
    student_model.train().to(device)

    optimizer = optim.Adam(student_model.parameters(), lr=learning_rate)
    criterion = nn.CrossEntropyLoss()

    training_history = []
    best_accuracy_retention = 0.0
    best_student_model = None

    def distillation_loss_gradient_matching(student_output, teacher_output, student_model, teacher_model, inputs):
        """
        Calculates the distillation loss using gradient matching.
        """
        student_output = F.log_softmax(student_output, dim=1)
        teacher_output = F.softmax(teacher_output / temperature, dim=1)

        # Calculate gradients of student and teacher outputs w.r.t. inputs
        student_grads = torch.autograd.grad(student_output.sum(), inputs, create_graph=True)[0]
        teacher_grads = torch.autograd.grad(teacher_output.sum(), inputs, create_graph=True)[0]

        # Flatten the gradients
        student_grads_flat = student_grads.view(student_grads.size(0), -1)
        teacher_grads_flat = teacher_grads.view(teacher_grads.size(0), -1)

        # Calculate the mean squared error between the flattened gradients
        loss = F.mse_loss(student_grads_flat, teacher_grads_flat)
        return loss


    def evaluate_accuracy_retention(student_model, teacher_model, data_loader, device):
        """
        Evaluates the accuracy of the student model compared to the teacher model.
        """
        student_correct = 0
        teacher_correct = 0
        total = 0

        student_model.eval()
        teacher_model.eval()

        with torch.no_grad():
            for inputs, labels in data_loader:
                inputs, labels = inputs.to(device), labels.to(device)

                student_outputs = student_model(inputs)
                teacher_outputs = teacher_model(inputs)

                _, student_predicted = torch.max(student_outputs.data, 1)
                _, teacher_predicted = torch.max(teacher_outputs.data, 1)

                total += labels.size(0)
                student_correct += (student_predicted == labels).sum().item()
                teacher_correct += (teacher_predicted == labels).sum().item()

        student_accuracy = student_correct / total
        teacher_accuracy = teacher_correct / total

        # Calculate accuracy retention as the ratio of student accuracy to teacher accuracy
        accuracy_retention = student_accuracy / teacher_accuracy if teacher_accuracy > 0 else 0.0

        student_model.train()
        teacher_model.eval()

        return accuracy_retention


    for epoch in range(epochs):
        epoch_loss = 0.0
        for i, (inputs, labels) in enumerate(train_loader):
            inputs, labels = inputs.to(device), labels.to(device)
            inputs.requires_grad_(True) # Crucial to enable gradient calculation w.r.t. inputs
            optimizer.zero_grad()

            # Forward pass
            student_outputs = student_model(inputs)
            teacher_outputs = teacher_model(inputs)

            # Calculate losses
            student_loss = criterion(student_outputs, labels)
            distillation_loss = distillation_loss_gradient_matching(student_outputs, teacher_outputs, student_model, teacher_model, inputs)

            # Combine losses
            loss = (1 - alpha) * student_loss + alpha * distillation_loss

            # Backward pass and optimization
            loss.backward()
            optimizer.step()

            epoch_loss += loss.item()

        avg_epoch_loss = epoch_loss / len(train_loader)
        training_history.append(avg_epoch_loss)
        print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_epoch_loss:.4f}")

        # Evaluate accuracy retention
        accuracy_retention = evaluate_accuracy_retention(student_model, teacher_model, train_loader, device)
        print(f"Epoch {epoch+1}/{epochs}, Accuracy Retention: {accuracy_retention:.4f}")

        # Save the best model based on accuracy retention
        if accuracy_retention > best_accuracy_retention:
            best_accuracy_retention = accuracy_retention
            best_student_model = student_model.state_dict()



    # Load the best model
    if best_student_model is not None:
        student_model.load_state_dict(best_student_model)
    student_model.eval()


    # Calculate final distillation loss and accuracy retention
    final_distillation_loss = 0.0
    with torch.no_grad():
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            inputs.requires_grad_(True)
            student_outputs = student_model(inputs)
            teacher_outputs = teacher_model(inputs)
            final_distillation_loss += distillation_loss_gradient_matching(student_outputs, teacher_outputs, student_model, teacher_model, inputs).item()
    final_distillation_loss /= len(train_loader)

    accuracy_retention = evaluate_accuracy_retention(student_model, teacher_model, train_loader, device)


    result = {
        'trained_student': student_model,
        'distillation_loss': final_distillation_loss,
        'accuracy_retention': accuracy_retention,
        'training_history': training_history
    }

    return result