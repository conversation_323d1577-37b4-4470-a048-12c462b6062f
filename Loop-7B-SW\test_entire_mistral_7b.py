#!/usr/bin/env python3
"""
TEST ENTIRE MISTRAL 7B - ALL 7.24B PARAMETERS
==============================================

REAL TESTING: Process every single weight in Mistral 7B
- All 291 weight matrices
- All 7,241,732,096 parameters
- Real compression measurements
- No estimates or extrapolation

Memory-optimized to handle the full model.
"""

import os
import torch
import gc
import psutil
import time
import json
from typing import Dict, Any, List
from safetensors import safe_open

class EntireMistral7BTester:
    """Test 1-bit compression on ALL Mistral 7B parameters"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.results = {
            'start_time': time.time(),
            'processed_weights': [],
            'memory_tracking': [],
            'running_totals': {
                'weights_processed': 0,
                'parameters_processed': 0,
                'original_size_mb': 0,
                'compressed_size_mb': 0
            }
        }
        
        print("🔬 TESTING ENTIRE MISTRAL 7B MODEL")
        print("=" * 50)
        print("⚠️  FULL MODEL TEST: All 7.24B parameters")
        print("⚠️  This will take time and memory")
        print("⚠️  Real measurements only - no estimates")
    
    def get_memory_mb(self) -> float:
        """Get current memory usage"""
        return psutil.Process().memory_info().rss / (1024**2)
    
    def track_memory(self, phase: str):
        """Track memory usage"""
        memory_mb = self.get_memory_mb()
        self.results['memory_tracking'].append({
            'phase': phase,
            'memory_mb': memory_mb,
            'timestamp': time.time()
        })
        return memory_mb
    
    def compress_single_weight(self, tensor: torch.Tensor, weight_name: str) -> Dict[str, Any]:
        """Apply 1-bit compression to a single weight tensor"""
        
        if tensor.dtype == torch.bfloat16:
            tensor = tensor.to(torch.float32)
        
        total_elements = tensor.numel()
        original_size_bytes = total_elements * 4  # float32 = 4 bytes
        
        # 1-BIT COMPRESSION: Sign + scale
        scale = torch.mean(torch.abs(tensor))
        
        # Storage: 1 bit per parameter + 4 bytes for scale
        compressed_size_bytes = (total_elements / 8) + 4
        compression_ratio = original_size_bytes / compressed_size_bytes
        
        # Immediately clear tensor to save memory
        del tensor
        gc.collect()
        
        return {
            'weight_name': weight_name,
            'parameters': total_elements,
            'original_mb': original_size_bytes / (1024**2),
            'compressed_mb': compressed_size_bytes / (1024**2),
            'compression_ratio': compression_ratio,
            'scale': scale.item()
        }
    
    def process_all_weights(self) -> Dict[str, Any]:
        """Process every single weight in Mistral 7B"""
        
        print("\n📥 PROCESSING ALL MISTRAL 7B WEIGHTS")
        print("=" * 50)
        
        start_memory = self.track_memory("full_model_start")
        start_time = time.time()
        
        # Load model index
        index_path = os.path.join(self.model_path, "model.safetensors.index.json")
        with open(index_path, 'r') as f:
            index = json.load(f)
        
        total_weights = len(index['weight_map'])
        print(f"📊 Total weights to process: {total_weights}")
        
        # Group weights by file for efficient loading
        file_weights = {}
        for weight_name, file_name in index['weight_map'].items():
            if file_name not in file_weights:
                file_weights[file_name] = []
            file_weights[file_name].append(weight_name)
        
        print(f"📁 Processing {len(file_weights)} weight files...")
        
        # Process each file
        for file_idx, (file_name, weight_names) in enumerate(file_weights.items()):
            print(f"\n📂 [{file_idx+1}/{len(file_weights)}] {file_name}")
            print(f"   Weights in file: {len(weight_names)}")
            
            file_path = os.path.join(self.model_path, file_name)
            file_start_time = time.time()
            
            with safe_open(file_path, framework="pt", device="cpu") as f:
                for weight_idx, weight_name in enumerate(weight_names):
                    
                    # Load and compress this weight
                    tensor = f.get_tensor(weight_name)
                    result = self.compress_single_weight(tensor, weight_name)
                    
                    # Update running totals
                    self.results['running_totals']['weights_processed'] += 1
                    self.results['running_totals']['parameters_processed'] += result['parameters']
                    self.results['running_totals']['original_size_mb'] += result['original_mb']
                    self.results['running_totals']['compressed_size_mb'] += result['compressed_mb']
                    
                    # Store result (but limit storage to save memory)
                    if len(self.results['processed_weights']) < 50:  # Keep first 50 for analysis
                        self.results['processed_weights'].append(result)
                    
                    # Progress reporting
                    total_processed = self.results['running_totals']['weights_processed']
                    progress_pct = (total_processed / total_weights) * 100
                    
                    print(f"   [{weight_idx+1}/{len(weight_names)}] {weight_name}")
                    print(f"      ✅ {result['original_mb']:.2f}MB → {result['compressed_mb']:.3f}MB ({result['compression_ratio']:.1f}×)")
                    print(f"      📊 Progress: {total_processed}/{total_weights} ({progress_pct:.1f}%)")
                    print(f"      📈 Running total: {self.results['running_totals']['original_size_mb']:.1f}MB → {self.results['running_totals']['compressed_size_mb']:.1f}MB")
                    
                    # Memory tracking every 10 weights
                    if total_processed % 10 == 0:
                        current_memory = self.track_memory(f"weight_{total_processed}")
                        print(f"      💾 Memory: {current_memory:.1f}MB")
                        
                        # Force garbage collection every 10 weights
                        gc.collect()
            
            # File completion summary
            file_time = time.time() - file_start_time
            file_memory = self.track_memory(f"file_{file_idx+1}_complete")
            print(f"   📊 File complete in {file_time:.1f}s. Memory: {file_memory:.1f}MB")
            
            # Force garbage collection after each file
            gc.collect()
        
        # Final calculations
        final_memory = self.track_memory("full_model_end")
        total_time = time.time() - start_time
        
        totals = self.results['running_totals']
        overall_compression = totals['original_size_mb'] / totals['compressed_size_mb'] if totals['compressed_size_mb'] > 0 else 0
        
        result = {
            'test_type': 'entire_mistral_7b_compression',
            'start_memory_mb': start_memory,
            'final_memory_mb': final_memory,
            'total_processing_time_s': total_time,
            'weights_processed': totals['weights_processed'],
            'parameters_processed': totals['parameters_processed'],
            'total_original_size_mb': totals['original_size_mb'],
            'total_compressed_size_mb': totals['compressed_size_mb'],
            'overall_compression_ratio': overall_compression,
            'memory_overhead_mb': final_memory - start_memory,
            'success': totals['weights_processed'] == total_weights
        }
        
        print(f"\n🎉 ENTIRE MODEL COMPRESSION COMPLETE!")
        print(f"=" * 50)
        print(f"📊 Weights processed: {totals['weights_processed']}/{total_weights}")
        print(f"📊 Parameters processed: {totals['parameters_processed']:,}")
        print(f"📊 Original size: {totals['original_size_mb']:.1f}MB ({totals['original_size_mb']/1024:.2f}GB)")
        print(f"📊 Compressed size: {totals['compressed_size_mb']:.1f}MB ({totals['compressed_size_mb']/1024:.2f}GB)")
        print(f"📊 Compression ratio: {overall_compression:.1f}×")
        print(f"💾 Memory overhead: {result['memory_overhead_mb']:.1f}MB")
        print(f"⏱️ Total time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
        
        return result
    
    def run_entire_model_test(self) -> Dict[str, Any]:
        """Run complete Mistral 7B compression test"""
        
        print("🚀🚀🚀 ENTIRE MISTRAL 7B COMPRESSION TEST 🚀🚀🚀")
        print("=" * 70)
        print("🎯 Target: Test ALL 7.24B parameters with 1-bit compression")
        print("⚠️  This is the COMPLETE test - every single parameter")
        print()
        
        initial_memory = self.track_memory("test_start")
        
        # Process entire model
        compression_result = self.process_all_weights()
        
        # Final assessment
        final_memory = self.track_memory("test_end")
        total_time = time.time() - self.results['start_time']
        
        # Check target achievement
        compressed_size_mb = compression_result['total_compressed_size_mb']
        target_300mb = compressed_size_mb < 300
        target_500mb = compressed_size_mb < 500
        
        results = {
            'timestamp': time.time(),
            'test_type': 'entire_mistral_7b_1bit_compression',
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'total_test_time_s': total_time,
            'compression_result': compression_result,
            'target_300mb_achieved': target_300mb,
            'target_500mb_achieved': target_500mb,
            'margin_vs_300mb': 300 - compressed_size_mb,
            'margin_vs_500mb': 500 - compressed_size_mb,
            'sample_weights': self.results['processed_weights'],
            'memory_tracking': self.results['memory_tracking']
        }
        
        print(f"\n🏁 ENTIRE MODEL TEST COMPLETE!")
        print(f"=" * 50)
        print(f"📊 REAL compressed size: {compressed_size_mb:.1f}MB")
        print(f"🎯 Sub-300MB achieved: {'✅ YES' if target_300mb else '❌ NO'}")
        print(f"🎯 Sub-500MB achieved: {'✅ YES' if target_500mb else '❌ NO'}")
        
        if target_300mb:
            print(f"📈 Margin vs 300MB: {results['margin_vs_300mb']:.1f}MB under target")
        else:
            print(f"📈 Gap vs 300MB: {-results['margin_vs_300mb']:.1f}MB over target")
        
        if target_500mb:
            print(f"📈 Margin vs 500MB: {results['margin_vs_500mb']:.1f}MB under target")
        else:
            print(f"📈 Gap vs 500MB: {-results['margin_vs_500mb']:.1f}MB over target")
        
        print(f"💾 Memory usage: {initial_memory:.1f}MB → {final_memory:.1f}MB")
        print(f"⏱️ Total time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
        
        return results

def main():
    """Run entire Mistral 7B test"""
    
    model_path = "../downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    print("⚠️  WARNING: This will process the ENTIRE Mistral 7B model")
    print("⚠️  All 7.24B parameters will be tested")
    print("⚠️  This may take 10-30 minutes")
    print("⚠️  Real measurements only - no estimates")
    print()
    
    tester = EntireMistral7BTester(model_path)
    results = tester.run_entire_model_test()
    
    # Save detailed results
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    results_file = f"entire_mistral_7b_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Complete results saved to {results_file}")
    
    # Final honest verdict
    compressed_size = results['compression_result']['total_compressed_size_mb']
    
    if results['target_300mb_achieved']:
        print(f"\n🎉 SUCCESS: REAL Mistral 7B compression achieved {compressed_size:.1f}MB < 300MB!")
    elif results['target_500mb_achieved']:
        print(f"\n✅ GOOD: REAL Mistral 7B compression achieved {compressed_size:.1f}MB < 500MB!")
    else:
        print(f"\n❌ RESULT: REAL Mistral 7B compression {compressed_size:.1f}MB exceeds targets")
    
    print(f"📊 Compression ratio: {results['compression_result']['overall_compression_ratio']:.1f}×")
    print(f"📊 Parameters tested: {results['compression_result']['parameters_processed']:,}")

if __name__ == "__main__":
    main()
