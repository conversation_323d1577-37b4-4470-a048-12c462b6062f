"""
Tests for the DataCollectionAgent using yfinance
"""
import asyncio
import pytest
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any
import pandas as pd
import pytz
import sys
from pathlib import Path

# Add parent directory to path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from financial_agent.agents.data_agent import DataCollectionAgent, MarketDataRequest, OHLCVData

# Import the mock MistralWrapper directly from the tests directory
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from mock_mistral_wrapper import MistralWrapper

@pytest.fixture
def mock_llm_wrapper():
    """Create a mock LLM wrapper for testing"""
    from unittest.mock import MagicMock
    return MagicMock()  # We don't need real LLM for data agent tests

@pytest.fixture
def data_agent(mock_llm_wrapper):
    """Create a DataCollectionAgent instance for testing"""
    agent = DataCollectionAgent(llm_wrapper=mock_llm_wrapper)
    return agent

@pytest.mark.asyncio
async def test_data_agent_initialization(data_agent):
    """Test that the data agent initializes correctly"""
    assert data_agent is not None
    assert data_agent.name == 'data'
    await data_agent.start()
    assert data_agent.is_running
    await data_agent.stop()
    assert not data_agent.is_running

@pytest.mark.asyncio
async def test_fetch_ohlcv_basic(data_agent):
    """Test basic OHLCV data fetching"""
    await data_agent.start()
    
    # Test with a valid symbol and interval
    ohlcv = await data_agent.fetch_ohlcv(
        symbol="AAPL",
        interval="1d",
        period="1mo"
    )
    
    assert ohlcv is not None
    assert isinstance(ohlcv, OHLCVData)
    assert ohlcv.symbol == "AAPL"
    assert ohlcv.interval == "1d"
    assert len(ohlcv.timestamp) > 0
    assert len(ohlcv.open) == len(ohlcv.timestamp)
    assert len(ohlcv.high) == len(ohlcv.timestamp)
    assert len(ohlcv.low) == len(ohlcv.timestamp)
    assert len(ohlcv.close) == len(ohlcv.timestamp)
    assert len(ohlcv.volume) == len(ohlcv.timestamp)
    
    # Verify timestamps are in order
    timestamps = ohlcv.timestamp
    assert all(timestamps[i] <= timestamps[i+1] for i in range(len(timestamps)-1))
    
    await data_agent.stop()

@pytest.mark.asyncio
async def test_fetch_ohlcv_date_range(data_agent):
    """Test fetching data with specific date range"""
    await data_agent.start()
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    ohlcv = await data_agent.fetch_ohlcv(
        symbol="MSFT",
        interval="1h",
        start=start_date.strftime("%Y-%m-%d"),
        end=end_date.strftime("%Y-%m-%d")
    )
    
    assert ohlcv is not None
    assert len(ohlcv.timestamp) > 0
    
    # Verify dates are within range
    start_ts = start_date.timestamp()
    end_ts = end_date.timestamp()
    for ts in ohlcv.timestamp:
        assert start_ts <= ts <= end_ts
    
    await data_agent.stop()

@pytest.mark.asyncio
async def test_process_method(data_agent):
    """Test the process method with a valid request"""
    await data_agent.start()
    
    request = {
        'request': {
            'symbols': ['AAPL', 'MSFT'],
            'interval': '1d',
            'period': '1wk'
        }
    }
    
    response = await data_agent.process(request)
    
    assert response.success
    assert 'results' in response.data
    assert 'AAPL' in response.data['results']
    assert 'MSFT' in response.data['results']
    assert len(response.data['results']['AAPL']) > 0
    assert len(response.data['results']['MSFT']) > 0
    
    await data_agent.stop()

@pytest.mark.asyncio
async def test_invalid_interval(data_agent):
    """Test that invalid intervals raise appropriate errors"""
    await data_agent.start()
    
    with pytest.raises(ValueError):
        await data_agent.fetch_ohlcv("AAPL", interval="invalid", period="1d")
    
    await data_agent.stop()

@pytest.mark.asyncio
async def test_market_hours(data_agent):
    """Test market hours functionality"""
    await data_agent.start()
    
    # Test with a specific date
    test_date = datetime(2023, 1, 3)  # A Tuesday
    market_open, market_close = data_agent.get_market_hours(test_date)
    
    # Convert to UTC for comparison
    ny_tz = pytz.timezone('America/New_York')
    expected_open = ny_tz.localize(datetime(2023, 1, 3, 9, 30)).timestamp()
    expected_close = ny_tz.localize(datetime(2023, 1, 3, 16, 0)).timestamp()
    
    assert abs(market_open - expected_open) < 60  # Within 1 minute
    assert abs(market_close - expected_close) < 60  # Within 1 minute
    
    # Test is_market_open (can't easily test the actual result as it depends on current time)
    is_open = data_agent.is_market_open()
    assert isinstance(is_open, bool)
    
    await data_agent.stop()

@pytest.mark.asyncio
async def test_cache_functionality(data_agent):
    """Test that the cache is working correctly"""
    await data_agent.start()
    
    # First fetch (should miss cache)
    start_time = datetime.now()
    ohlcv1 = await data_agent.fetch_ohlcv("AAPL", "1d", "1wk")
    first_fetch_time = datetime.now() - start_time
    
    # Test is_market_open on weekend
    weekend = datetime(2023, 1, 1)  # Sunday
    assert not data_agent.is_market_open(weekend)

# Run the tests
if __name__ == "__main__":
    import pytest
    import sys
    sys.exit(pytest.main([__file__]))
