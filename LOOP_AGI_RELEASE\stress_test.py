#!/usr/bin/env python3
"""
LOOP AGI - Stress Test System
100-cycle autonomous operation stress test
"""

import time
import json
import datetime
from loop import LoopAG<PERSON>

def run_stress_test(cycles: int = 100, interval: float = 0.5):
    """Run comprehensive stress test"""
    
    print(f"🚀 Starting {cycles}-cycle stress test...")
    print(f"⏱️  Cycle interval: {interval} seconds")
    print("=" * 50)
    
    # Initialize system
    agi = LoopAGI()
    
    # Configure for stress test
    agi.config['max_cycles'] = cycles
    agi.config['cycle_interval'] = interval
    
    # Record start metrics
    start_time = time.time()
    start_memory = agi.memory.copy()
    
    # Run stress test
    try:
        agi.run_autonomous_loop()
    except KeyboardInterrupt:
        print("\n⚠️  Stress test interrupted by user")
    except Exception as e:
        print(f"\n❌ Stress test failed with error: {str(e)}")
    
    # Record end metrics
    end_time = time.time()
    duration = end_time - start_time
    
    # Calculate results
    cycles_completed = agi.cycle_count
    successful_mutations = agi.memory['successful_mutations']
    failed_mutations = agi.memory['failed_mutations']
    success_rate = (successful_mutations / max(1, cycles_completed)) * 100
    
    # Performance analysis
    cognitive_metrics = agi.meta_cognitive.get_cognitive_metrics()
    performance_report = agi.performance_analyzer.generate_performance_report()
    
    # Generate stress test report
    stress_report = {
        'test_info': {
            'timestamp': datetime.datetime.now().isoformat(),
            'target_cycles': cycles,
            'actual_cycles': cycles_completed,
            'duration_seconds': duration,
            'avg_cycle_time': duration / max(1, cycles_completed),
            'completion_rate': (cycles_completed / cycles) * 100
        },
        'performance_metrics': {
            'successful_mutations': successful_mutations,
            'failed_mutations': failed_mutations,
            'success_rate_percent': success_rate,
            'intelligence_multiplier': performance_report.get('intelligence_multiplier', 1.0),
            'overall_grade': performance_report.get('overall_assessment', {}).get('grade', 'N/A')
        },
        'cognitive_metrics': cognitive_metrics,
        'system_health': {
            'memory_usage_mb': 'N/A',  # Would need psutil for actual measurement
            'cpu_usage_percent': 'N/A',
            'errors_encountered': failed_mutations,
            'safety_violations': 0  # Tracked separately
        },
        'stability_assessment': {
            'completed_successfully': cycles_completed >= cycles * 0.95,  # 95% completion threshold
            'performance_maintained': success_rate >= 90,  # 90% success rate threshold
            'no_critical_errors': failed_mutations < cycles * 0.1,  # Less than 10% failures
            'cognitive_health': cognitive_metrics['metrics']['average_quality'] > 0.3
        }
    }
    
    # Print results
    print("\n" + "=" * 50)
    print("📊 STRESS TEST RESULTS")
    print("=" * 50)
    print(f"✅ Cycles Completed: {cycles_completed}/{cycles} ({stress_report['test_info']['completion_rate']:.1f}%)")
    print(f"⏱️  Total Duration: {duration:.2f} seconds")
    print(f"🔄 Average Cycle Time: {stress_report['test_info']['avg_cycle_time']:.3f} seconds")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    print(f"🧠 Intelligence Multiplier: {stress_report['performance_metrics']['intelligence_multiplier']:.2f}x")
    print(f"🎯 Overall Grade: {stress_report['performance_metrics']['overall_grade']}")
    print(f"💭 Cognitive Quality: {cognitive_metrics['metrics']['average_quality']:.3f}")
    print(f"🔒 Safety Violations: {stress_report['system_health']['safety_violations']}")
    
    # Stability assessment
    print("\n🛡️  STABILITY ASSESSMENT:")
    stability = stress_report['stability_assessment']
    for criterion, passed in stability.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {criterion.replace('_', ' ').title()}: {status}")
    
    overall_stable = all(stability.values())
    print(f"\n🏆 OVERALL STABILITY: {'✅ STABLE' if overall_stable else '❌ UNSTABLE'}")
    
    # Save detailed report
    report_path = f"benchmarks/stress_test_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_path, 'w') as f:
        json.dump(stress_report, f, indent=2, default=str)
    
    print(f"\n📄 Detailed report saved to: {report_path}")
    
    return stress_report

if __name__ == "__main__":
    # Run 10-cycle test first for validation
    print("🧪 Running validation test (10 cycles)...")
    validation_report = run_stress_test(cycles=10, interval=0.1)
    
    if validation_report['stability_assessment']['completed_successfully']:
        print("\n✅ Validation passed! Ready for full stress test.")
        
        # Ask user if they want to continue with full test
        response = input("\n🚀 Run full 100-cycle stress test? (y/n): ").lower().strip()
        if response == 'y':
            print("\n🚀 Starting full 100-cycle stress test...")
            full_report = run_stress_test(cycles=100, interval=0.5)
        else:
            print("🛑 Full stress test skipped by user.")
    else:
        print("\n❌ Validation failed! System not ready for full stress test.")
        print("🔧 Please review system stability before proceeding.")
