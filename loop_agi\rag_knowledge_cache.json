{"786f3d2eb74e3cc5a5eafdf12a9ad1c2": {"query": "derivative rules", "domain": "calculus", "offline_results": [{"path": "calculus.derivative_rules", "key": "derivative_rules", "value": {"power_rule": "d/dx(x^n) = n*x^(n-1)", "product_rule": "d/dx(uv) = u'v + uv'", "chain_rule": "d/dx(f(g(x))) = f'(g(x)) * g'(x)", "quotient_rule": "d/dx(u/v) = (u'v - uv')/v^2"}, "relevance": 0.3, "type": "key_match"}, {"path": "calculus.integration_rules", "key": "integration_rules", "value": {"power_rule": "∫x^n dx = x^(n+1)/(n+1) + C", "substitution": "∫f(g(x))g'(x) dx = ∫f(u) du where u = g(x)", "by_parts": "∫u dv = uv - ∫v du"}, "relevance": 0.15, "type": "key_match"}], "online_results": [], "timestamp": 1749648189.7671297, "success": true}, "6b8e1d4633d203d011ac58720303add2": {"query": "limit rules", "domain": "calculus", "offline_results": [{"path": "calculus.derivative_rules", "key": "derivative_rules", "value": {"power_rule": "d/dx(x^n) = n*x^(n-1)", "product_rule": "d/dx(uv) = u'v + uv'", "chain_rule": "d/dx(f(g(x))) = f'(g(x)) * g'(x)", "quotient_rule": "d/dx(u/v) = (u'v - uv')/v^2"}, "relevance": 0.15, "type": "key_match"}, {"path": "calculus.integration_rules", "key": "integration_rules", "value": {"power_rule": "∫x^n dx = x^(n+1)/(n+1) + C", "substitution": "∫f(g(x))g'(x) dx = ∫f(u) du where u = g(x)", "by_parts": "∫u dv = uv - ∫v du"}, "relevance": 0.15, "type": "key_match"}, {"path": "calculus.limits", "key": "limits", "value": {"lhopital": "If lim f(x)/g(x) = 0/0 or ∞/∞, then lim f(x)/g(x) = lim f'(x)/g'(x)", "squeeze_theorem": "If g(x) ≤ f(x) ≤ h(x) and lim g(x) = lim h(x) = L, then lim f(x) = L"}, "relevance": 0.15, "type": "key_match"}], "online_results": [], "timestamp": 1749648189.7671297, "success": true}, "ae4ec1480a4336f0a64f9fdfc0fdd6d3": {"query": "mechanics laws", "domain": "physics", "offline_results": [{"path": "physics.mechanics", "key": "mechanics", "value": {"newton_laws": ["F = ma (Second Law)", "For every action, there is an equal and opposite reaction (Third Law)", "An object at rest stays at rest unless acted upon by a force (First Law)"], "kinematics": {"velocity": "v = dx/dt", "acceleration": "a = dv/dt = d²x/dt²", "equations": ["v = v₀ + at", "x = x₀ + v₀t + ½at²", "v² = v₀² + 2a(x-x₀)"]}}, "relevance": 0.65, "type": "key_match"}, {"path": "physics.mechanics.newton_laws", "key": "newton_laws", "value": ["F = ma (Second Law)", "For every action, there is an equal and opposite reaction (Third Law)", "An object at rest stays at rest unless acted upon by a force (First Law)"], "relevance": 0.15, "type": "key_match"}], "online_results": [], "timestamp": 1749648189.7702942, "success": true}, "29d0556a983c13440e98d5cb65ffe640": {"query": "If all birds can fly, and penguins are birds, what can we conclude about penguins?", "domain": "general", "offline_results": [{"path": "logic.formal_logic.modus_ponens", "key": "modus_ponens", "value": "If P → Q and P, then Q", "relevance": 0.14812030075187968, "type": "content_match"}, {"path": "logic.formal_logic.disjunctive_syllogism", "key": "disjunctive_syllogism", "value": "If P ∨ Q and ¬P, then Q", "relevance": 0.14812030075187968, "type": "content_match"}, {"path": "logic.formal_logic.modus_tollens", "key": "modus_tollens", "value": "If P → Q and ¬Q, then ¬P", "relevance": 0.14285714285714285, "type": "content_match"}, {"path": "logic.formal_logic.hypothetical_syllogism", "key": "hypothetical_syllogism", "value": "If P → Q and Q → R, then P → R", "relevance": 0.14285714285714285, "type": "content_match"}, {"path": "calculus.limits.squeeze_theorem", "key": "squeeze_theorem", "value": "If g(x) ≤ f(x) ≤ h(x) and lim g(x) = lim h(x) = L, then lim f(x) = L", "relevance": 0.12981366459627328, "type": "content_match"}], "online_results": [], "timestamp": 1749648189.7742186, "success": true}, "053961800446c523d04695c7c9476221": {"query": "integration rules", "domain": "calculus", "offline_results": [{"path": "calculus.integration_rules", "key": "integration_rules", "value": {"power_rule": "∫x^n dx = x^(n+1)/(n+1) + C", "substitution": "∫f(g(x))g'(x) dx = ∫f(u) du where u = g(x)", "by_parts": "∫u dv = uv - ∫v du"}, "relevance": 0.3, "type": "key_match"}, {"path": "calculus.derivative_rules", "key": "derivative_rules", "value": {"power_rule": "d/dx(x^n) = n*x^(n-1)", "product_rule": "d/dx(uv) = u'v + uv'", "chain_rule": "d/dx(f(g(x))) = f'(g(x)) * g'(x)", "quotient_rule": "d/dx(u/v) = (u'v - uv')/v^2"}, "relevance": 0.15, "type": "key_match"}], "online_results": [], "timestamp": 1749648189.7772768, "success": true}}