2025-06-12 15:57:39,429 - asyncio - DEBUG - Using proactor: IocpProactor
2025-06-12 15:57:39,436 - __main__ - INFO - 
================================================================================
2025-06-12 15:57:39,436 - __main__ - INFO - STARTING COMPARISON
2025-06-12 15:57:39,436 - __main__ - INFO - ================================================================================
2025-06-12 15:57:39,436 - __main__ - INFO - 
================================================================================
2025-06-12 15:57:39,436 - __main__ - INFO - DIRECT YFINANCE FETCH
2025-06-12 15:57:39,438 - __main__ - INFO - ================================================================================
2025-06-12 15:57:39,440 - yfinance - DEBUG - Entering history()
2025-06-12 15:57:39,442 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_kv" ("key" VARCHAR(255) NOT NULL PRIMARY KEY, "value" VARCHAR(255)) WITHOUT ROWID', [])
2025-06-12 15:57:39,444 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-06-12 15:57:39,508 - yfinance - DEBUG -  Entering history()
2025-06-12 15:57:39,509 - yfinance - DEBUG - AAPL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:57:39,509 - yfinance - DEBUG -   Entering get()
2025-06-12 15:57:39,509 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 15:57:39,509 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-06-12 15:57:39,509 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:57:39,509 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 15:57:39,509 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 15:57:39,509 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 15:57:39,509 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 15:57:39,509 - yfinance - DEBUG -        Entering _load_cookie_curlCffi()
2025-06-12 15:57:39,513 - peewee - DEBUG - ('CREATE TABLE IF NOT EXISTS "_cookieschema" ("strategy" VARCHAR(255) NOT NULL PRIMARY KEY, "fetch_date" DATETIME NOT NULL, "cookie_bytes" BLOB NOT NULL) WITHOUT ROWID', [])
2025-06-12 15:57:39,513 - peewee - DEBUG - ('SELECT "t1"."strategy", "t1"."fetch_date", "t1"."cookie_bytes" FROM "_cookieschema" AS "t1" WHERE ("t1"."strategy" = ?) LIMIT ? OFFSET ?', ['curlCffi', 1, 0])
2025-06-12 15:57:39,516 - yfinance - DEBUG -        Exiting _load_cookie_curlCffi()
2025-06-12 15:57:39,516 - yfinance - DEBUG - reusing persistent cookie
2025-06-12 15:57:39,516 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 15:57:39,516 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 15:57:39,516 - yfinance - DEBUG -        Entering _get_cookie_basic()
2025-06-12 15:57:39,516 - yfinance - DEBUG - reusing cookie
2025-06-12 15:57:39,516 - yfinance - DEBUG -        Exiting _get_cookie_basic()
2025-06-12 15:57:39,737 - yfinance - DEBUG - crumb = 'iDku.3/e0AN'
2025-06-12 15:57:39,738 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 15:57:39,738 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 15:57:39,738 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 15:57:40,012 - yfinance - DEBUG - response code=200
2025-06-12 15:57:40,012 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 15:57:40,012 - yfinance - DEBUG -   Exiting get()
2025-06-12 15:57:40,016 - yfinance - DEBUG - AAPL: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 15:57:40,021 - yfinance - DEBUG - AAPL: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 15:57:40,028 - yfinance - DEBUG - AAPL: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:57:40,030 - yfinance - DEBUG - AAPL: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:57:40,030 - yfinance - DEBUG -  Exiting history()
2025-06-12 15:57:40,030 - yfinance - DEBUG - Exiting history()
2025-06-12 15:57:40,030 - __main__ - INFO - Direct fetch completed in 0.59s
2025-06-12 15:57:40,030 - __main__ - INFO - Fetched 22 rows
2025-06-12 15:57:40,030 - __main__ - INFO - Columns: Open, High, Low, Close, Volume
2025-06-12 15:57:40,039 - __main__ - INFO - First row: Open      2.109700e+02
High      2.112700e+02
Low       2.067500e+02
Close     2.107900e+02
Volume    6.377580e+07
Name: 2025-05-12 00:00:00-04:00, dtype: float64
2025-06-12 15:57:40,040 - __main__ - INFO - Last row: Open      2.035000e+02
High      2.045000e+02
Low       1.984100e+02
Close     1.987800e+02
Volume    6.082020e+07
Name: 2025-06-11 00:00:00-04:00, dtype: float64
2025-06-12 15:57:42,054 - __main__ - INFO - 
================================================================================
2025-06-12 15:57:42,054 - __main__ - INFO - AGENT FETCH
2025-06-12 15:57:42,054 - __main__ - INFO - ================================================================================
2025-06-12 15:57:42,054 - agent.data - INFO - Initialized data agent
2025-06-12 15:57:42,054 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 15:57:42,054 - __main__ - INFO - Starting agent...
2025-06-12 15:57:42,054 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 15:57:42,058 - __main__ - INFO - Fetching data for AAPL...
2025-06-12 15:57:42,058 - financial_agent.agents.data_agent - INFO - Fetching data for AAPL (attempt 1/3)...
2025-06-12 15:57:42,058 - yfinance - DEBUG - Entering history()
2025-06-12 15:57:42,058 - peewee - DEBUG - ('SELECT "t1"."key", "t1"."value" FROM "_kv" AS "t1" WHERE ("t1"."key" = ?) LIMIT ? OFFSET ?', ['AAPL', 1, 0])
2025-06-12 15:57:42,058 - yfinance - DEBUG -  Entering history()
2025-06-12 15:57:42,063 - yfinance - DEBUG - AAPL: Yahoo GET parameters: {'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:57:42,063 - yfinance - DEBUG -   Entering get()
2025-06-12 15:57:42,063 - yfinance - DEBUG -    Entering _make_request()
2025-06-12 15:57:42,064 - yfinance - DEBUG - url=https://query2.finance.yahoo.com/v8/finance/chart/AAPL
2025-06-12 15:57:42,064 - yfinance - DEBUG - params={'range': '1mo', 'interval': '1d', 'includePrePost': False, 'events': 'div,splits,capitalGains'}
2025-06-12 15:57:42,064 - yfinance - DEBUG -     Entering _get_cookie_and_crumb()
2025-06-12 15:57:42,064 - yfinance - DEBUG - cookie_mode = 'basic'
2025-06-12 15:57:42,066 - yfinance - DEBUG -      Entering _get_cookie_and_crumb_basic()
2025-06-12 15:57:42,066 - yfinance - DEBUG -       Entering _get_cookie_basic()
2025-06-12 15:57:42,068 - yfinance - DEBUG - reusing cookie
2025-06-12 15:57:42,069 - yfinance - DEBUG -       Exiting _get_cookie_basic()
2025-06-12 15:57:42,069 - yfinance - DEBUG -       Entering _get_crumb_basic()
2025-06-12 15:57:42,069 - yfinance - DEBUG - reusing crumb
2025-06-12 15:57:42,070 - yfinance - DEBUG -       Exiting _get_crumb_basic()
2025-06-12 15:57:42,071 - yfinance - DEBUG -      Exiting _get_cookie_and_crumb_basic()
2025-06-12 15:57:42,071 - yfinance - DEBUG -     Exiting _get_cookie_and_crumb()
2025-06-12 15:57:42,109 - yfinance - DEBUG - response code=200
2025-06-12 15:57:42,109 - yfinance - DEBUG -    Exiting _make_request()
2025-06-12 15:57:42,109 - yfinance - DEBUG -   Exiting get()
2025-06-12 15:57:42,112 - yfinance - DEBUG - AAPL: yfinance received OHLC data: 2025-05-12 13:30:00 -> 2025-06-11 13:30:00
2025-06-12 15:57:42,117 - yfinance - DEBUG - AAPL: OHLC after cleaning: 2025-05-12 09:30:00-04:00 -> 2025-06-11 09:30:00-04:00
2025-06-12 15:57:42,124 - yfinance - DEBUG - AAPL: OHLC after combining events: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:57:42,132 - yfinance - DEBUG - AAPL: yfinance returning OHLC: 2025-05-12 00:00:00-04:00 -> 2025-06-11 00:00:00-04:00
2025-06-12 15:57:42,132 - yfinance - DEBUG -  Exiting history()
2025-06-12 15:57:42,132 - yfinance - DEBUG - Exiting history()
2025-06-12 15:57:42,132 - financial_agent.agents.data_agent - DEBUG - Fetched 22 rows for AAPL in 0.07s
2025-06-12 15:57:42,138 - financial_agent.agents.data_agent - INFO - Successfully fetched data for AAPL
2025-06-12 15:57:42,138 - __main__ - INFO - Agent fetch completed in 0.08s
2025-06-12 15:57:42,138 - __main__ - INFO - Fetched 22 data points
2025-06-12 15:57:42,138 - __main__ - INFO - Date range: 1747022400 to 1749614400
2025-06-12 15:57:42,140 - __main__ - INFO - Latest close: $198.78
2025-06-12 15:57:42,140 - __main__ - INFO - Volume: 60,820,200
2025-06-12 15:57:42,140 - __main__ - INFO - Stopping agent...
2025-06-12 15:57:42,140 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 15:57:42,140 - __main__ - INFO - Agent stopped.
2025-06-12 15:57:42,140 - __main__ - INFO - 
================================================================================
2025-06-12 15:57:42,140 - __main__ - INFO - COMPARISON RESULTS
2025-06-12 15:57:42,140 - __main__ - INFO - ================================================================================
2025-06-12 15:57:42,142 - __main__ - INFO - Both direct and agent fetches completed successfully
2025-06-12 15:57:42,142 - __main__ - INFO - Direct rows: 22
2025-06-12 15:57:42,142 - __main__ - INFO - Agent data points: 22
2025-06-12 15:57:42,142 - __main__ - INFO - 
Comparison complete.
