"""
Mock implementation of MistralWrapper for testing and demo purposes.
"""
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass

@dataclass
class LLMResponse:
    """Mock LLM response class"""
    text: str
    metadata: Dict[str, Any] = None

class MistralWrapper:
    """Mock MistralWrapper for testing and demo purposes"""
    
    def __init__(self, model_name: str = "mistral-7b-instruct-v0.1", **kwargs):
        self.model_name = model_name
        self.kwargs = kwargs
    
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Mock generate method"""
        return LLMResponse(
            text="This is a mock response",
            metadata={"model": self.model_name, **self.kwargs, **kwargs}
        )
    
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> LLMResponse:
        """Mock chat method"""
        return LLMResponse(
            text="This is a mock chat response",
            metadata={"messages": messages, "model": self.model_name, **self.kwargs, **kwargs}
        )
    
    def __call__(self, prompt: str, **kwargs) -> str:
        """Mock call method"""
        return "This is a mock response"
