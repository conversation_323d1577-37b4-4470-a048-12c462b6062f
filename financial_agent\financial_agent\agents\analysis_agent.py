"""
Analysis Agent for processing OHLCV data and generating trading signals.
"""
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import pandas as pd
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum, auto

from .base_agent import AgentResponse

from .base_agent import BaseAgent
from .data_agent import OHLCVData

# Configure logging
logger = logging.getLogger(__name__)

class SignalType(Enum):
    """Types of trading signals."""
    BUY = auto()
    SELL = auto()
    HOLD = auto()
    STRONG_BUY = auto()
    STRONG_SELL = auto()
    NO_SIGNAL = auto()

class IndicatorType(Enum):
    """Types of technical indicators."""
    TREND = auto()
    MOMENTUM = auto()
    VOLATILITY = auto()
    VOLUME = auto()
    OSCILLATOR = auto()
    OTHER = auto()

@dataclass
class Indicator:
    """Represents a technical indicator with its value and metadata."""
    name: str
    value: float
    indicator_type: IndicatorType
    metadata: Optional[dict] = None
    
    def __post_init__(self):
        """Validate the indicator after initialization."""
        if not isinstance(self.name, str) or not self.name.strip():
            raise ValueError("Indicator name must be a non-empty string")
        if not isinstance(self.value, (int, float)):
            raise ValueError("Indicator value must be a number")

@dataclass
class AnalysisResult:
    """Result of market analysis."""
    symbol: str
    signal: SignalType
    confidence: float  # 0.0 to 1.0
    indicators: List[Indicator]
    timestamp: int = field(default_factory=lambda: int(datetime.now().timestamp()))
    metadata: Optional[Dict[str, Any]] = None
    
    @property
    def overall_signal(self) -> SignalType:
        """For backward compatibility."""
        return self.signal

class AnalysisAgent(BaseAgent):
    """Agent responsible for analyzing market data and generating trading signals."""
    
    def __init__(self, llm_wrapper=None, config: Optional[dict] = None, name: str = 'analysis'):
        """Initialize the AnalysisAgent.
        
        Args:
            llm_wrapper: Optional LLM wrapper for advanced analysis
            config: Configuration dictionary with analysis parameters
            name: Name of the agent (default: 'analysis')
        """
        super().__init__(name=name, llm_wrapper=llm_wrapper)
        self.config = config or {}
        self._setup_default_config()
        
        # Initialize technical analysis parameters
        self.rsi_period = self.config.get('rsi_period', 14)
        self.macd_fast = self.config.get('macd_fast', 12)
        self.macd_slow = self.config.get('macd_slow', 26)
        self.macd_signal = self.config.get('macd_signal', 9)
        self.bb_period = self.config.get('bb_period', 20)
        self.bb_std = self.config.get('bb_std', 2.0)
        
        # Initialize state
        self._last_analysis = {}
    
    def _setup_default_config(self):
        """Set up default configuration if not provided."""
        defaults = {
            'rsi_overbought': 70,
            'rsi_oversold': 30,
            'volume_ma_period': 20,
            'price_ma_periods': [20, 50, 200],
            'volatility_lookback': 20,
            'trend_lookback': 50,
            'use_llm_analysis': False,
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
    
    async def start(self):
        """Start the analysis agent."""
        if self.is_running:
            logger.warning("Analysis agent is already running")
            return
            
        logger.info("Starting analysis agent...")
        self.is_running = True
        logger.info("Analysis agent started")
    
    async def stop(self):
        """Stop the analysis agent."""
        if not self.is_running:
            logger.warning("Analysis agent is not running")
            return
            
        logger.info("Stopping analysis agent...")
        self.is_running = False
        logger.info("Analysis agent stopped")
    
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """Process input data and return a response.
        
        Args:
            input_data: Dictionary containing input data with 'ohlcv' key
            
        Returns:
            AgentResponse containing analysis results or error
        """
        if 'ohlcv' not in input_data or not isinstance(input_data['ohlcv'], OHLCVData):
            return AgentResponse(
                success=False,
                error="Invalid input: 'ohlcv' key with OHLCVData object is required"
            )
        
        try:
            ohlcv_data = input_data['ohlcv']
            analysis_result = await self.analyze(ohlcv_data)
            
            return AgentResponse(
                success=True,
                data={
                    'symbol': analysis_result.symbol,
                    'timestamp': analysis_result.timestamp,
                    'signal': analysis_result.overall_signal.name,
                    'confidence': analysis_result.confidence,
                    'indicators': [
                        {
                            'name': ind.name,
                            'value': ind.value,
                            'signal': ind.signal.name
                        }
                        for ind in analysis_result.indicators
                    ],
                    'metadata': analysis_result.metadata
                }
            )
        except Exception as e:
            self.logger.error(f"Error in process: {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                error=f"Analysis failed: {str(e)}"
            )
    
    async def analyze(self, ohlcv_data: OHLCVData) -> AnalysisResult:
        """Analyze OHLCV data and generate trading signals.
        
        Args:
            ohlcv_data: OHLCV data to analyze
            
        Returns:
            AnalysisResult containing trading signals and indicators
        """
        if not self.is_running:
            logger.warning("Analysis agent is not running")
            await self.start()
        
        logger.info(f"Analyzing {ohlcv_data.symbol} data...")
        
        # Convert OHLCVData to pandas DataFrame for analysis
        df = pd.DataFrame({
            'open': ohlcv_data.open,
            'high': ohlcv_data.high,
            'low': ohlcv_data.low,
            'close': ohlcv_data.close,
            'volume': ohlcv_data.volume
        }, index=pd.to_datetime(ohlcv_data.timestamp, unit='s'))
        
        indicators = []
        
        # Calculate technical indicators
        rsi = self._calculate_rsi(df['close'])
        macd_line, signal_line, _ = self._calculate_macd(df['close'])
        bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(df['close'])
        volume_ma = self._calculate_volume_ma(df['volume'])
        price_mas = self._calculate_price_mas(df['close'])
        
        # Generate signals from indicators
        rsi_signal = self._generate_rsi_signal(rsi.iloc[-1])
        macd_signal = self._generate_macd_signal(macd_line, signal_line)
        bb_signal = self._generate_bb_signal(df['close'].iloc[-1], bb_upper.iloc[-1], bb_lower.iloc[-1])
        volume_signal = self._generate_volume_signal(df['volume'].iloc[-1], volume_ma.iloc[-1])
        trend_signal = self._generate_trend_signal(df['close'], price_mas)
        
        # Create technical indicators
        indicators = [
            Indicator(
                name='RSI',
                value=rsi.iloc[-1],
                indicator_type=IndicatorType.MOMENTUM,
                metadata={
                    'overbought': self.config.get('rsi_overbought', 70),
                    'oversold': self.config.get('rsi_oversold', 30)
                }
            ),
            Indicator(
                name='MACD',
                value=macd_line.iloc[-1],
                indicator_type=IndicatorType.TREND,
                metadata={'signal': macd_signal_line.iloc[-1]}
            ),
            Indicator(
                name='Bollinger_Bands',
                value=(bb_upper.iloc[-1] - bb_lower.iloc[-1]) / bb_middle.iloc[-1],
                indicator_type=IndicatorType.VOLATILITY,
                metadata={
                    'upper': bb_upper.iloc[-1],
                    'middle': bb_middle.iloc[-1],
                    'lower': bb_lower.iloc[-1],
                    'width': (bb_upper.iloc[-1] - bb_lower.iloc[-1]) / bb_middle.iloc[-1]
                }
            ),
            Indicator(
                name='Volume_MA',
                value=volume_ma.iloc[-1],
                indicator_type=IndicatorType.VOLUME,
                metadata={
                    'period': self.config.get('volume_ma_period', 20),
                    'current_volume': ohlcv_data.volume.iloc[-1]
                }
            )
        ]
        
        # Add price trend indicator if needed
        if 'ma_50' in ohlcv_data.columns and 'ma_200' in ohlcv_data.columns:
            indicators.append(Indicator(
                name='Price_Trend',
                value=1.0 if ohlcv_data['ma_50'].iloc[-1] > ohlcv_data['ma_200'].iloc[-1] else -1.0,
                indicator_type=IndicatorType.TREND,
                metadata={
                    'ma_50': ohlcv_data['ma_50'].iloc[-1],
                    'ma_200': ohlcv_data['ma_200'].iloc[-1]
                }
            ))
        
        # Generate overall signal (simplified for now)
        overall_signal, confidence = self._generate_overall_signal(indicators)
        
        # Store last analysis for reference
        analysis_result = AnalysisResult(
            symbol=ohlcv_data.symbol,
            timestamp=ohlcv_data.timestamp[-1],
            indicators=indicators,
            overall_signal=overall_signal,
            confidence=confidence,
            metadata={
                'rsi': rsi.iloc[-1],
                'macd': macd_line.iloc[-1],
                'macd_signal': signal_line.iloc[-1],
                'bb_upper': bb_upper.iloc[-1],
                'bb_lower': bb_lower.iloc[-1],
                'volume_ma': volume_ma.iloc[-1],
                'price_mas': {f'ma_{k}': v.iloc[-1] for k, v in price_mas.items()}
            }
        )
        
        self._last_analysis[ohlcv_data.symbol] = analysis_result
        
        logger.info(f"Analysis complete for {ohlcv_data.symbol}: {overall_signal.name} (Confidence: {confidence:.2f})")
        
        return analysis_result
    
    def _calculate_rsi(self, close_prices: pd.Series, period: Optional[int] = None) -> pd.Series:
        """Calculate Relative Strength Index (RSI)."""
        period = period or self.rsi_period
        delta = close_prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, close_prices: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD indicator."""
        exp1 = close_prices.ewm(span=self.macd_fast, adjust=False).mean()
        exp2 = close_prices.ewm(span=self.macd_slow, adjust=False).mean()
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=self.macd_signal, adjust=False).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def _calculate_bollinger_bands(self, close_prices: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands."""
        middle_band = close_prices.rolling(window=self.bb_period).mean()
        std = close_prices.rolling(window=self.bb_period).std()
        upper_band = middle_band + (std * self.bb_std)
        lower_band = middle_band - (std * self.bb_std)
        return upper_band, middle_band, lower_band
    
    def _calculate_volume_ma(self, volume: pd.Series) -> pd.Series:
        """Calculate volume moving average."""
        return volume.rolling(window=self.config['volume_ma_period']).mean()
    
    def _calculate_price_mas(self, close_prices: pd.Series) -> Dict[int, pd.Series]:
        """Calculate multiple moving averages."""
        return {
            period: close_prices.rolling(window=period).mean()
            for period in self.config['price_ma_periods']
        }
    
    def _generate_rsi_signal(self, rsi_value: float) -> SignalType:
        """Generate signal based on RSI value."""
        if rsi_value > self.config['rsi_overbought']:
            return SignalType.SELL
        elif rsi_value < self.config['rsi_oversold']:
            return SignalType.BUY
        return SignalType.HOLD
    
    def _generate_macd_signal(self, macd_line: pd.Series, signal_line: pd.Series) -> SignalType:
        """Generate signal based on MACD."""
        if len(macd_line) < 2 or len(signal_line) < 2:
            return SignalType.HOLD
            
        # Check for MACD line crossing above/below signal line
        prev_macd = macd_line.iloc[-2]
        curr_macd = macd_line.iloc[-1]
        prev_signal = signal_line.iloc[-2]
        curr_signal = signal_line.iloc[-1]
        
        if prev_macd < prev_signal and curr_macd > curr_signal:
            return SignalType.BUY
        elif prev_macd > prev_signal and curr_macd < curr_signal:
            return SignalType.SELL
        return SignalType.HOLD
    
    def _generate_bb_signal(self, price: float, upper_band: float, lower_band: float) -> SignalType:
        """Generate signal based on Bollinger Bands."""
        if price > upper_band:
            return SignalType.OVERBOUGHT
        elif price < lower_band:
            return SignalType.OVERSOLD
        return SignalType.HOLD
    
    def _generate_volume_signal(self, volume: float, volume_ma: float) -> SignalType:
        """Generate signal based on volume."""
        if volume > volume_ma * 1.5:  # 50% above MA
            return SignalType.STRONG_BUY if volume > volume_ma * 2 else SignalType.BUY
        return SignalType.HOLD
    
    def _generate_trend_signal(self, prices: pd.Series, mas: Dict[int, pd.Series]) -> SignalType:
        """Generate trend signal based on moving averages."""
        if len(prices) < 2:
            return SignalType.HOLD
            
        # Check if price is above/below key MAs
        price = prices.iloc[-1]
        ma_20 = mas.get(20, pd.Series()).iloc[-1] if 20 in mas and len(mas[20]) > 0 else None
        ma_50 = mas.get(50, pd.Series()).iloc[-1] if 50 in mas and len(mas[50]) > 0 else None
        ma_200 = mas.get(200, pd.Series()).iloc[-1] if 200 in mas and len(mas[200]) > 0 else None
        
        # Check for golden cross/death cross
        if ma_20 is not None and ma_50 is not None:
            if ma_20 > ma_50 and prices.iloc[-2] < ma_50 and price > ma_50:
                return SignalType.STRONG_BUY
            elif ma_20 < ma_50 and prices.iloc[-2] > ma_50 and price < ma_50:
                return SignalType.STRONG_SELL
        
        # Check for price position relative to MAs
        if ma_200 is not None and price > ma_200 * 1.2:  # 20% above 200 MA
            return SignalType.STRONG_BUY
        elif ma_200 is not None and price < ma_200 * 0.8:  # 20% below 200 MA
            return SignalType.STRONG_SELL
            
        return SignalType.HOLD
    
    def _generate_overall_signal(self, indicators: List[Indicator]) -> Tuple[SignalType, float]:
        """Generate an overall trading signal based on multiple indicators."""
        # Simple weighted voting system
        signals = []
        weights = []
        
        # Map indicator values to signals based on indicator type
        for indicator in indicators:
            if indicator.indicator_type == IndicatorType.MOMENTUM:
                # For momentum indicators like RSI
                if 'overbought' in indicator.metadata and 'oversold' in indicator.metadata:
                    overbought = indicator.metadata['overbought']
                    oversold = indicator.metadata['oversold']
                    
                    if indicator.value >= overbought:
                        signals.append(-1.0)  # Overbought -> potential sell
                        weights.append(1.5 if indicator.value > overbought + 10 else 1.0)
                    elif indicator.value <= oversold:
                        signals.append(1.0)  # Oversold -> potential buy
                        weights.append(1.5 if indicator.value < oversold - 10 else 1.0)
                    else:
                        signals.append(0.0)  # Neutral
                        weights.append(0.5)
            elif indicator.indicator_type == IndicatorType.TREND:
                # For trend indicators like MACD
                if 'signal' in indicator.metadata:
                    signal = indicator.metadata['signal']
                    if indicator.value > signal:
                        signals.append(1.0)  # Bullish crossover
                        weights.append(1.2)
                    else:
                        signals.append(-1.0)  # Bearish crossover
                        weights.append(1.2)
            else:
                # For other indicators
                signals.append(0.0)
                weights.append(0.3)
        
        if not signals:
            return SignalType.HOLD, 0.0
            
        # Calculate weighted average signal
        weighted_sum = sum(s * w for s, w in zip(signals, weights))
        total_weight = sum(weights) or 1.0  # Avoid division by zero
        avg_signal = weighted_sum / total_weight
        
        # Normalize confidence to 0-1 range
        confidence = min(1.0, abs(avg_signal))
        
        # Determine signal type based on weighted average
        if avg_signal > 0.5:
            return SignalType.BUY, confidence
        elif avg_signal > 0.1:
            return SignalType.BUY, confidence * 0.8
        elif avg_signal < -0.5:
            return SignalType.SELL, confidence
        elif avg_signal < -0.1:
            return SignalType.SELL, confidence * 0.8
        else:
            return SignalType.HOLD, confidence
    
    def get_last_analysis(self, symbol: str) -> Optional[AnalysisResult]:
        """Get the last analysis result for a symbol."""
        return self._last_analysis.get(symbol)
    
    def get_all_analyses(self) -> Dict[str, AnalysisResult]:
        """Get all stored analysis results."""
        return self._last_analysis.copy()
