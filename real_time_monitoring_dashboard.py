#!/usr/bin/env python3
"""
REAL-TIME MONITORING DASHBOARD
==============================

Comprehensive real-time monitoring of all Loop research activities:
1. GPT-J 6B download progress tracking
2. Compression algorithm development status
3. Research iteration progress
4. System resource monitoring
5. Performance metrics and predictions

Provides autonomous status updates and optimization recommendations.
"""

import time
import json
import logging
from pathlib import Path
from typing import Dict, Any, List
import psutil
import os
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RealTimeMonitoringDashboard:
    """Real-time monitoring dashboard for all Loop activities"""
    
    def __init__(self):
        self.start_time = time.time()
        self.monitoring_data = []
        self.last_update = 0
        
        logger.info(f"📊 Real-Time Monitoring Dashboard initialized")
        logger.info(f"   Monitoring all Loop research activities")
    
    def get_gptj_download_status(self) -> Dict[str, Any]:
        """Get GPT-J 6B download status"""
        
        download_path = Path("D:/Loop/downloaded_models/gpt_j_6b")
        
        if download_path.exists():
            model_files = list(download_path.glob("**/pytorch_model.bin"))
            
            if model_files:
                model_file = model_files[0]
                current_size_gb = model_file.stat().st_size / (1024**3)
                target_size_gb = 24.2
                progress_percent = min((current_size_gb / target_size_gb) * 100, 100)
                
                # Calculate download speed
                elapsed_time = time.time() - self.start_time
                if elapsed_time > 0:
                    speed_mbps = (current_size_gb * 1024) / (elapsed_time / 60)  # MB/min
                else:
                    speed_mbps = 0
                
                # Estimate ETA
                if progress_percent > 0 and progress_percent < 100:
                    remaining_gb = target_size_gb - current_size_gb
                    eta_minutes = (remaining_gb * 1024) / speed_mbps if speed_mbps > 0 else 0
                    eta_hours = eta_minutes / 60
                else:
                    eta_hours = 0
                
                status = 'completed' if progress_percent >= 100 else 'downloading'
                
                return {
                    'status': status,
                    'progress_percent': progress_percent,
                    'current_size_gb': current_size_gb,
                    'target_size_gb': target_size_gb,
                    'download_speed_mbps': speed_mbps,
                    'eta_hours': eta_hours,
                    'elapsed_hours': elapsed_time / 3600
                }
            else:
                return {
                    'status': 'starting',
                    'progress_percent': 0,
                    'current_size_gb': 0,
                    'target_size_gb': 24.2,
                    'download_speed_mbps': 0,
                    'eta_hours': 0,
                    'elapsed_hours': 0
                }
        else:
            return {
                'status': 'not_started',
                'progress_percent': 0,
                'current_size_gb': 0,
                'target_size_gb': 24.2,
                'download_speed_mbps': 0,
                'eta_hours': 0,
                'elapsed_hours': 0
            }
    
    def get_compression_algorithm_status(self) -> Dict[str, Any]:
        """Get compression algorithm development status"""
        
        algorithms = {
            'phase1_enhanced': {
                'file': 'enhanced_compression_comprehensive_results.json',
                'status': 'unknown',
                'best_compression': 0,
                'techniques': ['quantization', 'outlier_handling', 'block_scaling']
            },
            'phase2_structured': {
                'file': 'enhanced_compression_phase2_results.json',
                'status': 'unknown',
                'best_compression': 0,
                'techniques': ['structured_pruning', 'tensor_decomposition']
            },
            'combined_system': {
                'file': 'combined_enhanced_compression_results.json',
                'status': 'unknown',
                'best_compression': 0,
                'techniques': ['quantization', 'pruning', 'decomposition']
            },
            'phase3_ultra_aggressive': {
                'file': 'phase3_ultra_aggressive_results.json',
                'status': 'unknown',
                'best_compression': 0,
                'techniques': ['bitnet', 'extreme_sparsity', 'hierarchical_decomp', 'clustering']
            }
        }
        
        for alg_name, alg_info in algorithms.items():
            result_file = Path(alg_info['file'])
            
            if result_file.exists():
                try:
                    with open(result_file, 'r') as f:
                        data = json.load(f)
                    
                    # Extract best compression ratio
                    if alg_name == 'phase1_enhanced':
                        best_compression = 0
                        for config_data in data.get('detailed_results', {}).values():
                            compression = config_data['compression_results']['summary']['overall_compression_ratio']
                            best_compression = max(best_compression, compression)
                    else:
                        if 'summary' in data:
                            best_compression = data['summary']['overall_compression_ratio']
                        else:
                            # Handle different data structures
                            best_compression = 0
                            for config_data in data.values():
                                if isinstance(config_data, dict) and 'summary' in config_data:
                                    compression = config_data['summary']['overall_compression_ratio']
                                    best_compression = max(best_compression, compression)
                    
                    algorithms[alg_name]['status'] = 'completed'
                    algorithms[alg_name]['best_compression'] = best_compression
                    
                except Exception as e:
                    algorithms[alg_name]['status'] = 'error'
                    algorithms[alg_name]['error'] = str(e)
            else:
                algorithms[alg_name]['status'] = 'not_started'
        
        return algorithms
    
    def get_autonomous_research_status(self) -> Dict[str, Any]:
        """Get autonomous research system status"""
        
        research_file = Path("autonomous_research_results.json")
        
        if research_file.exists():
            try:
                with open(research_file, 'r') as f:
                    data = json.load(f)
                
                summary = data.get('summary', {})
                best_algorithms = data.get('best_algorithms', {})
                
                return {
                    'status': 'completed',
                    'iterations_completed': summary.get('total_iterations', 0),
                    'variants_tested': summary.get('total_variants_tested', 0),
                    'research_time_minutes': summary.get('total_time_minutes', 0),
                    'best_algorithms_found': len(best_algorithms),
                    'algorithm_types': list(best_algorithms.keys())
                }
            except Exception as e:
                return {
                    'status': 'error',
                    'error': str(e)
                }
        else:
            return {
                'status': 'running',
                'iterations_completed': 0,
                'variants_tested': 0,
                'research_time_minutes': 0,
                'best_algorithms_found': 0,
                'algorithm_types': []
            }
    
    def get_system_performance(self) -> Dict[str, Any]:
        """Get current system performance metrics"""
        
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('D:/')
        
        # Process information
        current_process = psutil.Process()
        process_memory_mb = current_process.memory_info().rss / (1024**2)
        
        # GPU information (if available)
        gpu_info = {}
        if hasattr(psutil, 'gpu_percent'):  # Not standard psutil
            try:
                gpu_info = {
                    'gpu_available': True,
                    'gpu_usage_percent': 0  # Would need nvidia-ml-py for real GPU monitoring
                }
            except:
                gpu_info = {'gpu_available': False}
        else:
            gpu_info = {'gpu_available': False}
        
        return {
            'cpu_usage_percent': cpu_percent,
            'memory_total_gb': memory.total / (1024**3),
            'memory_used_gb': memory.used / (1024**3),
            'memory_available_gb': memory.available / (1024**3),
            'memory_usage_percent': memory.percent,
            'disk_total_gb': disk.total / (1024**3),
            'disk_used_gb': disk.used / (1024**3),
            'disk_free_gb': disk.free / (1024**3),
            'disk_usage_percent': (disk.used / disk.total) * 100,
            'process_memory_mb': process_memory_mb,
            'active_processes': len(psutil.pids()),
            **gpu_info
        }
    
    def calculate_research_progress(self) -> Dict[str, Any]:
        """Calculate overall research progress"""
        
        # Get status of all components
        gptj_status = self.get_gptj_download_status()
        algorithms = self.get_compression_algorithm_status()
        research = self.get_autonomous_research_status()
        
        # Calculate progress scores
        gptj_progress = gptj_status['progress_percent'] / 100
        
        algorithm_progress = 0
        completed_algorithms = sum(1 for alg in algorithms.values() if alg['status'] == 'completed')
        algorithm_progress = completed_algorithms / len(algorithms)
        
        research_progress = 1.0 if research['status'] == 'completed' else 0.5
        
        # Overall progress (weighted)
        overall_progress = (gptj_progress * 0.4 + algorithm_progress * 0.4 + research_progress * 0.2)
        
        # Best compression achieved
        best_compression = max([alg['best_compression'] for alg in algorithms.values()], default=0)
        
        # Estimate completion time
        elapsed_hours = (time.time() - self.start_time) / 3600
        if overall_progress > 0.1:
            estimated_total_hours = elapsed_hours / overall_progress
            eta_hours = estimated_total_hours - elapsed_hours
        else:
            eta_hours = 0
        
        return {
            'overall_progress_percent': overall_progress * 100,
            'gptj_progress_percent': gptj_progress * 100,
            'algorithm_progress_percent': algorithm_progress * 100,
            'research_progress_percent': research_progress * 100,
            'best_compression_ratio': best_compression,
            'elapsed_hours': elapsed_hours,
            'estimated_eta_hours': max(0, eta_hours),
            'completed_algorithms': completed_algorithms,
            'total_algorithms': len(algorithms)
        }
    
    def generate_recommendations(self) -> List[str]:
        """Generate optimization recommendations"""
        
        recommendations = []
        
        # Get current status
        gptj_status = self.get_gptj_download_status()
        algorithms = self.get_compression_algorithm_status()
        research = self.get_autonomous_research_status()
        performance = self.get_system_performance()
        progress = self.calculate_research_progress()
        
        # GPT-J download recommendations
        if gptj_status['status'] == 'downloading':
            if gptj_status['progress_percent'] > 50:
                recommendations.append("GPT-J download >50% - prepare compression pipeline")
            if gptj_status['eta_hours'] > 2:
                recommendations.append("GPT-J download will take >2 hours - continue algorithm development")
        elif gptj_status['status'] == 'completed':
            recommendations.append("GPT-J download complete - ready for large-scale compression testing")
        
        # Algorithm development recommendations
        completed_count = sum(1 for alg in algorithms.values() if alg['status'] == 'completed')
        if completed_count >= 3:
            recommendations.append("Multiple compression algorithms ready - test on GPT-J 6B")
        
        best_compression = max([alg['best_compression'] for alg in algorithms.values()], default=0)
        if best_compression > 50:
            recommendations.append(f"Excellent compression achieved ({best_compression:.1f}×) - validate on large models")
        elif best_compression > 20:
            recommendations.append(f"Good compression achieved ({best_compression:.1f}×) - optimize further")
        
        # Research recommendations
        if research['status'] == 'completed':
            recommendations.append("Autonomous research complete - apply best algorithms to GPT-J")
        
        # Performance recommendations
        if performance['memory_usage_percent'] > 80:
            recommendations.append("High memory usage - consider reducing parallel processes")
        
        if performance['cpu_usage_percent'] > 90:
            recommendations.append("High CPU usage - system at capacity")
        
        if performance['disk_free_gb'] < 30:
            recommendations.append("Low disk space - monitor storage usage")
        
        # Progress-based recommendations
        if progress['overall_progress_percent'] > 80:
            recommendations.append("Research nearly complete - prepare final validation")
        
        return recommendations
    
    def print_dashboard(self):
        """Print comprehensive dashboard"""
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"\n" + "="*80)
        print(f"🚀 LOOP REAL-TIME MONITORING DASHBOARD")
        print(f"📅 {current_time}")
        print(f"⏱️  Uptime: {(time.time() - self.start_time)/3600:.1f} hours")
        print(f"="*80)
        
        # Overall Progress
        progress = self.calculate_research_progress()
        print(f"\n📊 OVERALL PROGRESS: {progress['overall_progress_percent']:.1f}%")
        print(f"   GPT-J Download: {progress['gptj_progress_percent']:.1f}%")
        print(f"   Algorithm Development: {progress['algorithm_progress_percent']:.1f}%")
        print(f"   Autonomous Research: {progress['research_progress_percent']:.1f}%")
        print(f"   Best Compression: {progress['best_compression_ratio']:.1f}×")
        if progress['estimated_eta_hours'] > 0:
            print(f"   Estimated ETA: {progress['estimated_eta_hours']:.1f} hours")
        
        # GPT-J Download Status
        gptj = self.get_gptj_download_status()
        print(f"\n📥 GPT-J 6B DOWNLOAD:")
        print(f"   Status: {gptj['status'].upper()}")
        print(f"   Progress: {gptj['progress_percent']:.1f}%")
        print(f"   Size: {gptj['current_size_gb']:.1f}GB / {gptj['target_size_gb']:.1f}GB")
        if gptj['download_speed_mbps'] > 0:
            print(f"   Speed: {gptj['download_speed_mbps']:.1f} MB/min")
        if gptj['eta_hours'] > 0:
            print(f"   ETA: {gptj['eta_hours']:.1f} hours")
        
        # Compression Algorithms
        algorithms = self.get_compression_algorithm_status()
        print(f"\n🔬 COMPRESSION ALGORITHMS:")
        for name, info in algorithms.items():
            status_icon = "✅" if info['status'] == 'completed' else "🔄" if info['status'] == 'running' else "❌"
            print(f"   {status_icon} {name.replace('_', ' ').title()}: {info['status']}")
            if info['best_compression'] > 0:
                print(f"      Best compression: {info['best_compression']:.1f}×")
        
        # Autonomous Research
        research = self.get_autonomous_research_status()
        print(f"\n🧠 AUTONOMOUS RESEARCH:")
        print(f"   Status: {research['status'].upper()}")
        if research['iterations_completed'] > 0:
            print(f"   Iterations: {research['iterations_completed']}")
            print(f"   Variants tested: {research['variants_tested']}")
            print(f"   Best algorithms: {research['best_algorithms_found']}")
        
        # System Performance
        perf = self.get_system_performance()
        print(f"\n💻 SYSTEM PERFORMANCE:")
        print(f"   CPU: {perf['cpu_usage_percent']:.1f}%")
        print(f"   Memory: {perf['memory_usage_percent']:.1f}% ({perf['memory_available_gb']:.1f}GB available)")
        print(f"   Disk: {perf['disk_usage_percent']:.1f}% ({perf['disk_free_gb']:.1f}GB free)")
        print(f"   Process Memory: {perf['process_memory_mb']:.1f}MB")
        
        # Recommendations
        recommendations = self.generate_recommendations()
        if recommendations:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations[:5], 1):  # Show top 5
                print(f"   {i}. {rec}")
        
        print(f"\n" + "="*80)
    
    def run_continuous_monitoring(self, update_interval_seconds: int = 30, max_duration_hours: int = 8):
        """Run continuous monitoring dashboard"""
        
        logger.info(f"🔄 Starting continuous monitoring...")
        logger.info(f"   Update interval: {update_interval_seconds} seconds")
        logger.info(f"   Max duration: {max_duration_hours} hours")
        
        start_time = time.time()
        iteration = 0
        
        try:
            while True:
                iteration += 1
                
                # Print dashboard
                self.print_dashboard()
                
                # Save monitoring data
                current_data = {
                    'timestamp': time.time(),
                    'iteration': iteration,
                    'gptj_status': self.get_gptj_download_status(),
                    'algorithms': self.get_compression_algorithm_status(),
                    'research': self.get_autonomous_research_status(),
                    'performance': self.get_system_performance(),
                    'progress': self.calculate_research_progress()
                }
                
                self.monitoring_data.append(current_data)
                
                # Save to file
                monitoring_file = Path("real_time_monitoring_data.json")
                with open(monitoring_file, 'w') as f:
                    json.dump(self.monitoring_data, f, indent=2, default=str)
                
                # Check exit conditions
                elapsed_hours = (time.time() - start_time) / 3600
                if elapsed_hours >= max_duration_hours:
                    logger.info(f"⏰ Maximum monitoring duration reached")
                    break
                
                # Check if major milestones reached
                progress = current_data['progress']
                if progress['overall_progress_percent'] >= 95:
                    logger.info(f"🎉 Research nearly complete!")
                    break
                
                # Wait for next update
                time.sleep(update_interval_seconds)
                
        except KeyboardInterrupt:
            logger.info(f"⏹️ Monitoring stopped by user")
        
        logger.info(f"📊 Monitoring complete after {iteration} iterations")

def run_monitoring_dashboard():
    """Run the real-time monitoring dashboard"""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    dashboard = RealTimeMonitoringDashboard()
    
    # Print initial status
    dashboard.print_dashboard()
    
    # Start continuous monitoring
    dashboard.run_continuous_monitoring(update_interval_seconds=60, max_duration_hours=6)

if __name__ == "__main__":
    run_monitoring_dashboard()
