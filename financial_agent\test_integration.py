"""
Integration tests for the Financial Agent system.
Tests the interaction between all components.
"""
import asyncio
import pytest
import pandas as pd
from datetime import datetime, timedelta
from financial_agent.agents.data_agent import DataCollectionAgent, MarketDataRequest
from financial_agent.agents.analysis_agent import AnalysisAgent
from financial_agent.agents.strategy_agent import StrategyAgent, StrategyType
from financial_agent.agents.risk_agent import RiskManagementAgent
from financial_agent.agents.execution_agent import ExecutionAgent

class TestIntegration:
    @pytest.fixture
    async def setup_agents(self):
        """Set up all agents for testing."""
        # Initialize agents with test configuration
        config = {
            'initial_portfolio': 100000.0,
            'watchlist': ['SPY', 'QQQ'],
            'data_agent': {'cache_ttl': 300},
            'analysis_agent': {
                'indicators': ['sma', 'rsi', 'macd']
            },
            'strategy_agent': {
                'strategy': 'etf_rotation',
                'rebalance_frequency': 'daily',
                'max_positions': 3,
                'position_size': 0.2
            },
            'risk_agent': {
                'max_drawdown': 0.10,
                'max_position_risk': 0.02,
                'max_sector_exposure': 0.30,
                'max_leverage': 1.0
            },
            'execution_agent': {
                'paper_trading': True,
                'slippage': 0.001,
                'commission': 0.005
            }
        }
        
        # Initialize agents
        data_agent = DataCollectionAgent(config=config['data_agent'])
        analysis_agent = AnalysisAgent(config=config['analysis_agent'])
        strategy_agent = StrategyAgent(config=config['strategy_agent'])
        risk_agent = RiskManagementAgent(config=config['risk_agent'])
        execution_agent = ExecutionAgent(config=config['execution_agent'])
        
        # Start agents
        await data_agent.start()
        await analysis_agent.start()
        await strategy_agent.start()
        await risk_agent.start()
        await execution_agent.start()
        
        yield {
            'data_agent': data_agent,
            'analysis_agent': analysis_agent,
            'strategy_agent': strategy_agent,
            'risk_agent': risk_agent,
            'execution_agent': execution_agent,
            'config': config
        }
        
        # Cleanup
        await data_agent.stop()
        await analysis_agent.stop()
        await strategy_agent.stop()
        await risk_agent.stop()
        await execution_agent.stop()
    
    @pytest.mark.asyncio
    async def test_full_trade_cycle(self, setup_agents):
        """Test a complete trade cycle from data collection to execution."""
        agents = await setup_agents
        
        # 1. Fetch market data
        request = MarketDataRequest(
            symbols=agents['config']['watchlist'],
            interval='1d',
            period='1mo'  # Last month of data
        )
        data_response = await agents['data_agent'].process({'request': request})
        assert data_response.success, "Data collection failed"
        
        # 2. Analyze the data
        analysis_response = await agents['analysis_agent'].process({
            'ohlcv': data_response.data
        })
        assert analysis_response.success, "Analysis failed"
        
        # 3. Generate trading signals
        signals_response = await agents['strategy_agent'].process({
            'analysis': analysis_response.data,
            'portfolio': agents['execution_agent'].get_portfolio_summary()
        })
        assert signals_response.success, "Signal generation failed"
        
        # 4. Apply risk management
        risk_response = await agents['risk_agent'].process({
            'signals': signals_response.data,
            'portfolio': agents['execution_agent'].get_portfolio_summary()
        })
        assert risk_response.success, "Risk assessment failed"
        
        # 5. Execute trades
        if risk_response.data.get('is_acceptable', False):
            execution_response = await agents['execution_agent'].process({
                'signals': risk_response.data.get('approved_signals', [])
            })
            assert execution_response.success, "Trade execution failed"
            
            # Verify positions were updated
            portfolio = agents['execution_agent'].get_portfolio_summary()
            assert len(portfolio['positions']) > 0, "No positions were opened"
            
            print("\nTrade execution successful!")
            print(f"Final portfolio value: ${portfolio['total_value']:,.2f}")
            print(f"Positions: {[p['symbol'] for p in portfolio['positions']]}")
        else:
            print("\nNo trades executed based on risk assessment")
            print(f"Reason: {risk_response.data.get('reasons', ['No reason provided'])}")

if __name__ == "__main__":
    import asyncio
    
    async def run_test():
        test = TestIntegration()
        agents = await test.setup_agents()
        try:
            await test.test_full_trade_cycle(agents)
        finally:
            await agents['data_agent'].stop()
            await agents['analysis_agent'].stop()
            await agents['strategy_agent'].stop()
            await agents['risk_agent'].stop()
            await agents['execution_agent'].stop()
    
    asyncio.run(run_test())
