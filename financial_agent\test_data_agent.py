"""
Test script for DataCollectionAgent with enhanced logging.
"""
import asyncio
import logging
import sys
import os

# Set up logging to both console and file
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_agent_test.log', mode='w')
    ]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

async def test_data_agent():
    """Test the DataCollectionAgent with enhanced logging."""
    logger.info("Starting DataCollectionAgent test...")
    
    # Initialize the agent with mock LLM
    mock_llm = MistralWrapper()
    agent = DataCollectionAgent(llm_wrapper=mock_llm)
    
    try:
        # Start the agent
        logger.info("Starting agent...")
        await agent.start()
        
        # Test fetching data for different symbols and intervals
        test_cases = [
            ("AAPL", "1d", "1mo"),  # Daily data for 1 month
            ("MSFT", "1h", "5d"),   # Hourly data for 5 days
            ("GOOGL", "1d", "1y"),   # Daily data for 1 year
        ]
        
        for symbol, interval, period in test_cases:
            try:
                logger.info(f"\n{'='*80}")
                logger.info(f"Testing {symbol} with interval={interval}, period={period}")
                logger.info(f"{'='*80}")
                
                # Fetch OHLCV data
                ohlcv = await agent.fetch_ohlcv(
                    symbol=symbol,
                    interval=interval,
                    period=period,
                    timeout=30,
                    max_retries=3
                )
                
                if ohlcv:
                    logger.info(f"Successfully fetched {len(ohlcv.timestamp)} data points for {symbol}")
                    logger.info(f"Date range: {ohlcv.timestamp[0]} to {ohlcv.timestamp[-1]}")
                    logger.info(f"Latest close: ${ohlcv.close[-1]:.2f}")
                    logger.info(f"Volume: {ohlcv.volume[-1]:,}")
                else:
                    logger.error(f"Failed to fetch data for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error testing {symbol}: {str(e)}", exc_info=True)
                continue
                
    except Exception as e:
        logger.error(f"Error in test_data_agent: {str(e)}", exc_info=True)
    finally:
        # Stop the agent
        if agent.is_running:
            logger.info("Stopping agent...")
            await agent.stop()
            logger.info("Agent stopped.")

if __name__ == "__main__":
    asyncio.run(test_data_agent())
