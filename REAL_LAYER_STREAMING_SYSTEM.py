#!/usr/bin/env python3
"""
REAL LAYER STREAMING SYSTEM
===========================

REAL implementation of layer streaming for actual RAM reduction
Starting from verified baseline: 2.58GB → 1.72GB (1.5× with float16)
Target: Further reduce RAM through actual layer streaming
"""

import os
import torch
import psutil
import time
import json
import gc
from typing import Dict, Any, List, Optional
from safetensors import safe_open
from transformers import AutoTokenizer, AutoModelForCausalLM
import numpy as np
from datetime import datetime

class RealRAMMeasurement:
    """Real RAM measurement system"""
    
    def __init__(self):
        self.measurements = []
        
    def measure_ram(self, description: str) -> Dict[str, float]:
        """Measure actual RAM usage"""
        
        process = psutil.Process()
        memory_info = process.memory_info()
        system_memory = psutil.virtual_memory()
        
        measurement = {
            'timestamp': time.time(),
            'description': description,
            'process_ram_mb': memory_info.rss / (1024**2),
            'process_ram_gb': memory_info.rss / (1024**3),
            'system_ram_gb': system_memory.used / (1024**3),
            'available_ram_gb': system_memory.available / (1024**3),
            'total_ram_gb': system_memory.total / (1024**3)
        }
        
        self.measurements.append(measurement)
        
        print(f"📊 RAM: {description} = {measurement['process_ram_gb']:.3f}GB ({measurement['process_ram_mb']:.1f}MB)")
        
        return measurement
    
    def get_ram_reduction(self, before_desc: str, after_desc: str) -> Dict[str, float]:
        """Calculate real RAM reduction between two measurements"""
        
        before = None
        after = None
        
        for m in self.measurements:
            if before_desc in m['description']:
                before = m
            if after_desc in m['description']:
                after = m
        
        if before and after:
            reduction_gb = before['process_ram_gb'] - after['process_ram_gb']
            reduction_ratio = before['process_ram_gb'] / after['process_ram_gb'] if after['process_ram_gb'] > 0 else 1.0
            
            return {
                'before_gb': before['process_ram_gb'],
                'after_gb': after['process_ram_gb'],
                'reduction_gb': reduction_gb,
                'reduction_ratio': reduction_ratio,
                'reduction_percent': (reduction_gb / before['process_ram_gb']) * 100 if before['process_ram_gb'] > 0 else 0
            }
        
        return {}

class RealLayerStreaming:
    """Real layer streaming implementation"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.ram_tracker = RealRAMMeasurement()
        self.loaded_layers = {}
        self.layer_cache = {}
        
        print(f"🚀 REAL LAYER STREAMING SYSTEM")
        print(f"📁 Model: {model_path}")
        
    def load_model_metadata(self) -> Dict[str, Any]:
        """Load model metadata without loading weights"""
        
        self.ram_tracker.measure_ram("before_metadata_load")
        
        try:
            # Load tokenizer
            tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            
            # Load model index
            index_path = os.path.join(self.model_path, "model.safetensors.index.json")
            with open(index_path, 'r') as f:
                weight_index = json.load(f)
            
            # Load config
            config_path = os.path.join(self.model_path, "config.json")
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            self.ram_tracker.measure_ram("after_metadata_load")
            
            metadata = {
                'tokenizer': tokenizer,
                'weight_index': weight_index,
                'config': config,
                'num_layers': config.get('num_hidden_layers', 32),
                'vocab_size': config.get('vocab_size', 32000),
                'hidden_size': config.get('hidden_size', 4096)
            }
            
            print(f"📊 Model metadata loaded:")
            print(f"   Layers: {metadata['num_layers']}")
            print(f"   Hidden size: {metadata['hidden_size']}")
            print(f"   Vocab size: {metadata['vocab_size']}")
            
            return metadata
            
        except Exception as e:
            print(f"❌ Error loading metadata: {e}")
            return {}
    
    def load_single_layer(self, layer_name: str, weight_index: Dict) -> Optional[torch.Tensor]:
        """Load a single layer into RAM"""
        
        if layer_name in self.layer_cache:
            return self.layer_cache[layer_name]
        
        if layer_name not in weight_index['weight_map']:
            print(f"⚠️ Layer not found: {layer_name}")
            return None
        
        try:
            file_name = weight_index['weight_map'][layer_name]
            file_path = os.path.join(self.model_path, file_name)
            
            with safe_open(file_path, framework="pt", device="cpu") as f:
                layer_tensor = f.get_tensor(layer_name)
                
                # Cache the layer
                self.layer_cache[layer_name] = layer_tensor.clone()
                
                print(f"✅ Loaded layer: {layer_name} ({layer_tensor.shape})")
                
                return layer_tensor
                
        except Exception as e:
            print(f"❌ Error loading layer {layer_name}: {e}")
            return None
    
    def unload_layer(self, layer_name: str):
        """Unload a layer from RAM"""
        
        if layer_name in self.layer_cache:
            del self.layer_cache[layer_name]
            gc.collect()
            print(f"🗑️ Unloaded layer: {layer_name}")
    
    def test_streaming_vs_full_load(self, max_layers: int = 5) -> Dict[str, Any]:
        """Test streaming approach vs full model loading"""
        
        print(f"\n🧪 TESTING: Streaming vs Full Load")
        print("=" * 50)
        
        # Load metadata
        metadata = self.load_model_metadata()
        if not metadata:
            return {}
        
        weight_index = metadata['weight_index']
        
        # Get layer names
        layer_names = [name for name in weight_index['weight_map'].keys() 
                      if 'layers.' in name and '.weight' in name][:max_layers]
        
        print(f"📊 Testing with {len(layer_names)} layers")
        
        # Test 1: Load all layers at once (traditional approach)
        self.ram_tracker.measure_ram("before_full_load")
        
        all_layers = {}
        for layer_name in layer_names:
            layer_tensor = self.load_single_layer(layer_name, weight_index)
            if layer_tensor is not None:
                all_layers[layer_name] = layer_tensor
        
        self.ram_tracker.measure_ram("after_full_load")
        
        # Test 2: Streaming approach (load one at a time)
        # Clear all layers first
        self.layer_cache.clear()
        gc.collect()
        
        self.ram_tracker.measure_ram("before_streaming")
        
        streaming_results = []
        
        for i, layer_name in enumerate(layer_names):
            # Load current layer
            layer_tensor = self.load_single_layer(layer_name, weight_index)
            
            if layer_tensor is not None:
                # Measure RAM with single layer
                measurement = self.ram_tracker.measure_ram(f"streaming_layer_{i}")
                
                # Simulate processing (matrix multiplication)
                if layer_tensor.dim() == 2:
                    # Simple computation to simulate inference
                    test_input = torch.randn(1, layer_tensor.shape[1])
                    output = torch.matmul(test_input, layer_tensor.t())
                    
                streaming_results.append({
                    'layer_name': layer_name,
                    'layer_shape': list(layer_tensor.shape),
                    'ram_gb': measurement['process_ram_gb'],
                    'computation_done': True
                })
                
                # Unload layer (except keep last one for comparison)
                if i < len(layer_names) - 1:
                    self.unload_layer(layer_name)
        
        self.ram_tracker.measure_ram("after_streaming")
        
        # Calculate results
        full_load_reduction = self.ram_tracker.get_ram_reduction("before_full_load", "after_full_load")
        streaming_reduction = self.ram_tracker.get_ram_reduction("before_streaming", "after_streaming")
        
        # Compare approaches
        if full_load_reduction and streaming_reduction:
            ram_savings = full_load_reduction['after_gb'] - streaming_reduction['after_gb']
            streaming_efficiency = full_load_reduction['after_gb'] / streaming_reduction['after_gb'] if streaming_reduction['after_gb'] > 0 else 1.0
            
            results = {
                'test_type': 'streaming_vs_full_load',
                'layers_tested': len(layer_names),
                'full_load': {
                    'ram_before_gb': full_load_reduction['before_gb'],
                    'ram_after_gb': full_load_reduction['after_gb'],
                    'ram_increase_gb': full_load_reduction['after_gb'] - full_load_reduction['before_gb']
                },
                'streaming': {
                    'ram_before_gb': streaming_reduction['before_gb'],
                    'ram_after_gb': streaming_reduction['after_gb'],
                    'ram_increase_gb': streaming_reduction['after_gb'] - streaming_reduction['before_gb'],
                    'layer_results': streaming_results
                },
                'comparison': {
                    'ram_savings_gb': ram_savings,
                    'streaming_efficiency': streaming_efficiency,
                    'streaming_better': ram_savings > 0
                },
                'measurements': self.ram_tracker.measurements
            }
            
            print(f"\n📊 STREAMING TEST RESULTS:")
            print(f"   Full load RAM: {full_load_reduction['after_gb']:.3f}GB")
            print(f"   Streaming RAM: {streaming_reduction['after_gb']:.3f}GB")
            print(f"   RAM savings: {ram_savings:.3f}GB")
            print(f"   Efficiency: {streaming_efficiency:.2f}× better")
            print(f"   Streaming wins: {'✅ YES' if ram_savings > 0 else '❌ NO'}")
            
            return results
        
        return {}
    
    def test_layer_by_layer_inference(self, text: str = "Hello, how are you?") -> Dict[str, Any]:
        """Test layer-by-layer inference with real RAM measurements"""
        
        print(f"\n🧪 TESTING: Layer-by-layer inference")
        print(f"📝 Input text: '{text}'")
        print("=" * 50)
        
        # Load metadata
        metadata = self.load_model_metadata()
        if not metadata:
            return {}
        
        tokenizer = metadata['tokenizer']
        weight_index = metadata['weight_index']
        
        # Tokenize input
        self.ram_tracker.measure_ram("before_tokenization")
        
        inputs = tokenizer(text, return_tensors="pt")
        input_ids = inputs['input_ids']
        
        self.ram_tracker.measure_ram("after_tokenization")
        
        print(f"📊 Tokenized: {input_ids.shape} tokens")
        
        # Test streaming inference on embedding and first few layers
        layer_results = []
        
        # Load embedding layer
        embedding_layer_name = 'model.embed_tokens.weight'
        if embedding_layer_name in weight_index['weight_map']:
            
            self.ram_tracker.measure_ram("before_embedding_load")
            
            embedding_tensor = self.load_single_layer(embedding_layer_name, weight_index)
            
            if embedding_tensor is not None:
                self.ram_tracker.measure_ram("after_embedding_load")
                
                # Perform embedding lookup
                embeddings = torch.nn.functional.embedding(input_ids, embedding_tensor)
                
                self.ram_tracker.measure_ram("after_embedding_computation")
                
                layer_results.append({
                    'layer_name': embedding_layer_name,
                    'layer_type': 'embedding',
                    'input_shape': list(input_ids.shape),
                    'output_shape': list(embeddings.shape),
                    'ram_after_load_gb': self.ram_tracker.measurements[-2]['process_ram_gb'],
                    'ram_after_compute_gb': self.ram_tracker.measurements[-1]['process_ram_gb']
                })
                
                print(f"✅ Embedding: {input_ids.shape} → {embeddings.shape}")
                
                # Unload embedding
                self.unload_layer(embedding_layer_name)
                
                self.ram_tracker.measure_ram("after_embedding_unload")
        
        # Test first transformer layer
        first_layer_names = [name for name in weight_index['weight_map'].keys() 
                           if 'layers.0.' in name and '.weight' in name][:3]  # First 3 weights of layer 0
        
        for layer_name in first_layer_names:
            self.ram_tracker.measure_ram(f"before_{layer_name.split('.')[-2]}_load")
            
            layer_tensor = self.load_single_layer(layer_name, weight_index)
            
            if layer_tensor is not None:
                self.ram_tracker.measure_ram(f"after_{layer_name.split('.')[-2]}_load")
                
                # Simple computation test
                if layer_tensor.dim() == 2 and embeddings.shape[-1] == layer_tensor.shape[0]:
                    test_output = torch.matmul(embeddings, layer_tensor.t())
                    
                    self.ram_tracker.measure_ram(f"after_{layer_name.split('.')[-2]}_compute")
                    
                    layer_results.append({
                        'layer_name': layer_name,
                        'layer_type': 'transformer_weight',
                        'layer_shape': list(layer_tensor.shape),
                        'computation_successful': True,
                        'ram_after_load_gb': self.ram_tracker.measurements[-2]['process_ram_gb'],
                        'ram_after_compute_gb': self.ram_tracker.measurements[-1]['process_ram_gb']
                    })
                    
                    print(f"✅ {layer_name}: computation successful")
                
                # Unload layer
                self.unload_layer(layer_name)
                
                self.ram_tracker.measure_ram(f"after_{layer_name.split('.')[-2]}_unload")
        
        # Final measurement
        self.ram_tracker.measure_ram("inference_test_complete")
        
        results = {
            'test_type': 'layer_by_layer_inference',
            'input_text': text,
            'layer_results': layer_results,
            'total_layers_tested': len(layer_results),
            'measurements': self.ram_tracker.measurements
        }
        
        print(f"\n📊 INFERENCE TEST RESULTS:")
        print(f"   Layers tested: {len(layer_results)}")
        print(f"   Successful computations: {sum(1 for r in layer_results if r.get('computation_successful', False))}")
        
        return results

def main():
    """Test real layer streaming system"""
    
    print("🚀 REAL LAYER STREAMING SYSTEM")
    print("=" * 60)
    print("GOAL: Reduce RAM through actual layer streaming")
    print("BASELINE: 2.58GB → 1.72GB (1.5× with float16)")
    print()
    
    # Model path
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Initialize streaming system
    streaming_system = RealLayerStreaming(model_path)
    
    # Test 1: Streaming vs Full Load
    streaming_results = streaming_system.test_streaming_vs_full_load(max_layers=3)
    
    # Test 2: Layer-by-layer inference
    inference_results = streaming_system.test_layer_by_layer_inference("The future of AI is")
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"real_layer_streaming_results_{timestamp}.json"
    
    complete_results = {
        'timestamp': time.time(),
        'test_type': 'real_layer_streaming',
        'model_path': model_path,
        'baseline': {
            'description': 'Verified baseline from previous testing',
            'original_ram_gb': 2.58,
            'compressed_ram_gb': 1.72,
            'compression_ratio': 1.5,
            'method': 'float16_conversion'
        },
        'streaming_test': streaming_results,
        'inference_test': inference_results
    }
    
    with open(results_file, 'w') as f:
        json.dump(complete_results, f, indent=2, default=str)
    
    print(f"\n✅ REAL TESTING COMPLETE")
    print(f"📄 Results saved: {results_file}")
    
    # Summary
    if streaming_results and 'comparison' in streaming_results:
        comp = streaming_results['comparison']
        print(f"\n📊 STREAMING SUMMARY:")
        print(f"   RAM savings: {comp['ram_savings_gb']:.3f}GB")
        print(f"   Efficiency: {comp['streaming_efficiency']:.2f}×")
        print(f"   Method works: {'✅ YES' if comp['streaming_better'] else '❌ NO'}")

if __name__ == "__main__":
    main()
