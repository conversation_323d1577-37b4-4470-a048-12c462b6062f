{"llm": {"model_name": "mistralai/Mistral-7B-v0.1", "device": "cuda", "generation": {"max_new_tokens": 1024, "temperature": 0.7, "top_p": 0.9, "top_k": 50, "repetition_penalty": 1.1, "do_sample": true}}, "agents": {"data": {"enabled": true, "config": {"data_sources": ["binance", "coinbase", "kraken"], "update_interval": 60, "max_retries": 3}}, "analysis": {"enabled": true, "config": {"timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"], "indicators": ["rsi", "macd", "bollinger_bands", "volume_profile"], "sentiment_analysis": true}}, "strategy": {"enabled": true, "config": {"max_position_size": 0.1, "take_profit": 0.05, "stop_loss": 0.03, "max_trades_per_day": 10}}, "execution": {"enabled": true, "config": {"exchange": "binance", "test_mode": true, "max_slippage": 0.001}}, "risk": {"enabled": true, "config": {"max_drawdown": 0.1, "max_risk_per_trade": 0.02, "max_portfolio_risk": 0.2, "volatility_threshold": 0.05}}}, "logging": {"level": "INFO", "file": "logs/financial_agent.log", "max_size_mb": 100, "backup_count": 5}, "api": {"enabled": true, "host": "0.0.0.0", "port": 8000, "auth_required": true, "cors_origins": ["http://localhost:3000"]}, "monitoring": {"metrics_enabled": true, "prometheus_port": 9090, "health_check_interval": 300}}