#!/usr/bin/env python3
"""
AI SCIENTIST AUTONOMOUS ARCHITECTURE SEARCH
===========================================

Integrates the evolutionary architecture search with Loop's AI scientist system
for autonomous discovery of efficient transformer architectures.

Features:
- LLM-guided architecture generation
- Autonomous hypothesis formation
- Continuous learning from results
- Research paper generation
- Self-improving search strategies
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import numpy as np
from dataclasses import asdict

from autonomous_architecture_search import (
    ArchitectureGenome, FitnessMetrics, EvolutionaryOptimizer, ArchitectureEvaluator
)
from ai_scientist_components import (
    ResearchDatabase, HypothesisGenerator, ExperimentPlanner, ResultsAnalyzer
)
from paper_generator import PaperGenerator

logger = logging.getLogger(__name__)

class AIScientistArchitectureSearch:
    """AI scientist for autonomous transformer architecture discovery"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Core components
        self.evolutionary_optimizer = EvolutionaryOptimizer(config)
        self.architecture_evaluator = ArchitectureEvaluator(config)
        
        # AI scientist components
        self.research_database = ResearchDatabase()
        self.hypothesis_generator = HypothesisGenerator(config)
        self.experiment_planner = ExperimentPlanner(config)
        self.results_analyzer = ResultsAnalyzer(config)
        self.paper_generator = PaperGenerator(config)
        
        # Search state
        self.current_generation = 0
        self.research_history = []
        self.discovered_architectures = []
        self.research_insights = []
        
        # Autonomous operation
        self.is_running = False
        self.target_generations = config.get('target_generations', 100)
        self.save_interval = config.get('save_interval', 10)
        
    async def run_autonomous_search(self) -> Dict[str, Any]:
        """Run autonomous architecture search with AI scientist guidance"""
        
        logger.info("🚀 Starting autonomous transformer architecture search")
        logger.info(f"Target: {self.target_generations} generations")
        
        self.is_running = True
        start_time = time.time()
        
        try:
            # Initialize population
            self.evolutionary_optimizer.initialize_population()
            
            # Main search loop
            for generation in range(self.target_generations):
                if not self.is_running:
                    break
                
                self.current_generation = generation
                logger.info(f"\n🧬 Generation {generation + 1}/{self.target_generations}")
                
                # Generate hypotheses for this generation
                hypotheses = await self.hypothesis_generator.generate_hypotheses(
                    self.research_history, self.evolutionary_optimizer.population
                )
                
                # Plan experiments based on hypotheses
                experiments = self.experiment_planner.plan_experiments(
                    hypotheses, self.evolutionary_optimizer.population
                )
                
                # Evaluate population
                fitness_scores = await self._evaluate_population_async(
                    self.evolutionary_optimizer.population
                )
                
                # Analyze results and extract insights
                insights = self.results_analyzer.analyze_generation_results(
                    self.evolutionary_optimizer.population, fitness_scores, hypotheses
                )
                
                # Store research data
                generation_data = {
                    'generation': generation,
                    'population': [genome.to_dict() for genome in self.evolutionary_optimizer.population],
                    'fitness_scores': fitness_scores,
                    'hypotheses': hypotheses,
                    'insights': insights,
                    'timestamp': time.time()
                }
                
                self.research_history.append(generation_data)
                self.research_insights.extend(insights)
                
                # Update discovered architectures
                self._update_discovered_architectures(
                    self.evolutionary_optimizer.population, fitness_scores
                )
                
                # Evolve to next generation
                if generation < self.target_generations - 1:
                    self.evolutionary_optimizer.evolve_generation(fitness_scores)
                
                # Adaptive strategy updates
                await self._update_search_strategies(insights)
                
                # Save progress periodically
                if (generation + 1) % self.save_interval == 0:
                    self._save_progress()
                
                # Generate interim reports
                if (generation + 1) % 20 == 0:
                    await self._generate_interim_report(generation + 1)
                
                logger.info(f"Best fitness: {max(fitness_scores):.4f}")
                logger.info(f"Population diversity: {self._calculate_diversity():.3f}")
            
            # Final analysis and paper generation
            final_results = await self._finalize_search()
            
            total_time = time.time() - start_time
            logger.info(f"🎉 Search completed in {total_time/3600:.2f} hours")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Autonomous search failed: {e}")
            raise
        finally:
            self.is_running = False
    
    async def _evaluate_population_async(self, population: List[ArchitectureGenome]) -> List[float]:
        """Evaluate population asynchronously for better performance"""
        
        logger.info(f"Evaluating population of {len(population)} architectures")
        
        # Create evaluation tasks
        tasks = []
        for i, genome in enumerate(population):
            task = asyncio.create_task(self._evaluate_genome_async(genome, i))
            tasks.append(task)
        
        # Wait for all evaluations to complete
        fitness_metrics = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Extract fitness scores
        fitness_scores = []
        for i, result in enumerate(fitness_metrics):
            if isinstance(result, Exception):
                logger.warning(f"Evaluation {i} failed: {result}")
                fitness_scores.append(0.0)
            else:
                fitness_scores.append(result.fitness_score)
        
        return fitness_scores
    
    async def _evaluate_genome_async(self, genome: ArchitectureGenome, index: int) -> FitnessMetrics:
        """Evaluate a single genome asynchronously"""
        
        # Add small delay to prevent overwhelming the system
        await asyncio.sleep(0.1)
        
        try:
            # Run evaluation in thread pool for CPU-bound work
            loop = asyncio.get_event_loop()
            metrics = await loop.run_in_executor(
                None, self.architecture_evaluator.evaluate_architecture, genome
            )
            return metrics
            
        except Exception as e:
            logger.warning(f"Genome {index} evaluation failed: {e}")
            return FitnessMetrics(fitness_score=0.0)
    
    def _update_discovered_architectures(self, population: List[ArchitectureGenome], 
                                       fitness_scores: List[float]) -> None:
        """Update list of discovered high-performing architectures"""
        
        # Find top performers
        top_indices = np.argsort(fitness_scores)[-5:]  # Top 5
        
        for idx in top_indices:
            if fitness_scores[idx] > 0.7:  # High fitness threshold
                arch_data = {
                    'genome': population[idx].to_dict(),
                    'fitness': fitness_scores[idx],
                    'generation': self.current_generation,
                    'discovery_time': time.time()
                }
                
                # Check if already discovered (avoid duplicates)
                is_duplicate = False
                for existing in self.discovered_architectures:
                    if self._genomes_similar(population[idx], 
                                           ArchitectureGenome.from_dict(existing['genome'])):
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    self.discovered_architectures.append(arch_data)
                    logger.info(f"🎯 Discovered new high-performing architecture: "
                               f"fitness {fitness_scores[idx]:.4f}")
    
    def _genomes_similar(self, genome1: ArchitectureGenome, 
                        genome2: ArchitectureGenome, threshold: float = 0.9) -> bool:
        """Check if two genomes are similar"""
        
        # Compare key parameters
        key_params = ['num_layers', 'hidden_size', 'num_heads', 'compression_ratio']
        
        similarity_score = 0.0
        for param in key_params:
            val1 = getattr(genome1, param)
            val2 = getattr(genome2, param)
            
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                if val1 == 0 and val2 == 0:
                    similarity_score += 1.0
                elif val1 == 0 or val2 == 0:
                    similarity_score += 0.0
                else:
                    similarity_score += 1.0 - abs(val1 - val2) / max(abs(val1), abs(val2))
            else:
                similarity_score += 1.0 if val1 == val2 else 0.0
        
        return (similarity_score / len(key_params)) >= threshold
    
    async def _update_search_strategies(self, insights: List[Dict[str, Any]]) -> None:
        """Update search strategies based on insights"""
        
        for insight in insights:
            insight_type = insight.get('type', '')
            
            if insight_type == 'convergence_slow':
                # Increase mutation rate
                self.evolutionary_optimizer.mutation_rate *= 1.1
                logger.info("🔄 Increased mutation rate due to slow convergence")
                
            elif insight_type == 'diversity_low':
                # Increase population diversity
                self.evolutionary_optimizer.mutation_strength *= 1.2
                logger.info("🔄 Increased mutation strength to improve diversity")
                
            elif insight_type == 'memory_constraint_hit':
                # Adjust constraints to favor smaller models
                self.evolutionary_optimizer.constraints['hidden_size'] = (256, 1024)
                logger.info("🔄 Adjusted constraints to favor smaller models")
                
            elif insight_type == 'accuracy_plateau':
                # Focus on advanced features
                # This could trigger more sophisticated mutations
                logger.info("🔄 Focusing on advanced architectural features")
    
    def _calculate_diversity(self) -> float:
        """Calculate population diversity"""
        
        if len(self.evolutionary_optimizer.population) < 2:
            return 0.0
        
        # Calculate pairwise distances
        distances = []
        population = self.evolutionary_optimizer.population
        
        for i in range(len(population)):
            for j in range(i + 1, len(population)):
                distance = self._genome_distance(population[i], population[j])
                distances.append(distance)
        
        return np.mean(distances) if distances else 0.0
    
    def _genome_distance(self, genome1: ArchitectureGenome, 
                        genome2: ArchitectureGenome) -> float:
        """Calculate distance between two genomes"""
        
        # Normalize and compare key parameters
        params1 = genome1.to_dict()
        params2 = genome2.to_dict()
        
        distance = 0.0
        count = 0
        
        for key in params1:
            val1 = params1[key]
            val2 = params2[key]
            
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                if val1 != 0 or val2 != 0:
                    max_val = max(abs(val1), abs(val2), 1.0)
                    distance += abs(val1 - val2) / max_val
                    count += 1
            elif isinstance(val1, bool) and isinstance(val2, bool):
                distance += 0.0 if val1 == val2 else 1.0
                count += 1
        
        return distance / max(count, 1)
    
    async def _generate_interim_report(self, generation: int) -> None:
        """Generate interim research report"""
        
        logger.info(f"📊 Generating interim report for generation {generation}")
        
        try:
            report = await self.paper_generator.generate_interim_report(
                generation, self.research_history, self.discovered_architectures
            )
            
            # Save report
            report_path = Path(f"reports/interim_report_gen_{generation}.md")
            report_path.parent.mkdir(exist_ok=True)
            
            with open(report_path, 'w') as f:
                f.write(report)
            
            logger.info(f"📄 Interim report saved to {report_path}")
            
        except Exception as e:
            logger.warning(f"Failed to generate interim report: {e}")
    
    async def _finalize_search(self) -> Dict[str, Any]:
        """Finalize search and generate comprehensive results"""
        
        logger.info("🔬 Finalizing search and generating comprehensive analysis")
        
        # Get best architectures
        best_architectures = sorted(
            self.discovered_architectures, 
            key=lambda x: x['fitness'], 
            reverse=True
        )[:10]  # Top 10
        
        # Generate final research paper
        final_paper = await self.paper_generator.generate_final_paper(
            self.research_history, best_architectures, self.research_insights
        )
        
        # Save final paper
        paper_path = Path("reports/final_architecture_search_paper.md")
        paper_path.parent.mkdir(exist_ok=True)
        
        with open(paper_path, 'w') as f:
            f.write(final_paper)
        
        # Compile final results
        final_results = {
            'total_generations': self.current_generation + 1,
            'best_architectures': best_architectures,
            'research_insights': self.research_insights,
            'population_diversity_history': [
                self._calculate_diversity_for_generation(gen_data)
                for gen_data in self.research_history
            ],
            'fitness_progression': [
                max(gen_data['fitness_scores']) 
                for gen_data in self.research_history
            ],
            'final_paper_path': str(paper_path),
            'search_statistics': self._compile_search_statistics()
        }
        
        # Save complete results
        results_path = Path("results/final_search_results.json")
        results_path.parent.mkdir(exist_ok=True)
        
        with open(results_path, 'w') as f:
            json.dump(final_results, f, indent=2, default=str)
        
        logger.info(f"🎉 Final results saved to {results_path}")
        logger.info(f"📄 Final paper saved to {paper_path}")
        
        return final_results
    
    def _calculate_diversity_for_generation(self, gen_data: Dict[str, Any]) -> float:
        """Calculate diversity for a specific generation"""
        
        population = [ArchitectureGenome.from_dict(g) for g in gen_data['population']]
        
        if len(population) < 2:
            return 0.0
        
        distances = []
        for i in range(len(population)):
            for j in range(i + 1, len(population)):
                distance = self._genome_distance(population[i], population[j])
                distances.append(distance)
        
        return np.mean(distances) if distances else 0.0
    
    def _compile_search_statistics(self) -> Dict[str, Any]:
        """Compile comprehensive search statistics"""
        
        if not self.research_history:
            return {}
        
        fitness_scores = [
            score for gen_data in self.research_history 
            for score in gen_data['fitness_scores']
        ]
        
        return {
            'total_architectures_evaluated': len(fitness_scores),
            'best_fitness_achieved': max(fitness_scores) if fitness_scores else 0.0,
            'average_fitness': np.mean(fitness_scores) if fitness_scores else 0.0,
            'fitness_std': np.std(fitness_scores) if fitness_scores else 0.0,
            'high_performing_architectures': len(self.discovered_architectures),
            'unique_insights_discovered': len(self.research_insights),
            'convergence_rate': self._calculate_convergence_rate(),
            'search_efficiency': self._calculate_search_efficiency()
        }
    
    def _calculate_convergence_rate(self) -> float:
        """Calculate how quickly the search converged"""
        
        if len(self.research_history) < 2:
            return 0.0
        
        # Look at improvement rate over generations
        best_fitness_per_gen = [
            max(gen_data['fitness_scores']) 
            for gen_data in self.research_history
        ]
        
        improvements = []
        for i in range(1, len(best_fitness_per_gen)):
            improvement = best_fitness_per_gen[i] - best_fitness_per_gen[i-1]
            improvements.append(max(0, improvement))
        
        return np.mean(improvements) if improvements else 0.0
    
    def _calculate_search_efficiency(self) -> float:
        """Calculate search efficiency (good architectures found per evaluation)"""
        
        total_evaluations = sum(
            len(gen_data['fitness_scores']) 
            for gen_data in self.research_history
        )
        
        if total_evaluations == 0:
            return 0.0
        
        return len(self.discovered_architectures) / total_evaluations
    
    def _save_progress(self) -> None:
        """Save current search progress"""
        
        progress_data = {
            'current_generation': self.current_generation,
            'research_history': self.research_history[-10:],  # Last 10 generations
            'discovered_architectures': self.discovered_architectures,
            'research_insights': self.research_insights[-50:],  # Last 50 insights
            'optimizer_state': {
                'mutation_rate': self.evolutionary_optimizer.mutation_rate,
                'mutation_strength': self.evolutionary_optimizer.mutation_strength,
                'best_fitness': self.evolutionary_optimizer.best_fitness
            }
        }
        
        progress_path = Path("checkpoints/search_progress.json")
        progress_path.parent.mkdir(exist_ok=True)
        
        with open(progress_path, 'w') as f:
            json.dump(progress_data, f, indent=2, default=str)
        
        logger.info(f"💾 Progress saved to {progress_path}")
    
    def stop_search(self) -> None:
        """Stop the autonomous search"""
        logger.info("🛑 Stopping autonomous search")
        self.is_running = False
