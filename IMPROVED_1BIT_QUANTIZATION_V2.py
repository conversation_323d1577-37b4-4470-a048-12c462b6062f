#!/usr/bin/env python3
"""
IMPROVED 1-BIT QUANTIZATION V2
==============================

Improve 1-bit quantization based on excellent 0.58% error result
Target: 4-8× compression with <5% quality loss
Current: 2× compression with 0.58% error (excellent baseline)
"""

import os
import torch
import psutil
import time
import json
import gc
import numpy as np
from typing import Dict, Any, List, Tuple
from safetensors import safe_open
from datetime import datetime

class Improved1BitQuantizerV2:
    """Improved 1-bit quantization with advanced techniques"""
    
    def __init__(self):
        self.ram_measurements = []
        
        print(f"⚡ IMPROVED 1-BIT QUANTIZATION V2")
        print(f"📊 Baseline: 2× compression, 0.58% error")
        print(f"🎯 Target: 4-8× compression, <5% error")
        
    def measure_ram(self, description: str) -> float:
        """Measure RAM and return GB"""
        process = psutil.Process()
        ram_gb = process.memory_info().rss / (1024**3)
        
        self.ram_measurements.append({
            'timestamp': time.time(),
            'description': description,
            'ram_gb': ram_gb
        })
        
        print(f"📊 RAM: {description} = {ram_gb:.3f}GB")
        return ram_gb
    
    def outlier_preserving_1bit(self, tensor: torch.Tensor, outlier_ratio: float = 0.01) -> Dict[str, Any]:
        """1-bit quantization with outlier preservation for better quality"""
        
        print(f"🔬 Outlier-preserving 1-bit: {tensor.shape}")
        
        tensor_f32 = tensor.to(torch.float32)
        
        # Identify outliers (top 1% by magnitude)
        abs_weights = torch.abs(tensor_f32)
        outlier_cutoff = torch.quantile(abs_weights, 1.0 - outlier_ratio)
        
        outlier_mask = abs_weights > outlier_cutoff
        outlier_weights = tensor_f32[outlier_mask]
        normal_weights = tensor_f32[~outlier_mask]
        
        print(f"   Outliers: {torch.sum(outlier_mask).item()} ({outlier_ratio*100:.1f}%)")
        
        # Quantize normal weights to 1-bit
        if len(normal_weights) > 0:
            normal_mean = torch.mean(normal_weights)
            normal_std = torch.std(normal_weights)
            
            centered_normal = normal_weights - normal_mean
            binary_normal = torch.sign(centered_normal)
            binary_normal_uint8 = ((binary_normal + 1) / 2).to(torch.uint8)
        else:
            normal_mean = 0
            normal_std = 1
            binary_normal_uint8 = torch.tensor([], dtype=torch.uint8)
        
        # Keep outliers in float16 for better precision
        outlier_weights_f16 = outlier_weights.to(torch.float16)
        
        # Calculate compression
        original_size = tensor.numel() * tensor.element_size()
        compressed_size = (
            binary_normal_uint8.numel() * binary_normal_uint8.element_size() +  # 1-bit weights
            outlier_weights_f16.numel() * outlier_weights_f16.element_size() +  # float16 outliers
            outlier_mask.numel() * 1 // 8  # 1 bit per position for mask
        )
        compression_ratio = original_size / compressed_size
        
        # Reconstruct for quality assessment
        reconstructed = torch.zeros_like(tensor_f32)
        
        if len(binary_normal_uint8) > 0:
            reconstructed_normal = (binary_normal_uint8.to(torch.float32) * 2 - 1) * normal_std + normal_mean
            reconstructed[~outlier_mask] = reconstructed_normal
        
        reconstructed[outlier_mask] = outlier_weights_f16.to(torch.float32)
        
        # Quality metrics
        mse_error = torch.mean((tensor_f32 - reconstructed) ** 2).item()
        mae_error = torch.mean(torch.abs(tensor_f32 - reconstructed)).item()
        
        tensor_range = torch.max(tensor_f32) - torch.min(tensor_f32)
        relative_error = mae_error / tensor_range.item() if tensor_range > 0 else 0
        
        return {
            'method': 'outlier_preserving_1bit',
            'compression_ratio': compression_ratio,
            'outlier_ratio': outlier_ratio,
            'outliers_count': torch.sum(outlier_mask).item(),
            'quality_metrics': {
                'mse_error': mse_error,
                'mae_error': mae_error,
                'relative_error_percent': relative_error * 100
            }
        }
    
    def test_single_layer(self, model_path: str) -> Dict[str, Any]:
        """Test improved quantization on a single layer"""
        
        print(f"\n🧪 TESTING SINGLE LAYER")
        print("=" * 30)
        
        if not os.path.exists(model_path):
            print(f"❌ Model not found: {model_path}")
            return {}
        
        self.measure_ram("before_test")
        
        try:
            # Load model index
            index_path = os.path.join(model_path, "model.safetensors.index.json")
            with open(index_path, 'r') as f:
                weight_index = json.load(f)
            
            # Find test layer
            test_layer = None
            for layer_name in weight_index['weight_map'].keys():
                if 'q_proj.weight' in layer_name:
                    test_layer = layer_name
                    break
            
            if not test_layer:
                print("❌ No suitable test layer found")
                return {}
            
            print(f"📊 Test layer: {test_layer}")
            
            # Load layer
            file_name = weight_index['weight_map'][test_layer]
            file_path = os.path.join(model_path, file_name)
            
            with safe_open(file_path, framework="pt", device="cpu") as f:
                tensor = f.get_tensor(test_layer)
                
                self.measure_ram("after_load")
                
                print(f"📊 Shape: {tensor.shape}")
                print(f"📊 Dtype: {tensor.dtype}")
                
                # Test different outlier ratios
                outlier_ratios = [0.005, 0.01, 0.02, 0.05]  # 0.5%, 1%, 2%, 5%
                
                results = {}
                
                for ratio in outlier_ratios:
                    print(f"\n🔬 Testing outlier ratio: {ratio*100:.1f}%")
                    
                    result = self.outlier_preserving_1bit(tensor, ratio)
                    
                    if result:
                        results[f"outlier_{ratio*100:.1f}pct"] = result
                        
                        print(f"   Compression: {result['compression_ratio']:.1f}×")
                        print(f"   Error: {result['quality_metrics']['relative_error_percent']:.2f}%")
                
                # Find best result
                best_method = None
                best_score = 0
                
                for method_name, result in results.items():
                    # Score = compression / (1 + error_penalty)
                    error = result['quality_metrics']['relative_error_percent']
                    compression = result['compression_ratio']
                    
                    # Penalty for error >1%
                    error_penalty = max(0, error - 1.0) * 0.2
                    score = compression / (1 + error_penalty)
                    
                    if score > best_score:
                        best_score = score
                        best_method = method_name
                
                self.measure_ram("after_test")
                
                final_result = {
                    'test_layer': test_layer,
                    'layer_shape': list(tensor.shape),
                    'all_results': results,
                    'best_method': best_method,
                    'best_score': best_score
                }
                
                if best_method and best_method in results:
                    best_result = results[best_method]
                    final_result['best_compression'] = best_result['compression_ratio']
                    final_result['best_error'] = best_result['quality_metrics']['relative_error_percent']
                    
                    print(f"\n🏆 BEST METHOD: {best_method}")
                    print(f"   Compression: {best_result['compression_ratio']:.1f}×")
                    print(f"   Error: {best_result['quality_metrics']['relative_error_percent']:.2f}%")
                
                return final_result
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return {}

def main():
    """Test improved 1-bit quantization"""
    
    print("🚀 IMPROVED 1-BIT QUANTIZATION V2")
    print("=" * 50)
    print("GOAL: Improve on 0.58% error baseline")
    print("TARGET: 4-8× compression, <5% error")
    print()
    
    # Model path
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Initialize quantizer
    quantizer = Improved1BitQuantizerV2()
    
    # Test on single layer
    results = quantizer.test_single_layer(model_path)
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"improved_1bit_v2_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✅ IMPROVED 1-BIT V2 TESTING COMPLETE")
    print(f"📄 Results saved: {results_file}")
    
    # Summary
    if results and 'best_compression' in results:
        print(f"\n📊 SUMMARY:")
        print(f"   Best compression: {results['best_compression']:.1f}×")
        print(f"   Best error: {results['best_error']:.2f}%")
        
        # Compare with baseline
        baseline_compression = 2.0
        baseline_error = 0.58
        
        improvement = results['best_compression'] / baseline_compression
        error_increase = results['best_error'] - baseline_error
        
        print(f"\n📈 VS BASELINE:")
        print(f"   Compression improvement: {improvement:.1f}×")
        print(f"   Error increase: +{error_increase:.2f}%")
        print(f"   Better overall: {'✅ YES' if improvement > 1.5 and error_increase < 4.0 else '❌ NO'}")

if __name__ == "__main__":
    main()
