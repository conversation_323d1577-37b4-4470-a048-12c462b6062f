#!/usr/bin/env python3
"""
🔥 REAL COMPLETE TEST - NO BULLSHIT, NO SIMULATIONS
===================================================

COMPLETE END-TO-END TEST:
1. Load REAL 876M model
2. Compress with REAL algorithms  
3. Load compressed weights BACK into model
4. Run ACTUAL inference on compressed model
5. Compare REAL outputs between original vs compressed
6. Measure ACTUAL accuracy, not estimates

NO LIES. NO SIMULATIONS. REAL RESULTS ONLY.
"""

import torch
import torch.nn as nn
import numpy as np
import time
from transformers import GPT2LMHeadModel, GPT2Tokenizer
from collections import OrderedDict
import gc

class RealCompleteTest:
    """Complete real test with actual compressed model inference"""
    
    def __init__(self):
        self.compressed_layers = {}
        self.layer_metadata = {}
        self.original_outputs = []
        self.compressed_outputs = []
        
    def load_real_model(self):
        """Load the actual 876M model"""
        
        print("🔥 LOADING REAL 876M MODEL - NO BULLSHIT")
        print("=" * 50)
        
        model_path = "downloaded_models/gpt2-large/models--gpt2-large/snapshots/32b71b12589c2f8d625668d2335a01cac3249519"
        
        try:
            model = GPT2LMHeadModel.from_pretrained(model_path, torch_dtype=torch.float32)
            tokenizer = GPT2Tokenizer.from_pretrained("gpt2-large")
            tokenizer.pad_token = tokenizer.eos_token
            
            total_params = sum(p.numel() for p in model.parameters())
            total_size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
            
            print(f"✅ REAL MODEL LOADED:")
            print(f"   Parameters: {total_params:,}")
            print(f"   Size: {total_size_mb:.1f}MB")
            
            return model, tokenizer, total_params, total_size_mb
            
        except Exception as e:
            print(f"❌ FAILED TO LOAD REAL MODEL: {e}")
            return None, None, 0, 0
    
    def compress_layer_real(self, weight: torch.Tensor, layer_name: str):
        """Real compression - no bullshit"""
        
        weight_np = weight.detach().cpu().numpy()
        original_size = weight_np.nbytes
        
        # Ultra-aggressive compression
        flat_weight = weight_np.flatten()
        
        # Keep only top 5% of weights (95% sparsity)
        k = max(1, int(0.05 * len(flat_weight)))
        top_k_indices = np.argpartition(np.abs(flat_weight), -k)[-k:]
        sparse_values = flat_weight[top_k_indices]
        
        # 2-bit quantization (4 levels: -1.5, -0.5, 0.5, 1.5)
        if len(sparse_values) > 0:
            scale = np.max(np.abs(sparse_values)) / 1.5
            quantized = np.round(sparse_values / scale * 1.5)
            quantized = np.clip(quantized, -1.5, 1.5)
            
            # Map to 2-bit values: -1.5->0, -0.5->1, 0.5->2, 1.5->3
            quantized_2bit = np.round((quantized + 1.5) / 0.5).astype(np.uint8)
            
            # Pack 4 values per byte
            padding = (4 - (len(quantized_2bit) % 4)) % 4
            if padding > 0:
                quantized_2bit = np.append(quantized_2bit, np.zeros(padding))
            
            packed = (quantized_2bit[::4] << 6) | (quantized_2bit[1::4] << 4) | (quantized_2bit[2::4] << 2) | quantized_2bit[3::4]
        else:
            packed = np.array([], dtype=np.uint8)
            scale = 1.0
            padding = 0
        
        # Store compressed data
        compressed_data = {
            'packed_values': packed,
            'indices': top_k_indices.astype(np.uint32),
            'scale': scale,
            'shape': weight_np.shape,
            'nnz': len(sparse_values),
            'padding': padding
        }
        
        compressed_size = packed.nbytes + top_k_indices.nbytes + 32
        compression_ratio = original_size / max(compressed_size, 1)
        
        return compressed_data, compression_ratio
    
    def decompress_layer_real(self, compressed_data):
        """Real decompression - no bullshit"""
        
        packed = compressed_data['packed_values']
        indices = compressed_data['indices']
        scale = compressed_data['scale']
        shape = compressed_data['shape']
        nnz = compressed_data['nnz']
        padding = compressed_data['padding']
        
        if len(packed) > 0:
            # Unpack 2-bit values
            unpacked = np.zeros(len(packed) * 4, dtype=np.uint8)
            unpacked[::4] = (packed >> 6) & 0x3
            unpacked[1::4] = (packed >> 4) & 0x3
            unpacked[2::4] = (packed >> 2) & 0x3
            unpacked[3::4] = packed & 0x3
            
            # Remove padding
            if padding > 0:
                unpacked = unpacked[:-padding]
            
            # Convert back to float: 0->-1.5, 1->-0.5, 2->0.5, 3->1.5
            values = (unpacked.astype(np.float32) * 0.5 - 1.5) * scale
        else:
            values = np.array([])
        
        # Reconstruct dense tensor
        dense = np.zeros(shape, dtype=np.float32)
        if len(values) > 0 and len(indices) > 0:
            flat_dense = dense.flatten()
            flat_dense[indices] = values
            dense = flat_dense.reshape(shape)
        
        return torch.from_numpy(dense)
    
    def compress_real_model(self, model):
        """Compress the real model - no bullshit"""
        
        print(f"\n🔥 COMPRESSING REAL MODEL - NO BULLSHIT")
        print("=" * 45)
        
        total_original_size = 0
        total_compressed_size = 0
        layer_count = 0
        
        # Compress each layer
        for name, module in model.named_modules():
            if hasattr(module, 'weight') and module.weight is not None:
                weight = module.weight
                original_size = weight.numel() * weight.element_size()
                
                # Compress layer
                compressed_data, compression_ratio = self.compress_layer_real(weight, name)
                
                # Store compressed data
                self.compressed_layers[name] = compressed_data
                
                compressed_size = (compressed_data['packed_values'].nbytes + 
                                 compressed_data['indices'].nbytes + 32)
                
                total_original_size += original_size
                total_compressed_size += compressed_size
                layer_count += 1
                
                if layer_count <= 10:
                    print(f"  {name}: {compression_ratio:.1f}× compression")
                elif layer_count == 11:
                    print(f"  ... compressing {sum(1 for n, m in model.named_modules() if hasattr(m, 'weight') and m.weight is not None) - 10} more layers")
        
        overall_ratio = total_original_size / total_compressed_size
        compressed_mb = total_compressed_size / (1024 * 1024)
        
        print(f"\n✅ REAL COMPRESSION COMPLETE:")
        print(f"   Compressed size: {compressed_mb:.1f}MB")
        print(f"   Compression ratio: {overall_ratio:.1f}×")
        
        return compressed_mb, overall_ratio
    
    def load_compressed_weights_into_model(self, model):
        """Load compressed weights BACK into the model - REAL TEST"""
        
        print(f"\n🔥 LOADING COMPRESSED WEIGHTS INTO MODEL - REAL TEST")
        print("=" * 55)
        
        loaded_count = 0
        
        # Replace each layer's weights with decompressed versions
        for name, module in model.named_modules():
            if hasattr(module, 'weight') and module.weight is not None:
                if name in self.compressed_layers:
                    # Decompress and load
                    decompressed_weight = self.decompress_layer_real(self.compressed_layers[name])
                    
                    # Replace the weight
                    with torch.no_grad():
                        module.weight.copy_(decompressed_weight)
                    
                    loaded_count += 1
                    
                    if loaded_count <= 5:
                        print(f"  ✅ Loaded compressed weights for {name}")
                    elif loaded_count == 6:
                        print(f"  ... loaded {sum(1 for n in self.compressed_layers.keys()) - 5} more layers")
        
        print(f"✅ COMPRESSED WEIGHTS LOADED: {loaded_count} layers")
        return loaded_count
    
    def test_real_generation_comparison(self, original_model, compressed_model, tokenizer):
        """REAL generation comparison - original vs compressed"""
        
        print(f"\n🔥 REAL GENERATION COMPARISON - NO BULLSHIT")
        print("=" * 50)
        
        test_prompts = [
            "The quick brown fox",
            "Machine learning is",
            "The capital of France is",
            "In the future, AI will",
            "Python programming"
        ]
        
        real_accuracy_scores = []
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n🔥 TEST {i+1}: '{prompt}'")
            print("-" * 40)
            
            # Tokenize
            inputs = tokenizer.encode(prompt, return_tensors='pt')
            
            # Generate with ORIGINAL model
            print("🔄 Generating with ORIGINAL model...")
            with torch.no_grad():
                original_outputs = original_model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 15,
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)
                )
            
            original_text = tokenizer.decode(original_outputs[0], skip_special_tokens=True)
            
            # Generate with COMPRESSED model
            print("🔄 Generating with COMPRESSED model...")
            with torch.no_grad():
                compressed_outputs = compressed_model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 15,
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)
                )
            
            compressed_text = tokenizer.decode(compressed_outputs[0], skip_special_tokens=True)
            
            # Calculate REAL similarity
            original_tokens = tokenizer.encode(original_text)
            compressed_tokens = tokenizer.encode(compressed_text)
            
            # Token-level accuracy
            min_len = min(len(original_tokens), len(compressed_tokens))
            if min_len > 0:
                matching_tokens = sum(1 for j in range(min_len) if original_tokens[j] == compressed_tokens[j])
                token_accuracy = matching_tokens / min_len * 100
            else:
                token_accuracy = 0
            
            real_accuracy_scores.append(token_accuracy)
            
            print(f"📝 ORIGINAL:   '{original_text}'")
            print(f"📝 COMPRESSED: '{compressed_text}'")
            print(f"📊 REAL TOKEN ACCURACY: {token_accuracy:.1f}%")
            
            # Store for analysis
            self.original_outputs.append(original_text)
            self.compressed_outputs.append(compressed_text)
        
        # Calculate overall REAL accuracy
        real_average_accuracy = np.mean(real_accuracy_scores)
        
        print(f"\n🎯 REAL ACCURACY RESULTS:")
        print(f"   Individual scores: {[f'{score:.1f}%' for score in real_accuracy_scores]}")
        print(f"   REAL average accuracy: {real_average_accuracy:.1f}%")
        print(f"   Target 95%+: {'✅ YES' if real_average_accuracy >= 95 else '❌ NO'}")
        
        return real_average_accuracy, real_accuracy_scores
    
    def test_real_memory_usage(self):
        """Test REAL memory usage"""
        
        print(f"\n🔥 REAL MEMORY USAGE TEST")
        print("=" * 30)
        
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        # Calculate compressed model size
        compressed_size = 0
        for layer_data in self.compressed_layers.values():
            compressed_size += (layer_data['packed_values'].nbytes + 
                              layer_data['indices'].nbytes + 32)
        
        compressed_mb = compressed_size / (1024 * 1024)
        
        print(f"✅ REAL MEMORY MEASUREMENTS:")
        print(f"   Process memory: {memory_mb:.1f}MB")
        print(f"   Compressed model: {compressed_mb:.1f}MB")
        print(f"   Target <100MB: {'✅ YES' if compressed_mb < 100 else '❌ NO'}")
        
        return compressed_mb, memory_mb

def main():
    """COMPLETE REAL TEST - NO BULLSHIT"""
    
    print("🔥🔥🔥 COMPLETE REAL TEST - NO BULLSHIT, NO SIMULATIONS 🔥🔥🔥")
    print("=" * 80)
    
    tester = RealCompleteTest()
    
    # Step 1: Load REAL model
    print("STEP 1: LOADING REAL MODEL")
    original_model, tokenizer, total_params, total_size_mb = tester.load_real_model()
    if original_model is None:
        print("❌ FAILED - Cannot continue without real model")
        return
    
    # Step 2: Compress REAL model
    print("\nSTEP 2: COMPRESSING REAL MODEL")
    compressed_mb, compression_ratio = tester.compress_real_model(original_model)
    
    # Step 3: Create copy for compressed version
    print("\nSTEP 3: CREATING COMPRESSED MODEL COPY")
    compressed_model = GPT2LMHeadModel.from_pretrained(
        "downloaded_models/gpt2-large/models--gpt2-large/snapshots/32b71b12589c2f8d625668d2335a01cac3249519",
        torch_dtype=torch.float32
    )
    
    # Step 4: Load compressed weights into model
    print("\nSTEP 4: LOADING COMPRESSED WEIGHTS")
    loaded_layers = tester.load_compressed_weights_into_model(compressed_model)
    
    # Step 5: REAL generation comparison
    print("\nSTEP 5: REAL GENERATION COMPARISON")
    real_accuracy, accuracy_scores = tester.test_real_generation_comparison(
        original_model, compressed_model, tokenizer
    )
    
    # Step 6: REAL memory measurement
    print("\nSTEP 6: REAL MEMORY MEASUREMENT")
    compressed_mb_final, process_memory = tester.test_real_memory_usage()
    
    # FINAL REAL RESULTS
    print(f"\n🔥🔥🔥 FINAL REAL RESULTS - NO BULLSHIT 🔥🔥🔥")
    print("=" * 60)
    print(f"✅ REAL MODEL: GPT-2 Large ({total_params/1_000_000:.1f}M params)")
    print(f"✅ REAL COMPRESSION: {compression_ratio:.1f}× ratio")
    print(f"✅ REAL COMPRESSED SIZE: {compressed_mb_final:.1f}MB")
    print(f"✅ REAL MEMORY TARGET: {'✅ YES' if compressed_mb_final < 100 else '❌ NO'} (<100MB)")
    print(f"✅ REAL ACCURACY: {real_accuracy:.1f}%")
    print(f"✅ REAL ACCURACY TARGET: {'✅ YES' if real_accuracy >= 95 else '❌ NO'} (95%+)")
    print(f"✅ REAL LAYERS COMPRESSED: {loaded_layers}")
    print(f"✅ REAL GENERATION TESTED: {len(accuracy_scores)} prompts")
    
    # Overall REAL success
    memory_success = compressed_mb_final < 100
    accuracy_success = real_accuracy >= 95
    overall_success = memory_success and accuracy_success
    
    print(f"\n🏆 OVERALL REAL SUCCESS: {'✅ YES' if overall_success else '❌ NO'}")
    
    if overall_success:
        print("🔥 REAL STREAMING WEIGHTS COMPRESSION WORKS!")
        print("🔥 NO BULLSHIT - ACTUAL RESULTS PROVEN!")
    else:
        print("🔥 REAL RESULTS SHOWN - SOME TARGETS MISSED")
        if not memory_success:
            print(f"   Memory: {compressed_mb_final:.1f}MB (target: <100MB)")
        if not accuracy_success:
            print(f"   Accuracy: {real_accuracy:.1f}% (target: 95%+)")
    
    print(f"\n🔥 NO LIES, NO SIMULATIONS - THESE ARE REAL RESULTS! 🔥")
    
    return {
        'real_compression_ratio': compression_ratio,
        'real_compressed_size_mb': compressed_mb_final,
        'real_accuracy': real_accuracy,
        'real_memory_success': memory_success,
        'real_accuracy_success': accuracy_success,
        'real_overall_success': overall_success,
        'real_outputs': {
            'original': tester.original_outputs,
            'compressed': tester.compressed_outputs
        }
    }

if __name__ == "__main__":
    main()
