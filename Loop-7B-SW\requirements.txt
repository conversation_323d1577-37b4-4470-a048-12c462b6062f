# Loop 7B SW - Streaming Weights Compression
# Core dependencies for ultra-low memory 7B model inference

# Core ML libraries
torch>=2.0.0
transformers>=4.30.0
safetensors>=0.3.0
numpy>=1.21.0

# Memory monitoring and system utilities
psutil>=5.9.0

# Optional: For enhanced performance
accelerate>=0.20.0

# Development dependencies (optional)
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0

# Documentation (optional)
mkdocs>=1.4.0
mkdocs-material>=9.0.0
