2025-06-12 15:50:19,390 - __main__ - INFO - Initializing DataCollectionAgent with mock LLM...
2025-06-12 15:50:19,390 - agent.data - INFO - Initialized data agent
2025-06-12 15:50:19,390 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 15:50:19,392 - __main__ - INFO - Starting agent...
2025-06-12 15:50:19,393 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 15:50:19,393 - __main__ - INFO - 
==================================================
2025-06-12 15:50:19,393 - __main__ - INFO - EXAMPLE 1: FETCHING DAILY DATA
2025-06-12 15:50:19,393 - __main__ - INFO - ==================================================
2025-06-12 15:50:19,393 - __main__ - INFO - ==================================================
2025-06-12 15:50:19,396 - __main__ - INFO - FETCHING 1D DATA FOR AAPL, MSFT, GOOGL
2025-06-12 15:50:19,396 - __main__ - INFO - Time period: 1mo
2025-06-12 15:50:19,397 - __main__ - INFO - ==================================================
2025-06-12 15:50:19,397 - __main__ - INFO - 
Fetching AAPL data...
2025-06-12 15:50:19,908 - __main__ - ERROR -   Error fetching data for AAPL: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:50:19,913 - __main__ - INFO - 
Fetching MSFT data...
2025-06-12 15:50:20,058 - __main__ - ERROR -   Error fetching data for MSFT: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:50:20,058 - __main__ - INFO - 
Fetching GOOGL data...
2025-06-12 15:50:20,269 - __main__ - ERROR -   Error fetching data for GOOGL: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:50:20,271 - __main__ - WARNING - No data returned for any symbols
2025-06-12 15:50:20,272 - __main__ - INFO - 
==================================================
2025-06-12 15:50:20,272 - __main__ - INFO - EXAMPLE 2: MARKET HOURS INFORMATION
2025-06-12 15:50:20,273 - __main__ - INFO - ==================================================
2025-06-12 15:50:20,277 - __main__ - ERROR - Error getting market hours: 'datetime.datetime' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 124, in main
    logger.info(f"  Market Open: {datetime.fromtimestamp(market_open).strftime('%Y-%m-%d %H:%M:%S')}")
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'datetime.datetime' object cannot be interpreted as an integer
2025-06-12 15:50:20,280 - __main__ - INFO - 
==================================================
2025-06-12 15:50:20,281 - __main__ - INFO - EXAMPLE 3: INTRADAY DATA
2025-06-12 15:50:20,282 - __main__ - INFO - ==================================================
2025-06-12 15:50:20,283 - __main__ - INFO - 
Fetching hourly data for AAPL from 2025-06-09 to 2025-06-12...
2025-06-12 15:50:20,461 - __main__ - ERROR - Error in Example 3: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 146, in main
    if aapl_data and hasattr(aapl_data, 'timestamp') and aapl_data.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:50:20,462 - __main__ - INFO - 
Demo completed successfully!
2025-06-12 15:50:20,462 - __main__ - INFO - Stopping DataCollectionAgent...
2025-06-12 15:50:20,462 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 15:50:20,462 - __main__ - INFO - DataCollectionAgent has been stopped.
2025-06-12 15:53:04,155 - __main__ - INFO - Initializing DataCollectionAgent with mock LLM...
2025-06-12 15:53:04,155 - agent.data - INFO - Initialized data agent
2025-06-12 15:53:04,155 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 15:53:04,155 - __main__ - INFO - Starting agent...
2025-06-12 15:53:04,155 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 15:53:04,157 - __main__ - INFO - 
==================================================
2025-06-12 15:53:04,157 - __main__ - INFO - EXAMPLE 1: FETCHING DAILY DATA
2025-06-12 15:53:04,158 - __main__ - INFO - ==================================================
2025-06-12 15:53:04,158 - __main__ - INFO - ==================================================
2025-06-12 15:53:04,158 - __main__ - INFO - FETCHING 1D DATA FOR AAPL, MSFT, GOOGL
2025-06-12 15:53:04,158 - __main__ - INFO - Time period: 1mo
2025-06-12 15:53:04,158 - __main__ - INFO - ==================================================
2025-06-12 15:53:04,158 - __main__ - INFO - 
Fetching AAPL data...
2025-06-12 15:53:04,208 - financial_agent.agents.data_agent - WARNING - Attempt 1 failed for AAPL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:04,215 - financial_agent.agents.data_agent - INFO - Retry attempt 2/3 for AAPL
2025-06-12 15:53:05,229 - financial_agent.agents.data_agent - WARNING - Attempt 2 failed for AAPL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:05,230 - financial_agent.agents.data_agent - INFO - Retry attempt 3/3 for AAPL
2025-06-12 15:53:07,234 - financial_agent.agents.data_agent - WARNING - Attempt 3 failed for AAPL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:07,234 - financial_agent.agents.data_agent - ERROR - Failed to fetch data for AAPL after 3 attempts
Traceback (most recent call last):
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 265, in fetch_ohlcv
    df = await loop.run_in_executor(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 267, in <lambda>
    lambda: ticker.history(
            ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\base.py", line 96, in history
    return self._lazy_load_price_history().history(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
TypeError: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:07,243 - __main__ - WARNING -   No data returned for AAPL
2025-06-12 15:53:07,243 - __main__ - INFO - 
Fetching MSFT data...
2025-06-12 15:53:07,248 - financial_agent.agents.data_agent - WARNING - Attempt 1 failed for MSFT: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:07,248 - financial_agent.agents.data_agent - INFO - Retry attempt 2/3 for MSFT
2025-06-12 15:53:08,259 - financial_agent.agents.data_agent - WARNING - Attempt 2 failed for MSFT: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:08,260 - financial_agent.agents.data_agent - INFO - Retry attempt 3/3 for MSFT
2025-06-12 15:53:10,255 - financial_agent.agents.data_agent - WARNING - Attempt 3 failed for MSFT: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:10,255 - financial_agent.agents.data_agent - ERROR - Failed to fetch data for MSFT after 3 attempts
Traceback (most recent call last):
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 265, in fetch_ohlcv
    df = await loop.run_in_executor(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 267, in <lambda>
    lambda: ticker.history(
            ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\base.py", line 96, in history
    return self._lazy_load_price_history().history(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
TypeError: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:10,258 - __main__ - WARNING -   No data returned for MSFT
2025-06-12 15:53:10,259 - __main__ - INFO - 
Fetching GOOGL data...
2025-06-12 15:53:10,259 - financial_agent.agents.data_agent - WARNING - Attempt 1 failed for GOOGL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:10,259 - financial_agent.agents.data_agent - INFO - Retry attempt 2/3 for GOOGL
2025-06-12 15:53:11,274 - financial_agent.agents.data_agent - WARNING - Attempt 2 failed for GOOGL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:11,274 - financial_agent.agents.data_agent - INFO - Retry attempt 3/3 for GOOGL
2025-06-12 15:53:13,289 - financial_agent.agents.data_agent - WARNING - Attempt 3 failed for GOOGL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:13,290 - financial_agent.agents.data_agent - ERROR - Failed to fetch data for GOOGL after 3 attempts
Traceback (most recent call last):
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 265, in fetch_ohlcv
    df = await loop.run_in_executor(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 267, in <lambda>
    lambda: ticker.history(
            ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\base.py", line 96, in history
    return self._lazy_load_price_history().history(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
TypeError: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:13,290 - __main__ - WARNING -   No data returned for GOOGL
2025-06-12 15:53:13,292 - __main__ - WARNING - No data returned for any symbols
2025-06-12 15:53:13,292 - __main__ - INFO - 
==================================================
2025-06-12 15:53:13,292 - __main__ - INFO - EXAMPLE 2: MARKET HOURS INFORMATION
2025-06-12 15:53:13,292 - __main__ - INFO - ==================================================
2025-06-12 15:53:13,292 - __main__ - ERROR - Error getting market hours: 'datetime.datetime' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 124, in main
    logger.info(f"  Market Open: {datetime.fromtimestamp(market_open).strftime('%Y-%m-%d %H:%M:%S')}")
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'datetime.datetime' object cannot be interpreted as an integer
2025-06-12 15:53:13,292 - __main__ - INFO - 
==================================================
2025-06-12 15:53:13,292 - __main__ - INFO - EXAMPLE 3: INTRADAY DATA
2025-06-12 15:53:13,292 - __main__ - INFO - ==================================================
2025-06-12 15:53:13,292 - __main__ - INFO - 
Fetching hourly data for AAPL from 2025-06-09 to 2025-06-12...
2025-06-12 15:53:13,299 - financial_agent.agents.data_agent - WARNING - Attempt 1 failed for AAPL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:13,299 - financial_agent.agents.data_agent - INFO - Retry attempt 2/3 for AAPL
2025-06-12 15:53:14,310 - financial_agent.agents.data_agent - WARNING - Attempt 2 failed for AAPL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:14,310 - financial_agent.agents.data_agent - INFO - Retry attempt 3/3 for AAPL
2025-06-12 15:53:16,317 - financial_agent.agents.data_agent - WARNING - Attempt 3 failed for AAPL: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:16,317 - financial_agent.agents.data_agent - ERROR - Failed to fetch data for AAPL after 3 attempts
Traceback (most recent call last):
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 265, in fetch_ohlcv
    df = await loop.run_in_executor(
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Loop\financial_agent\financial_agent\agents\data_agent.py", line 267, in <lambda>
    lambda: ticker.history(
            ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\base.py", line 96, in history
    return self._lazy_load_price_history().history(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\yfinance\utils.py", line 99, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
TypeError: PriceHistory.history() got an unexpected keyword argument 'progress'
2025-06-12 15:53:16,319 - __main__ - WARNING - No intraday data returned for AAPL
2025-06-12 15:53:16,319 - __main__ - INFO - 
Demo completed successfully!
2025-06-12 15:53:16,319 - __main__ - INFO - Stopping DataCollectionAgent...
2025-06-12 15:53:16,319 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 15:53:16,319 - __main__ - INFO - DataCollectionAgent has been stopped.
2025-06-12 15:53:43,053 - __main__ - INFO - Initializing DataCollectionAgent with mock LLM...
2025-06-12 15:53:43,053 - agent.data - INFO - Initialized data agent
2025-06-12 15:53:43,053 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 15:53:43,058 - __main__ - INFO - Starting agent...
2025-06-12 15:53:43,058 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 15:53:43,058 - __main__ - INFO - 
==================================================
2025-06-12 15:53:43,058 - __main__ - INFO - EXAMPLE 1: FETCHING DAILY DATA
2025-06-12 15:53:43,058 - __main__ - INFO - ==================================================
2025-06-12 15:53:43,058 - __main__ - INFO - ==================================================
2025-06-12 15:53:43,058 - __main__ - INFO - FETCHING 1D DATA FOR AAPL, MSFT, GOOGL
2025-06-12 15:53:43,058 - __main__ - INFO - Time period: 1mo
2025-06-12 15:53:43,058 - __main__ - INFO - ==================================================
2025-06-12 15:53:43,058 - __main__ - INFO - 
Fetching AAPL data...
2025-06-12 15:53:43,621 - __main__ - ERROR -   Error fetching data for AAPL: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:53:43,623 - __main__ - INFO - 
Fetching MSFT data...
2025-06-12 15:53:43,923 - __main__ - ERROR -   Error fetching data for MSFT: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:53:43,923 - __main__ - INFO - 
Fetching GOOGL data...
2025-06-12 15:53:44,233 - __main__ - ERROR -   Error fetching data for GOOGL: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:53:44,234 - __main__ - WARNING - No data returned for any symbols
2025-06-12 15:53:44,235 - __main__ - INFO - 
==================================================
2025-06-12 15:53:44,235 - __main__ - INFO - EXAMPLE 2: MARKET HOURS INFORMATION
2025-06-12 15:53:44,235 - __main__ - INFO - ==================================================
2025-06-12 15:53:44,237 - __main__ - ERROR - Error getting market hours: 'datetime.datetime' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 124, in main
    logger.info(f"  Market Open: {datetime.fromtimestamp(market_open).strftime('%Y-%m-%d %H:%M:%S')}")
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'datetime.datetime' object cannot be interpreted as an integer
2025-06-12 15:53:44,238 - __main__ - INFO - 
==================================================
2025-06-12 15:53:44,238 - __main__ - INFO - EXAMPLE 3: INTRADAY DATA
2025-06-12 15:53:44,238 - __main__ - INFO - ==================================================
2025-06-12 15:53:44,238 - __main__ - INFO - 
Fetching hourly data for AAPL from 2025-06-09 to 2025-06-12...
2025-06-12 15:53:44,466 - __main__ - ERROR - Error in Example 3: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 146, in main
    if aapl_data and hasattr(aapl_data, 'timestamp') and aapl_data.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:53:44,467 - __main__ - INFO - 
Demo completed successfully!
2025-06-12 15:53:44,467 - __main__ - INFO - Stopping DataCollectionAgent...
2025-06-12 15:53:44,467 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 15:53:44,467 - __main__ - INFO - DataCollectionAgent has been stopped.
2025-06-12 15:55:24,492 - __main__ - INFO - Initializing DataCollectionAgent with mock LLM...
2025-06-12 15:55:24,493 - agent.data - INFO - Initialized data agent
2025-06-12 15:55:24,494 - financial_agent.agents.data_agent - INFO - Initialized DataCollectionAgent with yfinance
2025-06-12 15:55:24,494 - __main__ - INFO - Starting agent...
2025-06-12 15:55:24,494 - financial_agent.agents.data_agent - INFO - Started data agent
2025-06-12 15:55:24,494 - __main__ - INFO - 
==================================================
2025-06-12 15:55:24,494 - __main__ - INFO - EXAMPLE 1: FETCHING DAILY DATA
2025-06-12 15:55:24,494 - __main__ - INFO - ==================================================
2025-06-12 15:55:24,494 - __main__ - INFO - ==================================================
2025-06-12 15:55:24,494 - __main__ - INFO - FETCHING 1D DATA FOR AAPL, MSFT, GOOGL
2025-06-12 15:55:24,494 - __main__ - INFO - Time period: 1mo
2025-06-12 15:55:24,494 - __main__ - INFO - ==================================================
2025-06-12 15:55:24,494 - __main__ - INFO - 
Fetching AAPL data...
2025-06-12 15:55:24,494 - financial_agent.agents.data_agent - INFO - Fetching data for AAPL (attempt 1/3)...
2025-06-12 15:55:25,160 - financial_agent.agents.data_agent - INFO - Successfully fetched data for AAPL
2025-06-12 15:55:25,160 - __main__ - ERROR -   Error fetching data for AAPL: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:55:25,163 - __main__ - INFO - 
Fetching MSFT data...
2025-06-12 15:55:25,163 - financial_agent.agents.data_agent - INFO - Fetching data for MSFT (attempt 1/3)...
2025-06-12 15:55:25,343 - financial_agent.agents.data_agent - INFO - Successfully fetched data for MSFT
2025-06-12 15:55:25,344 - __main__ - ERROR -   Error fetching data for MSFT: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:55:25,347 - __main__ - INFO - 
Fetching GOOGL data...
2025-06-12 15:55:25,348 - financial_agent.agents.data_agent - INFO - Fetching data for GOOGL (attempt 1/3)...
2025-06-12 15:55:25,482 - financial_agent.agents.data_agent - INFO - Successfully fetched data for GOOGL
2025-06-12 15:55:25,482 - __main__ - ERROR -   Error fetching data for GOOGL: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 55, in fetch_market_data
    if ohlcv and hasattr(ohlcv, 'timestamp') and ohlcv.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:55:25,482 - __main__ - WARNING - No data returned for any symbols
2025-06-12 15:55:25,484 - __main__ - INFO - 
==================================================
2025-06-12 15:55:25,484 - __main__ - INFO - EXAMPLE 2: MARKET HOURS INFORMATION
2025-06-12 15:55:25,484 - __main__ - INFO - ==================================================
2025-06-12 15:55:25,484 - __main__ - ERROR - Error getting market hours: 'datetime.datetime' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 124, in main
    logger.info(f"  Market Open: {datetime.fromtimestamp(market_open).strftime('%Y-%m-%d %H:%M:%S')}")
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'datetime.datetime' object cannot be interpreted as an integer
2025-06-12 15:55:25,484 - __main__ - INFO - 
==================================================
2025-06-12 15:55:25,484 - __main__ - INFO - EXAMPLE 3: INTRADAY DATA
2025-06-12 15:55:25,484 - __main__ - INFO - ==================================================
2025-06-12 15:55:25,484 - __main__ - INFO - 
Fetching hourly data for AAPL from 2025-06-09 to 2025-06-12...
2025-06-12 15:55:25,484 - financial_agent.agents.data_agent - INFO - Fetching data for AAPL (attempt 1/3)...
2025-06-12 15:55:25,670 - financial_agent.agents.data_agent - INFO - Successfully fetched data for AAPL
2025-06-12 15:55:25,671 - __main__ - ERROR - Error in Example 3: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
Traceback (most recent call last):
  File "D:\Loop\financial_agent\examples\data_collection_demo.py", line 146, in main
    if aapl_data and hasattr(aapl_data, 'timestamp') and aapl_data.timestamp:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\pandas\core\indexes\base.py", line 3170, in __nonzero__
    raise ValueError(
ValueError: The truth value of a Index is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-12 15:55:25,672 - __main__ - INFO - 
Demo completed successfully!
2025-06-12 15:55:25,672 - __main__ - INFO - Stopping DataCollectionAgent...
2025-06-12 15:55:25,673 - financial_agent.agents.data_agent - INFO - Stopped data agent
2025-06-12 15:55:25,673 - __main__ - INFO - DataCollectionAgent has been stopped.
