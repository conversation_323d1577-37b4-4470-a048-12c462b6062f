from setuptools import setup, find_packages

setup(
    name="financial_agent",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        'yfinance>=0.2.3',
        'pandas>=1.5.0',
        'numpy>=1.24.0',
        'pytz>=2021.3',
        'python-dateutil>=2.8.2',
        'pytest>=7.3.1',
        'pytest-asyncio>=0.20.0',
        'pytest-cov>=4.0.0',
    ],
    python_requires='>=3.8',
    author="Financial Agent Team",
    author_email="",
    description="A financial agent system with data collection, analysis, and trading capabilities",
    keywords="finance trading ai agent",
)
