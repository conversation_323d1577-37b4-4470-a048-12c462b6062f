{"name": "@gradio/column", "version": "0.2.0", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "main_changeset": true, "private": false, "main": "./Index.svelte", "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./package.json": "./package.json"}, "devDependencies": {"@gradio/preview": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/utils": "workspace:^"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/column"}}