{"name": "@gradio/annotatedimage", "version": "0.9.19", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "main_changeset": true, "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./package.json": "./package.json"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "peerDependencies": {"svelte": "^4.0.0"}, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/client": "workspace:^", "@gradio/wasm": "workspace:^"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/annotatedimage"}}