{"name": "@gradio/file", "version": "0.12.18", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/client": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "main": "./Index.svelte", "main_changeset": true, "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./example": {"gradio": "./Example.svelte", "svelte": "./dist/Example.svelte", "types": "./dist/Example.svelte.d.ts"}, "./package.json": "./package.json"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/file"}}