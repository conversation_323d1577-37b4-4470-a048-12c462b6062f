/**
 * Dashboard JavaScript for Financial Agent
 * Handles WebSocket communication and UI updates
 */

// WebSocket connection
let socket;
let performanceChart;
let allocationChart;
let lastUpdateTime = new Date();

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Format percentage
function formatPercent(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
}

// Initialize WebSocket connection
function connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    socket = new WebSocket(wsUrl);
    
    socket.onopen = function() {
        console.log('WebSocket connection established');
        // Request initial data
        socket.send(JSON.stringify({type: 'get_initial_data'}));
    };
    
    socket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('WebSocket message received:', data);
            
            if (data.type === 'update') {
                updateDashboard(data.data);
            } else if (data.type === 'initial_data') {
                initializeDashboard(data.data);
            }
        } catch (e) {
            console.error('Error processing WebSocket message:', e);
        }
    };
    
    socket.onclose = function() {
        console.log('WebSocket connection closed');
        // Try to reconnect after 5 seconds
        setTimeout(connectWebSocket, 5000);
    };
    
    socket.onerror = function(error) {
        console.error('WebSocket error:', error);
    };
}

// Update last updated time
function updateLastUpdated() {
    const now = new Date();
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };
    document.getElementById('last-updated').textContent = now.toLocaleString('en-US', options);
    lastUpdateTime = now;
}

// Update dashboard with new data
function updateDashboard(data) {
    if (!data) return;
    
    // Update portfolio summary
    if (data.portfolio) {
        document.getElementById('portfolio-value').textContent = formatCurrency(data.portfolio.current_value);
        document.getElementById('daily-change').textContent = formatCurrency(data.portfolio.performance.daily * data.portfolio.current_value);
        document.getElementById('daily-change-pct').textContent = formatPercent(data.portfolio.performance.daily);
        document.getElementById('all-time-change').textContent = formatCurrency(data.portfolio.performance.all_time * data.portfolio.current_value);
        document.getElementById('all-time-change-pct').textContent = formatPercent(data.portfolio.performance.all_time);
        
        // Update positions table
        if (data.portfolio.positions) {
            updatePositionsTable(data.portfolio.positions, data.portfolio.current_value);
        }
        
        // Update charts
        updatePerformanceChart(data.portfolio);
        updateAllocationChart(data.portfolio);
    }
    
    // Update risk metrics
    if (data.risk) {
        document.getElementById('volatility').textContent = formatPercent(data.risk.volatility);
        document.getElementById('sharpe-ratio').textContent = data.risk.sharpe_ratio.toFixed(2);
        document.getElementById('max-drawdown').textContent = formatPercent(data.risk.max_drawdown);
        document.getElementById('beta').textContent = data.risk.beta.toFixed(2);
        document.getElementById('alpha').textContent = formatPercent(data.risk.alpha);
    }
    
    // Update trades table
    if (data.trades) {
        updateTradesTable(data.trades);
    }
    
    updateLastUpdated();
}

// Initialize dashboard with initial data
function initializeDashboard(data) {
    // Initialize charts
    initPerformanceChart();
    initAllocationChart();
    
    // Update with initial data
    updateDashboard(data);
}

// Get risk level based on drawdown
function getRiskLevel(drawdown) {
    if (drawdown < 0.02) return 'Low';
    if (drawdown < 0.05) return 'Moderate';
    if (drawdown < 0.1) return 'High';
    return 'Extreme';
}

// Update positions table
function updatePositionsTable(positions, portfolioValue) {
    const tbody = document.querySelector('#positions-table tbody');
    if (!tbody) return;
    
    // Clear existing rows
    tbody.innerHTML = '';
    
    // Add positions
    Object.values(positions).forEach(position => {
        const row = document.createElement('tr');
        const weight = (position.value / portfolioValue) || 0;
        
        row.innerHTML = `
            <td class="font-medium text-gray-900">${position.symbol}</td>
            <td>${position.quantity.toFixed(2)}</td>
            <td>${formatCurrency(position.entry_price)}</td>
            <td>${formatCurrency(position.current_price)}</td>
            <td class="font-medium ${position.pnl >= 0 ? 'text-green-600' : 'text-red-600'}">
                ${formatCurrency(position.pnl)} (${formatPercent(position.pnl_pct / 100)})
            </td>
            <td>${formatPercent(weight)}</td>
            <td>${position.sector || 'N/A'}</td>
            <td>${getRiskLevel(position.beta || 0)}</td>
        `;
        
        tbody.appendChild(row);
    });
}

// Initialize performance chart
function initPerformanceChart() {
    const ctx = document.getElementById('performance-chart');
    if (!ctx) return;
    
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Portfolio Value',
                data: [],
                borderColor: 'rgb(79, 70, 229)',
                tension: 0.1,
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `$${context.parsed.y.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString('en-US');
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Update performance chart
function updatePerformanceChart(portfolio) {
    if (!performanceChart || !portfolio.performance_history) return;
    
    const labels = portfolio.performance_history.map(entry => new Date(entry.timestamp).toLocaleDateString());
    const values = portfolio.performance_history.map(entry => entry.value);
    
    performanceChart.data.labels = labels;
    performanceChart.data.datasets[0].data = values;
    performanceChart.update();
}

// Initialize allocation chart
function initAllocationChart() {
    const ctx = document.getElementById('allocation-chart');
    if (!ctx) return;
    
    allocationChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    'rgba(79, 70, 229, 0.8)',
                    'rgba(99, 102, 241, 0.8)',
                    'rgba(129, 140, 248, 0.8)',
                    'rgba(165, 180, 252, 0.8)',
                    'rgba(199, 210, 254, 0.8)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${percentage}%`;
                        }
                    }
                }
            }
        }
    });
    
    // Initialize legend
    updateAllocationLegend(allocationChart);
}

// Update allocation chart
function updateAllocationChart(portfolio) {
    if (!allocationChart || !portfolio.positions) return;
    
    const positions = Object.values(portfolio.positions);
    if (positions.length === 0) return;
    
    // Group by sector
    const sectors = {};
    positions.forEach(position => {
        const sector = position.sector || 'Other';
        if (!sectors[sector]) {
            sectors[sector] = 0;
        }
        sectors[sector] += position.value || 0;
    });
    
    // Convert to arrays for chart
    const labels = Object.keys(sectors);
    const data = Object.values(sectors);
    
    // Sort by value (descending)
    const sortedIndices = Array.from({length: labels.length}, (_, i) => i)
        .sort((a, b) => data[b] - data[a]);
    
    const sortedLabels = sortedIndices.map(i => labels[i]);
    const sortedData = sortedIndices.map(i => data[i]);
    
    // Update chart
    allocationChart.data.labels = sortedLabels;
    allocationChart.data.datasets[0].data = sortedData;
    allocationChart.update();
    
    // Update legend
    updateAllocationLegend(allocationChart);
}

// Update allocation chart legend
function updateAllocationLegend(chart) {
    const legendContainer = document.getElementById('allocation-legend');
    if (!legendContainer) return;
    
    const ul = legendContainer.querySelector('ul');
    if (!ul) return;
    
    // Clear existing legend items
    ul.innerHTML = '';
    
    if (!chart.data.labels || chart.data.labels.length === 0) {
        const li = document.createElement('li');
        li.className = 'text-sm text-gray-500';
        li.textContent = 'No allocation data available';
        ul.appendChild(li);
        return;
    }
    
    const total = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
    
    chart.data.labels.forEach((label, i) => {
        const value = chart.data.datasets[0].data[i];
        const percentage = Math.round((value / total) * 100) || 0;
        
        const li = document.createElement('li');
        li.className = 'flex items-center justify-between py-1 text-sm';
        li.innerHTML = `
            <div class="flex items-center">
                <span class="inline-block w-3 h-3 mr-2 rounded-full" style="background-color: ${chart.data.datasets[0].backgroundColor[i]}"></span>
                <span>${label}</span>
            </div>
            <span>${percentage}%</span>
        `;
        
        ul.appendChild(li);
    });
}

// Update trades table
function updateTradesTable(trades) {
    const tbody = document.querySelector('#trades-table tbody');
    if (!tbody) return;
    
    // Clear existing rows
    tbody.innerHTML = '';
    
    if (!trades || trades.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="6" class="px-6 py-4 text-sm text-center text-gray-500">No trades yet</td>';
        tbody.appendChild(tr);
        return;
    }
    
    // Add trades
    trades.forEach(trade => {
        const tr = document.createElement('tr');
        tr.className = 'hover:bg-gray-50';
        
        const typeClass = trade.type.toLowerCase() === 'buy' ? 'text-green-600' : 'text-red-600';
        
        tr.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${new Date(trade.timestamp).toLocaleString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${trade.symbol}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm ${typeClass}">
                ${trade.type.toUpperCase()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${trade.quantity.toFixed(2)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatCurrency(trade.price)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatCurrency(trade.quantity * trade.price)}
            </td>
        `;
        
        tbody.appendChild(tr);
    });
}

// Initialize the dashboard when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Connect to WebSocket
    connectWebSocket();
    
    // Update last updated time
    updateLastUpdated();
    
    // Set up auto-refresh every 5 seconds
    setInterval(updateLastUpdated, 5000);
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
