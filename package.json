{"name": "loop-frontend", "version": "1.0.0", "description": "Loop AI Scientist Frontend - Autonomous Compression Research Interface", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "react-hot-toast": "^2.4.1", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "prismjs": "^1.29.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "date-fns": "^2.30.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "14.0.4"}, "engines": {"node": ">=18.0.0"}}