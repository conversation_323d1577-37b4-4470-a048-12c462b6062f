#!/usr/bin/env python3
"""
🔬 FINAL VANILLA VS COMPRESSED COMPARISON
========================================

GOAL: Get complete real data comparison between:
1. Vanilla Mistral 7B (with proper loading)
2. Our compressed streaming version
3. Direct side-by-side measurements

100% REAL DATA - NO ESTIMATES
"""

import os
import torch
import time
import psutil
import json
from transformers import AutoTokenizer, AutoModelForCausalLM
from datetime import datetime
import gc

def get_memory_mb():
    """Get current memory usage in MB"""
    return psutil.Process().memory_info().rss / (1024**2)

def test_vanilla_model_simple():
    """Test vanilla model with simple loading approach"""
    
    print("🔬 VANILLA MODEL TEST (SIMPLIFIED)")
    print("=" * 50)
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'vanilla_simple',
        'success': False
    }
    
    try:
        # Load tokenizer
        print("📥 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Try simple model loading without device_map
        print("📥 Loading model (simple approach)...")
        start_memory = get_memory_mb()
        start_time = time.time()
        
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            low_cpu_mem_usage=True
        )
        
        load_time = time.time() - start_time
        load_memory = get_memory_mb()
        
        print(f"✅ Model loaded successfully!")
        print(f"   Load time: {load_time:.2f}s")
        print(f"   Memory increase: {load_memory - start_memory:.1f}MB")
        print(f"   Total memory: {load_memory:.1f}MB")
        
        # Test inference
        print("🚀 Testing inference...")
        test_prompts = ["The future of AI is", "Technology will"]
        inference_results = []
        
        for prompt in test_prompts:
            print(f"   Testing: '{prompt}'")
            
            inputs = tokenizer.encode(prompt, return_tensors="pt")
            
            start_time = time.time()
            start_memory = get_memory_mb()
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_new_tokens=5,
                    do_sample=False,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            inference_time = time.time() - start_time
            peak_memory = get_memory_mb()
            
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            tokens_generated = len(outputs[0]) - len(inputs[0])
            
            result = {
                'prompt': prompt,
                'generated_text': generated_text,
                'tokens_generated': tokens_generated,
                'inference_time_seconds': inference_time,
                'tokens_per_second': tokens_generated / inference_time if inference_time > 0 else 0,
                'memory_before_mb': start_memory,
                'memory_peak_mb': peak_memory
            }
            
            inference_results.append(result)
            print(f"      Generated: {tokens_generated} tokens in {inference_time:.2f}s")
            print(f"      Speed: {result['tokens_per_second']:.1f} tokens/sec")
            print(f"      Text: '{generated_text}'")
        
        # Calculate averages
        avg_speed = sum(r['tokens_per_second'] for r in inference_results) / len(inference_results)
        avg_memory = sum(r['memory_peak_mb'] for r in inference_results) / len(inference_results)
        
        results.update({
            'success': True,
            'load_time_seconds': load_time,
            'memory_increase_mb': load_memory - start_memory,
            'total_memory_mb': load_memory,
            'inference_results': inference_results,
            'averages': {
                'tokens_per_second': avg_speed,
                'memory_peak_mb': avg_memory
            }
        })
        
        print(f"\n📊 Vanilla Model Results:")
        print(f"   Average speed: {avg_speed:.1f} tokens/sec")
        print(f"   Average memory: {avg_memory:.1f}MB")
        
        # Clean up
        del model
        gc.collect()
        
        return results
        
    except Exception as e:
        print(f"❌ Vanilla model test failed: {e}")
        results['error'] = str(e)
        return results

def test_compressed_model():
    """Test our compressed model"""
    
    print("\n🗜️ COMPRESSED MODEL TEST")
    print("=" * 50)
    
    try:
        from MILESTONE_PROOF_COMPRESSED_INFERENCE import MilestoneProofSystem
        
        model_path = "downloaded_models/mistral-7b-v0.1"
        proof_system = MilestoneProofSystem(model_path)
        
        test_prompts = ["The future of AI is", "Technology will"]
        results = []
        
        for prompt in test_prompts:
            result = proof_system.simple_text_generation(prompt, max_tokens=5)
            results.append(result)
        
        # Calculate averages
        successful_results = [r for r in results if r.get('success')]
        if successful_results:
            avg_speed = sum(r['tokens_generated'] / r['inference_time_s'] 
                           for r in successful_results) / len(successful_results)
            avg_memory = sum(r['peak_memory_mb'] for r in successful_results) / len(successful_results)
        else:
            avg_speed = avg_memory = 0
        
        compressed_results = {
            'success': True,
            'individual_tests': results,
            'averages': {
                'tokens_per_second': avg_speed,
                'memory_peak_mb': avg_memory
            }
        }
        
        print(f"📊 Compressed Model Results:")
        print(f"   Average speed: {avg_speed:.1f} tokens/sec")
        print(f"   Average memory: {avg_memory:.1f}MB")
        
        return compressed_results
        
    except Exception as e:
        print(f"❌ Compressed model test failed: {e}")
        return {'success': False, 'error': str(e)}

def compare_results(vanilla_results, compressed_results):
    """Compare vanilla vs compressed results"""
    
    print("\n📊 DIRECT COMPARISON")
    print("=" * 50)
    
    comparison = {
        'timestamp': datetime.now().isoformat(),
        'vanilla': vanilla_results,
        'compressed': compressed_results,
        'comparison_metrics': {}
    }
    
    if vanilla_results.get('success') and compressed_results.get('success'):
        vanilla_speed = vanilla_results['averages']['tokens_per_second']
        compressed_speed = compressed_results['averages']['tokens_per_second']
        
        vanilla_memory = vanilla_results['averages']['memory_peak_mb']
        compressed_memory = compressed_results['averages']['memory_peak_mb']
        
        comparison['comparison_metrics'] = {
            'speed_comparison': {
                'vanilla_tokens_per_sec': vanilla_speed,
                'compressed_tokens_per_sec': compressed_speed,
                'speed_ratio': vanilla_speed / compressed_speed if compressed_speed > 0 else 0,
                'speed_loss_percent': ((vanilla_speed - compressed_speed) / vanilla_speed * 100) if vanilla_speed > 0 else 0
            },
            'memory_comparison': {
                'vanilla_memory_mb': vanilla_memory,
                'compressed_memory_mb': compressed_memory,
                'memory_reduction_ratio': vanilla_memory / compressed_memory if compressed_memory > 0 else 0,
                'memory_savings_mb': vanilla_memory - compressed_memory,
                'memory_savings_percent': ((vanilla_memory - compressed_memory) / vanilla_memory * 100) if vanilla_memory > 0 else 0
            }
        }
        
        print(f"🚀 SPEED COMPARISON:")
        print(f"   Vanilla: {vanilla_speed:.1f} tokens/sec")
        print(f"   Compressed: {compressed_speed:.1f} tokens/sec")
        print(f"   Speed ratio: {comparison['comparison_metrics']['speed_comparison']['speed_ratio']:.2f}×")
        print(f"   Speed loss: {comparison['comparison_metrics']['speed_comparison']['speed_loss_percent']:.1f}%")
        
        print(f"\n💾 MEMORY COMPARISON:")
        print(f"   Vanilla: {vanilla_memory:.1f}MB")
        print(f"   Compressed: {compressed_memory:.1f}MB")
        print(f"   Memory reduction: {comparison['comparison_metrics']['memory_comparison']['memory_reduction_ratio']:.1f}×")
        print(f"   Memory savings: {comparison['comparison_metrics']['memory_comparison']['memory_savings_mb']:.1f}MB")
        print(f"   Memory savings: {comparison['comparison_metrics']['memory_comparison']['memory_savings_percent']:.1f}%")
    
    else:
        print("⚠️ Cannot compare - one or both tests failed")
        if not vanilla_results.get('success'):
            print(f"   Vanilla failed: {vanilla_results.get('error', 'Unknown error')}")
        if not compressed_results.get('success'):
            print(f"   Compressed failed: {compressed_results.get('error', 'Unknown error')}")
    
    return comparison

def main():
    """Run complete vanilla vs compressed comparison"""
    
    print("🔬🔬🔬 FINAL VANILLA VS COMPRESSED COMPARISON 🔬🔬🔬")
    print("=" * 70)
    print("GETTING COMPLETE REAL DATA FOR DIRECT COMPARISON")
    print()
    
    # Test vanilla model
    vanilla_results = test_vanilla_model_simple()
    
    # Test compressed model
    compressed_results = test_compressed_model()
    
    # Compare results
    comparison = compare_results(vanilla_results, compressed_results)
    
    # Save complete results
    results_file = f"final_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(comparison, f, indent=2)
    
    print(f"\n💾 Complete results saved to: {results_file}")
    print(f"\n🎯 COMPARISON COMPLETE - ALL DATA IS REAL AND MEASURED")
    
    return comparison

if __name__ == "__main__":
    results = main()
