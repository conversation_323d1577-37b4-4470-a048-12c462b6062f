#!/usr/bin/env python3
"""
Memory Monitoring Example - Loop 7B 1-BIT
==========================================

Advanced example showing detailed memory monitoring during
Loop 7B 1-BIT compression and inference operations.

Features:
- Real-time memory tracking
- Memory usage visualization
- Performance profiling
- Memory leak detection
"""

import sys
import os
import psutil
import time
import json
from typing import List, Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loop_1bit_compressor import Loop1BitCompressor

class MemoryMonitor:
    """Advanced memory monitoring for Loop 7B 1-BIT"""
    
    def __init__(self):
        self.measurements = []
        self.start_time = time.time()
        self.baseline_memory = self.get_memory_mb()
        
        print("📊 Memory Monitor initialized")
        print(f"💾 Baseline memory: {self.baseline_memory:.1f}MB")
    
    def get_memory_mb(self) -> float:
        """Get current memory usage in MB"""
        return psutil.Process().memory_info().rss / (1024**2)
    
    def track(self, phase: str, details: str = "") -> float:
        """Track memory usage for a specific phase"""
        current_memory = self.get_memory_mb()
        timestamp = time.time() - self.start_time
        
        measurement = {
            'phase': phase,
            'details': details,
            'memory_mb': current_memory,
            'memory_delta': current_memory - self.baseline_memory,
            'timestamp': timestamp
        }
        
        self.measurements.append(measurement)
        
        print(f"💾 {phase}: {current_memory:.1f}MB (+{measurement['memory_delta']:.1f}MB) {details}")
        
        return current_memory
    
    def get_peak_memory(self) -> float:
        """Get peak memory usage"""
        if not self.measurements:
            return self.baseline_memory
        return max(m['memory_mb'] for m in self.measurements)
    
    def get_memory_growth(self) -> float:
        """Get total memory growth from baseline"""
        current = self.get_memory_mb()
        return current - self.baseline_memory
    
    def detect_memory_leaks(self) -> Dict[str, Any]:
        """Detect potential memory leaks"""
        if len(self.measurements) < 5:
            return {'leak_detected': False, 'reason': 'Insufficient data'}
        
        # Check if memory keeps growing in recent measurements
        recent_measurements = self.measurements[-5:]
        memory_trend = [m['memory_mb'] for m in recent_measurements]
        
        # Simple leak detection: consistent growth
        growing_count = sum(1 for i in range(1, len(memory_trend)) 
                          if memory_trend[i] > memory_trend[i-1])
        
        leak_detected = growing_count >= 3  # 3 out of 4 increases
        
        return {
            'leak_detected': leak_detected,
            'recent_trend': memory_trend,
            'growing_measurements': growing_count,
            'total_growth_mb': memory_trend[-1] - memory_trend[0]
        }
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive memory report"""
        peak_memory = self.get_peak_memory()
        current_memory = self.get_memory_mb()
        total_growth = self.get_memory_growth()
        leak_analysis = self.detect_memory_leaks()
        
        # Calculate phase statistics
        phase_stats = {}
        for measurement in self.measurements:
            phase = measurement['phase']
            if phase not in phase_stats:
                phase_stats[phase] = {
                    'count': 0,
                    'total_memory': 0,
                    'max_memory': 0,
                    'min_memory': float('inf')
                }
            
            stats = phase_stats[phase]
            stats['count'] += 1
            stats['total_memory'] += measurement['memory_mb']
            stats['max_memory'] = max(stats['max_memory'], measurement['memory_mb'])
            stats['min_memory'] = min(stats['min_memory'], measurement['memory_mb'])
        
        # Calculate averages
        for phase, stats in phase_stats.items():
            stats['avg_memory'] = stats['total_memory'] / stats['count']
        
        report = {
            'baseline_memory_mb': self.baseline_memory,
            'peak_memory_mb': peak_memory,
            'current_memory_mb': current_memory,
            'total_growth_mb': total_growth,
            'measurements_count': len(self.measurements),
            'monitoring_duration_s': time.time() - self.start_time,
            'phase_statistics': phase_stats,
            'leak_analysis': leak_analysis,
            'memory_efficiency_score': 1000 / total_growth if total_growth > 0 else float('inf'),
            'target_300mb_achieved': total_growth <= 300
        }
        
        return report
    
    def print_summary(self):
        """Print memory monitoring summary"""
        report = self.generate_report()
        
        print(f"\n📊 MEMORY MONITORING SUMMARY")
        print(f"=" * 40)
        print(f"💾 Baseline: {report['baseline_memory_mb']:.1f}MB")
        print(f"💾 Peak: {report['peak_memory_mb']:.1f}MB")
        print(f"💾 Current: {report['current_memory_mb']:.1f}MB")
        print(f"📈 Total growth: {report['total_growth_mb']:.1f}MB")
        print(f"🎯 300MB target: {'✅ ACHIEVED' if report['target_300mb_achieved'] else '❌ NOT ACHIEVED'}")
        print(f"🔍 Memory leaks: {'⚠️ DETECTED' if report['leak_analysis']['leak_detected'] else '✅ NONE'}")
        print(f"📊 Efficiency score: {report['memory_efficiency_score']:.1f}")
        
        print(f"\n📋 Phase Statistics:")
        for phase, stats in report['phase_statistics'].items():
            print(f"   {phase}: {stats['avg_memory']:.1f}MB avg, {stats['max_memory']:.1f}MB peak")

def monitored_compression_example():
    """Example with detailed memory monitoring during compression"""
    
    print("🔬 Monitored Compression Example")
    print("=" * 40)
    
    model_path = "../downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    # Initialize memory monitor
    monitor = MemoryMonitor()
    
    # Initialize compressor
    monitor.track("initialization", "Creating Loop1BitCompressor")
    compressor = Loop1BitCompressor(model_path)
    
    # Load tokenizer
    monitor.track("tokenizer_start", "Loading tokenizer")
    compressor.load_tokenizer()
    monitor.track("tokenizer_complete", "Tokenizer loaded")
    
    # Load model config
    monitor.track("config_start", "Loading model config")
    compressor.load_model_config()
    monitor.track("config_complete", "Config loaded")
    
    # Compress model
    monitor.track("compression_start", "Starting model compression")
    compression_result = compressor.compress_model()
    monitor.track("compression_complete", f"Compressed {compression_result.get('weights_compressed', 0)} weights")
    
    # Test multiple inferences
    test_prompts = [
        "Hello world",
        "Explain AI",
        "What is quantum computing?"
    ]
    
    for i, prompt in enumerate(test_prompts):
        monitor.track(f"inference_{i+1}_start", f"Starting inference: {prompt[:20]}...")
        response = compressor.generate(prompt, max_tokens=20)
        monitor.track(f"inference_{i+1}_complete", f"Generated {len(response.split())} tokens")
    
    # Final monitoring
    monitor.track("cleanup_start", "Starting cleanup")
    del compressor
    import gc
    gc.collect()
    monitor.track("cleanup_complete", "Cleanup finished")
    
    # Generate and display report
    monitor.print_summary()
    
    # Save detailed report
    report = monitor.generate_report()
    report['measurements'] = monitor.measurements
    
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    filename = f"memory_monitoring_report_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Detailed report saved to {filename}")

def real_time_monitoring_example():
    """Example with real-time memory monitoring"""
    
    print("⚡ Real-Time Memory Monitoring")
    print("=" * 40)
    print("Monitoring memory usage in real-time during operations...")
    
    model_path = "../downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    monitor = MemoryMonitor()
    
    # Real-time monitoring during operations
    def monitor_operation(operation_name: str, operation_func):
        print(f"\n🔄 {operation_name}")
        start_memory = monitor.get_memory_mb()
        start_time = time.time()
        
        # Monitor every 0.5 seconds during operation
        import threading
        monitoring = True
        
        def continuous_monitor():
            counter = 0
            while monitoring:
                counter += 1
                monitor.track(f"{operation_name.lower()}_step_{counter}", f"Step {counter}")
                time.sleep(0.5)
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=continuous_monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # Execute operation
        try:
            result = operation_func()
        finally:
            monitoring = False
            monitor_thread.join(timeout=1)
        
        end_memory = monitor.get_memory_mb()
        end_time = time.time()
        
        print(f"   ✅ Complete: {end_memory - start_memory:+.1f}MB in {end_time - start_time:.1f}s")
        return result
    
    # Initialize with monitoring
    compressor = monitor_operation("Initializing Compressor", 
                                 lambda: Loop1BitCompressor(model_path))
    
    # Load tokenizer with monitoring
    monitor_operation("Loading Tokenizer", 
                     lambda: compressor.load_tokenizer())
    
    # Compress model with monitoring
    compression_result = monitor_operation("Compressing Model", 
                                         lambda: compressor.compress_model())
    
    # Test inference with monitoring
    monitor_operation("Testing Inference", 
                     lambda: compressor.generate("Test prompt", max_tokens=10))
    
    # Final summary
    monitor.print_summary()

def memory_stress_test():
    """Stress test memory usage with multiple operations"""
    
    print("💪 Memory Stress Test")
    print("=" * 30)
    print("Testing memory stability under repeated operations...")
    
    model_path = "../downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    monitor = MemoryMonitor()
    
    # Initialize compressor
    monitor.track("stress_init", "Initializing for stress test")
    compressor = Loop1BitCompressor(model_path)
    compressor.load_tokenizer()
    compressor.compress_model()
    
    # Stress test: multiple inferences
    print(f"\n🔄 Running 10 inference cycles...")
    
    for i in range(10):
        monitor.track(f"stress_cycle_{i+1}", f"Cycle {i+1}/10")
        
        # Multiple prompts per cycle
        prompts = [
            f"Test prompt {i+1}-A",
            f"Test prompt {i+1}-B", 
            f"Test prompt {i+1}-C"
        ]
        
        for j, prompt in enumerate(prompts):
            response = compressor.generate(prompt, max_tokens=15)
            if i == 0:  # Only track detailed steps for first cycle
                monitor.track(f"stress_cycle_{i+1}_prompt_{j+1}", f"Prompt {j+1} complete")
    
    # Check for memory leaks
    leak_analysis = monitor.detect_memory_leaks()
    
    print(f"\n🔍 Stress Test Results:")
    print(f"   Memory leak detected: {'⚠️ YES' if leak_analysis['leak_detected'] else '✅ NO'}")
    print(f"   Total memory growth: {monitor.get_memory_growth():.1f}MB")
    print(f"   Peak memory: {monitor.get_peak_memory():.1f}MB")
    
    if leak_analysis['leak_detected']:
        print(f"   ⚠️ Recent growth: {leak_analysis['total_growth_mb']:.1f}MB")
    
    monitor.print_summary()

def main():
    """Main function with monitoring examples menu"""
    
    print("📊 Loop 7B 1-BIT Memory Monitoring Examples")
    print("=" * 50)
    print("1. Monitored compression example")
    print("2. Real-time monitoring example")
    print("3. Memory stress test")
    print("4. Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-4): ").strip()
            
            if choice == "1":
                monitored_compression_example()
            elif choice == "2":
                real_time_monitoring_example()
            elif choice == "3":
                memory_stress_test()
            elif choice == "4":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-4.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
