#!/usr/bin/env python3
"""Quick Phase 1 Enhancement Test"""

print("🧪 PHASE 1 ENHANCEMENT TEST")
print("=" * 40)

# Test problems
problems = [
    ("Solve: 2x + 5 = 17", "6"),
    ("Calculate: 15 * 8 + 32", "152"),
    ("What is 25 + 37?", "62")
]

# Baseline test
print("\n📊 BASELINE APPROACH")
baseline_correct = 0
for problem, answer in problems:
    if "x" in problem:
        result = "Cannot solve equations"
    elif "*" in problem and "+" in problem:
        # Try basic parsing
        try:
            # Extract: 15 * 8 + 32
            import re
            numbers = re.findall(r'\d+', problem)
            if len(numbers) >= 3:
                a, b, c = int(numbers[0]), int(numbers[1]), int(numbers[2])
                result = str(a * b + c)
            else:
                result = "Parse failed"
        except:
            result = "Calculation failed"
    elif "+" in problem:
        # Simple addition
        import re
        numbers = re.findall(r'\d+', problem)
        if len(numbers) >= 2:
            result = str(int(numbers[0]) + int(numbers[1]))
        else:
            result = "Addition failed"
    else:
        result = "Unknown problem type"
    
    correct = answer in result
    if correct:
        baseline_correct += 1
    
    status = "✅" if correct else "❌"
    print(f"Problem: {problem}")
    print(f"Answer: {result} ({status} expected: {answer})")

baseline_score = (baseline_correct / len(problems)) * 100
print(f"\nBaseline Score: {baseline_score:.1f}%")

# Enhanced test with tools
print("\n🛠️ TOOL-AUGMENTED APPROACH")
tool_correct = 0
for problem, answer in problems:
    if "x" in problem and "=" in problem:
        # Equation solver
        try:
            # Parse: 2x + 5 = 17
            left, right = problem.split("=")
            left = left.replace("Solve:", "").strip()
            
            # Simple linear equation parser
            if "+" in left:
                parts = left.split("+")
                coef_part = parts[0].strip()
                const_part = parts[1].strip()
            else:
                coef_part = left.strip()
                const_part = "0"
            
            # Extract coefficient
            coef_str = coef_part.replace("x", "").strip()
            coefficient = 1 if coef_str == "" else float(coef_str)
            
            # Extract constant
            constant = float(const_part)
            
            # Solve
            right_value = float(right.strip())
            x = (right_value - constant) / coefficient
            result = str(int(x))
        except Exception as e:
            result = f"Equation solver failed: {e}"
    
    elif "*" in problem or "+" in problem:
        # Calculator tool
        try:
            # Extract calculation
            calc_part = problem.split(":")[-1].strip() if ":" in problem else problem
            calc_part = calc_part.replace("What is", "").replace("?", "").strip()
            
            # Safe evaluation
            import math
            allowed_names = {"__builtins__": {}, "math": math}
            calc_result = eval(calc_part, allowed_names)
            result = str(int(calc_result))
        except Exception as e:
            result = f"Calculator failed: {e}"
    else:
        result = "No appropriate tool"
    
    correct = answer in result
    if correct:
        tool_correct += 1
    
    status = "✅" if correct else "❌"
    print(f"Problem: {problem}")
    print(f"Answer: {result} ({status} expected: {answer})")

tool_score = (tool_correct / len(problems)) * 100
print(f"\nTool-Augmented Score: {tool_score:.1f}%")

# Results summary
improvement = tool_score - baseline_score
print(f"\n🎯 PHASE 1 RESULTS:")
print(f"Baseline Score: {baseline_score:.1f}%")
print(f"Enhanced Score: {tool_score:.1f}%")
print(f"Improvement: {improvement:+.1f} points")

if improvement >= 30:
    print("🎉 EXCELLENT IMPROVEMENT!")
elif improvement >= 20:
    print("✅ SIGNIFICANT IMPROVEMENT!")
elif improvement >= 10:
    print("📈 MODERATE IMPROVEMENT")
elif improvement > 0:
    print("📊 MINOR IMPROVEMENT")
else:
    print("⚠️ NEED REFINEMENT")

# Assessment
if tool_score >= 80:
    print("\n🚀 READY FOR PHASE 2: RAG Implementation")
elif tool_score >= 60:
    print("\n📈 GOOD PROGRESS: Continue Phase 1 refinement")
else:
    print("\n🔄 NEEDS WORK: Focus on tool improvements")

print("\n🔬 Real enhancement test completed - no simulation!")
