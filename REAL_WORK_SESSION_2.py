#!/usr/bin/env python3
"""
REAL WORK SESSION 2
==================

IMPROVING COMPRESSION QUALITY
Based on Session 1 results: 2× compression, 0.58% error, but 142% computation error

GOAL: Implement outlier preservation to reduce computation error
TARGET: Maintain 2× compression, reduce computation error to <10%
"""

import os
import torch
import psutil
import time
import json
from safetensors import safe_open
from datetime import datetime

def log_work_progress(task, status, details):
    """Log real work progress with timestamp"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    log_entry = {
        'timestamp': timestamp,
        'task': task,
        'status': status,
        'details': details,
        'session': 'REAL_WORK_SESSION_2'
    }
    
    print(f"📝 WORK LOG [{timestamp}]: {task} - {status}")
    print(f"   Details: {details}")
    
    # Append to work log file
    try:
        with open('work_progress_log.json', 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
    except:
        pass
    
    return log_entry

def measure_real_ram():
    """Measure actual RAM usage"""
    process = psutil.Process()
    ram_gb = process.memory_info().rss / (1024**3)
    ram_mb = ram_gb * 1024
    
    measurement = {
        'ram_gb': ram_gb,
        'ram_mb': ram_mb,
        'timestamp': time.time(),
        'measurement_type': 'REAL_HARDWARE'
    }
    
    print(f"📊 REAL RAM: {ram_gb:.3f}GB ({ram_mb:.0f}MB)")
    return measurement

def improved_1bit_quantization_with_outliers(tensor, outlier_ratio=0.01):
    """Improved 1-bit quantization with outlier preservation"""
    
    log_work_progress("OUTLIER_QUANTIZATION", "STARTED", 
                     f"Applying outlier-preserving quantization (ratio: {outlier_ratio})")
    
    tensor_f32 = tensor.to(torch.float32)
    
    # Identify outliers (top 1% by magnitude)
    abs_weights = torch.abs(tensor_f32)
    outlier_cutoff = torch.quantile(abs_weights, 1.0 - outlier_ratio)
    
    outlier_mask = abs_weights > outlier_cutoff
    outlier_weights = tensor_f32[outlier_mask]
    normal_weights = tensor_f32[~outlier_mask]
    
    outlier_count = torch.sum(outlier_mask).item()
    total_weights = tensor_f32.numel()
    actual_outlier_ratio = outlier_count / total_weights
    
    log_work_progress("OUTLIER_IDENTIFICATION", "SUCCESS", 
                     f"Found {outlier_count} outliers ({actual_outlier_ratio*100:.2f}%)")
    
    # Quantize normal weights to 1-bit
    if len(normal_weights) > 0:
        normal_mean = torch.mean(normal_weights)
        normal_std = torch.std(normal_weights)
        
        centered_normal = normal_weights - normal_mean
        binary_normal = torch.sign(centered_normal)
        binary_normal_uint8 = ((binary_normal + 1) / 2).to(torch.uint8)
    else:
        normal_mean = 0
        normal_std = 1
        binary_normal_uint8 = torch.tensor([], dtype=torch.uint8)
    
    # Keep outliers in float16 for better precision
    outlier_weights_f16 = outlier_weights.to(torch.float16)
    
    # Calculate compression
    original_size = tensor.numel() * tensor.element_size()
    compressed_size = (
        binary_normal_uint8.numel() * binary_normal_uint8.element_size() +  # 1-bit weights
        outlier_weights_f16.numel() * outlier_weights_f16.element_size() +  # float16 outliers
        outlier_mask.numel() * 1 // 8  # 1 bit per position for mask
    )
    compression_ratio = original_size / compressed_size
    
    log_work_progress("COMPRESSION_CALCULATION", "SUCCESS", 
                     f"Compression: {compression_ratio:.2f}× ({original_size/1024**2:.1f}MB → {compressed_size/1024**2:.1f}MB)")
    
    # Reconstruct for quality assessment
    reconstructed = torch.zeros_like(tensor_f32)
    
    if len(binary_normal_uint8) > 0:
        reconstructed_normal = (binary_normal_uint8.to(torch.float32) * 2 - 1) * normal_std + normal_mean
        reconstructed[~outlier_mask] = reconstructed_normal
    
    reconstructed[outlier_mask] = outlier_weights_f16.to(torch.float32)
    
    # Quality metrics
    mse_error = torch.mean((tensor_f32 - reconstructed) ** 2).item()
    mae_error = torch.mean(torch.abs(tensor_f32 - reconstructed)).item()
    
    tensor_range = torch.max(tensor_f32) - torch.min(tensor_f32)
    relative_error = mae_error / tensor_range.item() if tensor_range > 0 else 0
    
    log_work_progress("QUALITY_ASSESSMENT", "SUCCESS", 
                     f"Weight error: {relative_error*100:.2f}%")
    
    return {
        'method': 'outlier_preserving_1bit',
        'compression_ratio': compression_ratio,
        'outlier_ratio': actual_outlier_ratio,
        'outlier_count': outlier_count,
        'quality_metrics': {
            'mse_error': mse_error,
            'mae_error': mae_error,
            'relative_error_percent': relative_error * 100
        },
        'reconstructed_tensor': reconstructed,
        'compressed_data': {
            'binary_weights': binary_normal_uint8,
            'outlier_weights': outlier_weights_f16,
            'outlier_mask': outlier_mask,
            'normal_mean': normal_mean.item() if isinstance(normal_mean, torch.Tensor) else normal_mean,
            'normal_std': normal_std.item() if isinstance(normal_std, torch.Tensor) else normal_std
        }
    }

def test_improved_compression():
    """Test improved compression with outlier preservation"""
    
    log_work_progress("IMPROVED_COMPRESSION_TEST", "STARTED", "Testing outlier-preserving compression")
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    # Load model index
    index_path = os.path.join(model_path, "model.safetensors.index.json")
    with open(index_path, 'r') as f:
        weight_index = json.load(f)
    
    # Use same test layer as Session 1
    test_layer = "model.layers.0.self_attn.q_proj.weight"
    
    log_work_progress("LAYER_SELECTION", "SUCCESS", f"Using test layer: {test_layer}")
    
    # Measure RAM before loading
    ram_before = measure_real_ram()
    
    # Load the layer
    file_name = weight_index['weight_map'][test_layer]
    file_path = os.path.join(model_path, file_name)
    
    with safe_open(file_path, framework="pt", device="cpu") as f:
        tensor = f.get_tensor(test_layer)
        
        ram_after_load = measure_real_ram()
        
        log_work_progress("LAYER_LOADING", "SUCCESS", 
                         f"Loaded {tensor.shape} tensor")
        
        # Test different outlier ratios
        outlier_ratios = [0.005, 0.01, 0.02]  # 0.5%, 1%, 2%
        
        results = {}
        
        for ratio in outlier_ratios:
            print(f"\n🔬 Testing outlier ratio: {ratio*100:.1f}%")
            
            # Apply improved quantization
            compression_result = improved_1bit_quantization_with_outliers(tensor, ratio)
            
            if compression_result:
                # Test computation quality
                log_work_progress("COMPUTATION_TEST", "STARTED", 
                                 f"Testing computation with {ratio*100:.1f}% outliers")
                
                if tensor.dim() == 2:
                    test_input = torch.randn(1, tensor.shape[1])
                    
                    # Original computation
                    tensor_f32 = tensor.to(torch.float32)
                    original_output = torch.matmul(test_input, tensor_f32.t())
                    
                    # Compressed computation
                    reconstructed = compression_result['reconstructed_tensor']
                    compressed_output = torch.matmul(test_input, reconstructed.t())
                    
                    # Compare outputs
                    output_diff = torch.abs(original_output - compressed_output)
                    max_diff = torch.max(output_diff).item()
                    mean_diff = torch.mean(output_diff).item()
                    
                    original_magnitude = torch.mean(torch.abs(original_output)).item()
                    relative_output_error = mean_diff / original_magnitude if original_magnitude > 0 else 0
                    
                    compression_result['computation_test'] = {
                        'max_output_difference': max_diff,
                        'mean_output_difference': mean_diff,
                        'relative_output_error_percent': relative_output_error * 100,
                        'computation_successful': True
                    }
                    
                    log_work_progress("COMPUTATION_TEST", "SUCCESS", 
                                     f"Output error: {relative_output_error*100:.2f}%")
                
                # Store result
                results[f"outlier_{ratio*100:.1f}pct"] = compression_result
                
                print(f"   Compression: {compression_result['compression_ratio']:.2f}×")
                print(f"   Weight error: {compression_result['quality_metrics']['relative_error_percent']:.2f}%")
                if 'computation_test' in compression_result:
                    print(f"   Computation error: {compression_result['computation_test']['relative_output_error_percent']:.2f}%")
        
        ram_final = measure_real_ram()
        
        # Find best result
        best_method = None
        best_score = 0
        
        for method_name, result in results.items():
            # Score = compression / (1 + computation_error_penalty)
            compression = result['compression_ratio']
            computation_error = result.get('computation_test', {}).get('relative_output_error_percent', 100)
            
            # Heavy penalty for high computation error
            error_penalty = computation_error / 10.0  # 10% error = 1.0 penalty
            score = compression / (1 + error_penalty)
            
            if score > best_score:
                best_score = score
                best_method = method_name
        
        # Compile final results
        final_results = {
            'session': 'REAL_WORK_SESSION_2',
            'test_layer': test_layer,
            'layer_shape': list(tensor.shape),
            'all_results': results,
            'best_method': best_method,
            'best_score': best_score,
            'ram_measurements': {
                'before_load_gb': ram_before['ram_gb'],
                'after_load_gb': ram_after_load['ram_gb'],
                'final_gb': ram_final['ram_gb']
            },
            'test_type': 'REAL_HARDWARE_MEASUREMENT',
            'improvement_over_session_1': {}
        }
        
        # Compare with Session 1 results
        if best_method and best_method in results:
            best_result = results[best_method]
            
            # Session 1 had 142% computation error
            session_1_computation_error = 142.02
            current_computation_error = best_result.get('computation_test', {}).get('relative_output_error_percent', 100)
            
            improvement = session_1_computation_error - current_computation_error
            improvement_ratio = session_1_computation_error / current_computation_error if current_computation_error > 0 else float('inf')
            
            final_results['improvement_over_session_1'] = {
                'session_1_computation_error': session_1_computation_error,
                'session_2_computation_error': current_computation_error,
                'improvement_percentage': improvement,
                'improvement_ratio': improvement_ratio
            }
            
            final_results['best_compression'] = best_result['compression_ratio']
            final_results['best_weight_error'] = best_result['quality_metrics']['relative_error_percent']
            final_results['best_computation_error'] = current_computation_error
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"real_work_session_2_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2, default=str)
        
        log_work_progress("RESULTS_SAVED", "SUCCESS", f"Results saved to {results_file}")
        
        return final_results

def main():
    """Main work session 2 - improve compression quality"""
    
    print("🚀 REAL WORK SESSION 2 - IMPROVING COMPRESSION")
    print("=" * 60)
    print("GOAL: Reduce computation error from 142% to <10%")
    print("METHOD: Outlier-preserving 1-bit quantization")
    print("BASELINE: Session 1 - 2× compression, 0.58% weight error, 142% computation error")
    print()
    
    # Start work log
    log_work_progress("WORK_SESSION_2", "STARTED", "Improving compression quality")
    
    # Test improved compression
    results = test_improved_compression()
    
    if results:
        print(f"\n✅ REAL WORK SESSION 2 COMPLETED")
        print(f"📊 IMPROVEMENT RESULTS:")
        
        if 'best_compression' in results:
            print(f"   Best compression: {results['best_compression']:.2f}×")
            print(f"   Best weight error: {results['best_weight_error']:.2f}%")
            print(f"   Best computation error: {results['best_computation_error']:.2f}%")
            
            if 'improvement_over_session_1' in results:
                improvement = results['improvement_over_session_1']
                print(f"\n📈 IMPROVEMENT OVER SESSION 1:")
                print(f"   Session 1 computation error: {improvement['session_1_computation_error']:.2f}%")
                print(f"   Session 2 computation error: {improvement['session_2_computation_error']:.2f}%")
                print(f"   Improvement: {improvement['improvement_percentage']:.2f}% reduction")
                print(f"   Improvement ratio: {improvement['improvement_ratio']:.1f}× better")
        
        log_work_progress("WORK_SESSION_2", "COMPLETED", "Compression quality improved")
        
        return results
    else:
        print(f"\n❌ WORK SESSION 2 FAILED")
        log_work_progress("WORK_SESSION_2", "FAILED", "Could not improve compression")
        return None

if __name__ == "__main__":
    main()
