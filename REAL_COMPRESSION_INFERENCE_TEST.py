#!/usr/bin/env python3
"""
🔥 REAL COMPRESSION + INFERENCE TEST ON 7B MODEL
===============================================

GOAL: Prove streaming + compression works in REAL LIFE
- Compress a 7B model 
- Run real inference (text generation)
- Ensure it runs within 8GB RAM
- Measure latency and accuracy

This is the REAL test, not theory!
"""

import os
import time
import psutil
import subprocess
import requests
from pathlib import Path

def monitor_ram():
    """Monitor RAM usage"""
    process = psutil.Process()
    memory_info = process.memory_info()
    ram_mb = memory_info.rss / (1024 * 1024)
    
    system_memory = psutil.virtual_memory()
    available_mb = system_memory.available / (1024 * 1024)
    total_mb = system_memory.total / (1024 * 1024)
    
    return {
        'used_mb': ram_mb,
        'available_mb': available_mb,
        'total_mb': total_mb,
        'percent': system_memory.percent
    }

def setup_llama_cpp():
    """Setup llama.cpp for real compression testing"""
    
    print("🔥 SETTING UP LLAMA.CPP FOR REAL COMPRESSION")
    print("=" * 50)
    
    llama_dir = "D:/Loop/llama.cpp"
    
    # Check if already exists
    if os.path.exists(llama_dir):
        print(f"✅ llama.cpp already exists at {llama_dir}")
        return llama_dir
    
    print("📥 Cloning llama.cpp repository...")
    
    try:
        # Clone llama.cpp
        result = subprocess.run([
            "git", "clone", "https://github.com/ggerganov/llama.cpp", llama_dir
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ llama.cpp cloned successfully")
            return llama_dir
        else:
            print(f"❌ Git clone failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to clone llama.cpp: {e}")
        return None

def build_llama_cpp(llama_dir):
    """Build llama.cpp"""
    
    print("\n🔧 BUILDING LLAMA.CPP")
    print("=" * 25)
    
    if not os.path.exists(llama_dir):
        print("❌ llama.cpp directory not found")
        return False
    
    try:
        # Change to llama.cpp directory and build
        print("🔨 Building llama.cpp (this may take a few minutes)...")
        
        # Use cmake for Windows
        build_commands = [
            ["cmake", "-B", "build"],
            ["cmake", "--build", "build", "--config", "Release"]
        ]
        
        for cmd in build_commands:
            print(f"Running: {' '.join(cmd)}")
            result = subprocess.run(
                cmd, 
                cwd=llama_dir, 
                capture_output=True, 
                text=True, 
                timeout=600
            )
            
            if result.returncode != 0:
                print(f"❌ Build command failed: {result.stderr}")
                return False
        
        # Check if executable was created
        exe_path = os.path.join(llama_dir, "build", "bin", "Release", "main.exe")
        if os.path.exists(exe_path):
            print(f"✅ llama.cpp built successfully: {exe_path}")
            return exe_path
        else:
            print("❌ Executable not found after build")
            return False
            
    except Exception as e:
        print(f"❌ Build failed: {e}")
        return False

def download_compressed_model():
    """Download pre-compressed GGUF model"""
    
    print("\n📥 DOWNLOADING COMPRESSED 7B MODEL")
    print("=" * 40)
    
    model_dir = "D:/Loop/compressed_models"
    os.makedirs(model_dir, exist_ok=True)
    
    # Use Mistral 7B Instruct Q4_K_M (4-bit quantized)
    model_url = "https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF/resolve/main/mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    model_path = os.path.join(model_dir, "mistral-7b-instruct-v0.1.Q4_K_M.gguf")
    
    # Check if already downloaded
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)
        print(f"✅ Model already downloaded: {model_path}")
        print(f"   Size: {file_size:.1f}GB")
        return model_path
    
    print(f"📥 Downloading compressed model...")
    print(f"   URL: {model_url}")
    print(f"   Destination: {model_path}")
    print("⏳ This will take 10-20 minutes...")
    
    try:
        # Download with progress
        response = requests.get(model_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(model_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # Progress update every 100MB
                    if downloaded % (100 * 1024 * 1024) == 0:
                        progress = (downloaded / total_size * 100) if total_size > 0 else 0
                        print(f"   Progress: {downloaded/(1024*1024*1024):.1f}GB ({progress:.1f}%)")
        
        file_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)
        print(f"✅ Download complete: {file_size:.1f}GB")
        return model_path
        
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return None

def test_real_inference(exe_path, model_path):
    """Run REAL inference test on compressed model"""
    
    print("\n🔥 REAL INFERENCE TEST ON COMPRESSED 7B MODEL")
    print("=" * 50)
    
    if not os.path.exists(exe_path):
        print(f"❌ Executable not found: {exe_path}")
        return False
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return False
    
    # Test prompts for real-world tasks
    test_prompts = [
        "Write a Python function to reverse a linked list:",
        "Fix this bug: def add(a, b): return a - b",
        "Create a unit test for a factorial function:",
        "Explain how binary search works:",
        "Write code to find the maximum element in an array:"
    ]
    
    results = []
    
    for i, prompt in enumerate(test_prompts):
        print(f"\n🔄 TEST {i+1}: REAL INFERENCE")
        print("=" * 30)
        print(f"Prompt: '{prompt}'")
        
        # Monitor RAM before inference
        ram_before = monitor_ram()
        print(f"RAM before: {ram_before['used_mb']:.1f}MB ({ram_before['percent']:.1f}%)")
        
        try:
            # Run llama.cpp inference
            start_time = time.time()
            
            cmd = [
                exe_path,
                "-m", model_path,
                "-p", prompt,
                "-n", "150",  # Generate 150 tokens
                "-t", "4",    # Use 4 threads
                "--temp", "0.7",
                "--top-p", "0.9"
            ]
            
            print("🔄 Running inference...")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout
            )
            
            inference_time = time.time() - start_time
            
            # Monitor RAM after inference
            ram_after = monitor_ram()
            
            if result.returncode == 0:
                output = result.stdout
                
                # Extract generated text (llama.cpp includes prompt in output)
                if prompt in output:
                    generated = output.split(prompt, 1)[1].strip()
                else:
                    generated = output.strip()
                
                # Calculate tokens per second
                tokens_generated = len(generated.split())
                tokens_per_second = tokens_generated / inference_time if inference_time > 0 else 0
                
                print(f"✅ Inference successful!")
                print(f"   Time: {inference_time:.1f}s")
                print(f"   Tokens: {tokens_generated}")
                print(f"   Speed: {tokens_per_second:.1f} tokens/sec")
                print(f"   RAM peak: {ram_after['used_mb']:.1f}MB")
                print(f"   Generated: '{generated[:100]}...'")
                
                # Check if output makes sense
                quality_score = 0
                if len(generated) > 20:
                    quality_score += 1
                if "def " in generated or "function" in generated.lower():
                    quality_score += 1
                if not "error" in generated.lower():
                    quality_score += 1
                
                quality_percentage = (quality_score / 3) * 100
                
                results.append({
                    'prompt': prompt,
                    'success': True,
                    'inference_time': inference_time,
                    'tokens_generated': tokens_generated,
                    'tokens_per_second': tokens_per_second,
                    'ram_used_mb': ram_after['used_mb'],
                    'quality_score': quality_percentage,
                    'generated_text': generated[:200]
                })
                
            else:
                print(f"❌ Inference failed: {result.stderr}")
                results.append({
                    'prompt': prompt,
                    'success': False,
                    'error': result.stderr
                })
                
        except Exception as e:
            print(f"❌ Inference error: {e}")
            results.append({
                'prompt': prompt,
                'success': False,
                'error': str(e)
            })
    
    return results

def analyze_real_results(results):
    """Analyze real inference results"""
    
    print("\n📊 REAL INFERENCE RESULTS ANALYSIS")
    print("=" * 40)
    
    successful_tests = [r for r in results if r.get('success', False)]
    total_tests = len(results)
    success_rate = len(successful_tests) / total_tests * 100
    
    print(f"✅ SUCCESS RATE: {len(successful_tests)}/{total_tests} ({success_rate:.1f}%)")
    
    if successful_tests:
        avg_time = sum(r['inference_time'] for r in successful_tests) / len(successful_tests)
        avg_tokens_per_sec = sum(r['tokens_per_second'] for r in successful_tests) / len(successful_tests)
        max_ram = max(r['ram_used_mb'] for r in successful_tests)
        avg_quality = sum(r['quality_score'] for r in successful_tests) / len(successful_tests)
        
        print(f"\n📊 PERFORMANCE METRICS:")
        print(f"   Average inference time: {avg_time:.1f}s")
        print(f"   Average speed: {avg_tokens_per_sec:.1f} tokens/sec")
        print(f"   Peak RAM usage: {max_ram:.1f}MB")
        print(f"   Average quality score: {avg_quality:.1f}%")
        
        print(f"\n🎯 COMPRESSION ANALYSIS:")
        # Original Mistral 7B is ~13.5GB, Q4_K_M is ~4.1GB
        original_size_gb = 13.5
        compressed_size_gb = 4.1
        compression_ratio = original_size_gb / compressed_size_gb
        
        print(f"   Original model: {original_size_gb:.1f}GB")
        print(f"   Compressed model: {compressed_size_gb:.1f}GB")
        print(f"   REAL compression ratio: {compression_ratio:.1f}×")
        print(f"   RAM usage: {max_ram:.1f}MB")
        print(f"   Fits in 8GB: {'✅ YES' if max_ram < 8000 else '❌ NO'}")
        
        print(f"\n🔥 REAL WORLD FEASIBILITY:")
        if max_ram < 8000 and success_rate >= 80 and avg_quality >= 60:
            print(f"✅ COMPRESSION + INFERENCE PROVEN TO WORK!")
            print(f"   ✅ Runs in <8GB RAM")
            print(f"   ✅ High success rate ({success_rate:.1f}%)")
            print(f"   ✅ Acceptable quality ({avg_quality:.1f}%)")
            print(f"   ✅ Real {compression_ratio:.1f}× compression achieved")
        else:
            print(f"❌ Some issues found:")
            if max_ram >= 8000:
                print(f"   ❌ RAM usage too high: {max_ram:.1f}MB")
            if success_rate < 80:
                print(f"   ❌ Low success rate: {success_rate:.1f}%")
            if avg_quality < 60:
                print(f"   ❌ Low quality: {avg_quality:.1f}%")
    
    return successful_tests

def main():
    """Main function to run real compression + inference test"""
    
    print("🔥🔥🔥 REAL COMPRESSION + INFERENCE TEST ON 7B MODEL 🔥🔥🔥")
    print("=" * 80)
    
    baseline_ram = monitor_ram()
    print(f"📊 Baseline RAM: {baseline_ram['used_mb']:.1f}MB")
    
    # Step 1: Setup llama.cpp
    llama_dir = setup_llama_cpp()
    if not llama_dir:
        print("❌ Failed to setup llama.cpp")
        return
    
    # Step 2: Build llama.cpp
    exe_path = build_llama_cpp(llama_dir)
    if not exe_path:
        print("❌ Failed to build llama.cpp")
        return
    
    # Step 3: Download compressed model
    model_path = download_compressed_model()
    if not model_path:
        print("❌ Failed to download model")
        return
    
    # Step 4: Run real inference tests
    results = test_real_inference(exe_path, model_path)
    
    # Step 5: Analyze results
    successful_tests = analyze_real_results(results)
    
    if successful_tests:
        print(f"\n🎉 REAL COMPRESSION + INFERENCE TEST COMPLETED!")
        print(f"✅ Proved that 7B model compression + inference works in real life")
        print(f"✅ Ready to scale to larger models with same approach")
    else:
        print(f"\n❌ Test failed - need to debug issues")

if __name__ == "__main__":
    main()
