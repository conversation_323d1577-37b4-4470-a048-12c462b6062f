"""
Execution Agent for placing and managing trades based on strategy signals.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum, auto
import time

from .base_agent import BaseAgent, AgentResponse
from .strategy_agent import TradeSignal, OrderSide, OrderType
from .analysis_agent import AnalysisResult, SignalType, Indicator

# Configure logging
logger = logging.getLogger(__name__)

class OrderSide(Enum):
    """Side of an order (buy/sell)."""
    BUY = auto()
    SELL = auto()

class OrderType(Enum):
    """Type of an order (market/limit/stop)."""
    MARKET = auto()
    LIMIT = auto()
    STOP = auto()
    STOP_LIMIT = auto()
    TRAILING_STOP = auto()

class OrderStatus(Enum):
    """Status of an order."""
    NEW = auto()
    PARTIALLY_FILLED = auto()
    FILLED = auto()
    CANCELED = auto()
    REJECTED = auto()
    EXPIRED = auto()

@dataclass
class Order:
    """Represents a trading order."""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.NEW
    filled_quantity: float = 0.0
    avg_fill_price: Optional[float] = None
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Position:
    """Represents an open position."""
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    side: OrderSide
    unrealized_pnl: float = 0.0
    unrealized_pnl_pct: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    opened_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

class ExecutionAgent(BaseAgent):
    """Agent responsible for executing trades and managing positions."""
    
    def __init__(self, llm_wrapper=None, config: Optional[Dict[str, Any]] = None, name: str = 'execution'):
        """Initialize the ExecutionAgent.
        
        Args:
            llm_wrapper: Optional LLM wrapper for advanced execution logic
            config: Configuration dictionary
            name: Name of the agent (default: 'execution')
        """
        super().__init__(name=name, llm_wrapper=llm_wrapper)
        self.config = config or {}
        self._setup_default_config()
        
        # Initialize state
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.trade_history: List[Dict[str, Any]] = []
        self.account_balance = self.config.get('initial_balance', 100000.0)
        self.available_balance = self.account_balance
        self.leverage = self.config.get('leverage', 1.0)
        self.max_position_size = self.config.get('max_position_size', 0.1)  # 10% of account balance
        self.risk_per_trade = self.config.get('risk_per_trade', 0.01)  # 1% risk per trade
    
    def _setup_default_config(self):
        """Set up default configuration if not provided."""
        defaults = {
            'initial_balance': 100000.0,
            'leverage': 1.0,
            'max_position_size': 0.1,  # 10% of account balance
            'risk_per_trade': 0.01,  # 1% risk per trade
            'slippage': 0.0005,  # 0.05% slippage
            'commission_rate': 0.001,  # 0.1% commission
            'max_open_positions': 10,
            'default_stop_loss_pct': 0.05,  # 5% stop loss
            'default_take_profit_pct': 0.10,  # 10% take profit
            'allow_shorting': True,
            'allow_leverage': False,
            'max_leverage': 2.0,
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
    
    async def start(self):
        """Start the execution agent."""
        if self.is_running:
            logger.warning("Execution agent is already running")
            return
            
        logger.info("Starting execution agent...")
        self.is_running = True
        logger.info(f"Initial account balance: ${self.account_balance:,.2f}")
        logger.info(f"Leverage: {self.leverage}x")
        logger.info(f"Max position size: {self.max_position_size*100:.1f}% of account")
        logger.info(f"Risk per trade: {self.risk_per_trade*100:.1f}% of account")
        logger.info("Execution agent started")
    
    async def stop(self):
        """Stop the execution agent."""
        if not self.is_running:
            logger.warning("Execution agent is not running")
            return
            
        logger.info("Stopping execution agent...")
        
        # Cancel all open orders
        for order_id in list(self.orders.keys()):
            await self.cancel_order(order_id)
        
        self.is_running = False
        logger.info("Execution agent stopped")
    
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """Process trading signals and execute orders.
        
        Args:
            input_data: Dictionary containing 'signals' key with list of TradeSignal objects
            
        Returns:
            AgentResponse containing execution results or error
        """
        if 'signals' not in input_data or not isinstance(input_data['signals'], list):
            return AgentResponse(
                success=False,
                error="Invalid input: 'signals' key with list of TradeSignal objects is required"
            )
        
        try:
            signals = input_data['signals']
            execution_results = []
            
            for signal in signals:
                if not isinstance(signal, TradeSignal):
                    logger.warning(f"Invalid signal type: {type(signal).__name__}")
                    continue
                
                # Execute the trade based on the signal
                result = await self.execute_trade(signal)
                execution_results.append(result)
            
            return AgentResponse(
                success=True,
                data={
                    'executions': execution_results,
                    'timestamp': int(time.time()),
                    'account_balance': self.account_balance,
                    'available_balance': self.available_balance,
                    'open_positions': len(self.positions),
                    'open_orders': len(self.orders)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error in process: {str(e)}", exc_info=True)
            return AgentResponse(
                success=False,
                error=f"Trade execution failed: {str(e)}"
            )
    
    async def execute_trade(self, signal: TradeSignal) -> Dict[str, Any]:
        """Execute a trade based on a trading signal.
        
        Args:
            signal: TradeSignal object containing trade details
            
        Returns:
            Dictionary with execution details
        """
        try:
            logger.info(f"Executing trade: {signal.signal_type.name} {signal.symbol} "
                       f"@ {signal.entry_price:.2f} (SL: {signal.stop_loss:.2f}, TP: {signal.take_profit:.2f})")
            
            # Calculate position size based on risk management
            position_size = await self.calculate_position_size(signal)
            if position_size <= 0:
                return {
                    'success': False,
                    'error': 'Insufficient funds or invalid position size',
                    'symbol': signal.symbol,
                    'signal': signal.signal_type.name,
                    'timestamp': int(time.time())
                }
            
            # Create and place the order
            order = await self.place_order(
                symbol=signal.symbol,
                side=signal.signal_type,
                order_type=signal.order_type,
                quantity=position_size,
                price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                metadata=signal.metadata
            )
            
            # Simulate order execution (in a real system, this would be handled by an exchange API)
            await asyncio.sleep(0.1)  # Simulate network latency
            
            # Update order status to filled
            order.filled_quantity = order.quantity
            order.avg_fill_price = order.price
            order.status = OrderStatus.FILLED
            order.updated_at = time.time()
            
            # Update position
            position = await self.update_position(order)
            
            return {
                'success': True,
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side.name,
                'quantity': order.quantity,
                'price': order.avg_fill_price,
                'position_id': f"{order.symbol}_{int(time.time())}",
                'timestamp': int(time.time()),
                'metadata': order.metadata
            }
            
        except Exception as e:
            logger.error(f"Error executing trade: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'symbol': signal.symbol if signal else 'unknown',
                'timestamp': int(time.time())
            }
    
    async def calculate_position_size(self, signal: TradeSignal) -> float:
        """Calculate position size based on risk management rules.
        
        Args:
            signal: TradeSignal object
            
        Returns:
            Position size in base currency
        """
        # Get current price if not provided
        entry_price = signal.entry_price
        if entry_price <= 0:
            # In a real system, we would fetch the current market price here
            logger.warning(f"Invalid entry price: {entry_price}")
            return 0.0
        
        # Calculate position size based on risk per trade
        risk_amount = self.account_balance * self.risk_per_trade
        stop_loss_pct = abs((entry_price - signal.stop_loss) / entry_price) if signal.stop_loss else self.config['default_stop_loss_pct']
        
        if stop_loss_pct <= 0:
            logger.warning(f"Invalid stop loss percentage: {stop_loss_pct}")
            return 0.0
        
        # Position size in quote currency
        position_size_quote = risk_amount / stop_loss_pct
        
        # Convert to base currency (number of shares/coins)
        position_size = position_size_quote / entry_price
        
        # Apply position size limits
        max_position_value = self.account_balance * self.max_position_size
        max_position_size = max_position_value / entry_price
        
        position_size = min(position_size, max_position_size)
        
        # Apply leverage if allowed
        if self.config['allow_leverage']:
            position_size = min(position_size * self.leverage, max_position_size * self.config['max_leverage'])
        
        # Ensure minimum position size (e.g., 1 share/coin)
        min_position_size = 1.0 / entry_price  # At least 1 share/coin
        if position_size < min_position_size:
            logger.warning(f"Position size too small: {position_size} < {min_position_size}")
            return 0.0
        
        return position_size
    
    async def place_order(
        self,
        symbol: str,
        side: SignalType,
        order_type: OrderType,
        quantity: float,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Order:
        """Place a new order.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTC/USDT')
            side: Order side (BUY/SELL)
            order_type: Order type (MARKET, LIMIT, etc.)
            quantity: Order quantity in base currency
            price: Order price (required for LIMIT orders)
            stop_price: Stop price for STOP orders
            stop_loss: Stop loss price
            take_profit: Take profit price
            metadata: Additional order metadata
            
        Returns:
            Order object
        """
        # Generate a unique order ID (in a real system, this would come from the exchange)
        order_id = f"{symbol.replace('/', '')}_{int(time.time() * 1000)}_{len(self.orders)}"
        
        # Create the order
        order = Order(
            order_id=order_id,
            symbol=symbol,
            side=OrderSide.BUY if side in [SignalType.BUY, SignalType.STRONG_BUY] else OrderSide.SELL,
            order_type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            metadata={
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'strategy': metadata.get('strategy', 'unknown') if metadata else 'unknown',
                'confidence': metadata.get('confidence', 0.0) if metadata else 0.0,
                **(metadata or {})
            }
        )
        
        # Add to orders dictionary
        self.orders[order_id] = order
        
        logger.info(f"Placed {order_type.name} order {order_id}: {side.name} {quantity} {symbol} @ {price}")
        
        return order
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an open order.
        
        Args:
            order_id: ID of the order to cancel
            
        Returns:
            True if the order was successfully canceled, False otherwise
        """
        if order_id not in self.orders:
            logger.warning(f"Order not found: {order_id}")
            return False
        
        order = self.orders[order_id]
        
        # In a real system, we would call the exchange API to cancel the order
        logger.info(f"Canceling order {order_id}: {order.side.name} {order.quantity} {order.symbol}")
        
        # Update order status
        order.status = OrderStatus.CANCELED
        order.updated_at = time.time()
        
        # Remove from active orders
        del self.orders[order_id]
        
        return True
    
    async def update_position(self, order: Order) -> Position:
        """Update or create a position based on a filled order.
        
        Args:
            order: Filled order
            
        Returns:
            Updated or new Position object
        """
        if not order.filled_quantity or not order.avg_fill_price:
            raise ValueError("Cannot update position with unfilled order")
        
        position_id = order.symbol
        
        if position_id in self.positions:
            # Update existing position
            position = self.positions[position_id]
            
            if position.side == order.side:
                # Increase position
                total_quantity = position.quantity + order.filled_quantity
                position.entry_price = (
                    (position.entry_price * position.quantity) + 
                    (order.avg_fill_price * order.filled_quantity)
                ) / total_quantity
                position.quantity = total_quantity
            else:
                # Decrease or close position
                if order.filled_quantity >= position.quantity:
                    # Close position
                    del self.positions[position_id]
                    return position
                else:
                    # Decrease position
                    position.quantity -= order.filled_quantity
        else:
            # Create new position
            position = Position(
                symbol=order.symbol,
                quantity=order.filled_quantity,
                entry_price=order.avg_fill_price,
                current_price=order.avg_fill_price,
                side=order.side,
                stop_loss=order.metadata.get('stop_loss'),
                take_profit=order.metadata.get('take_profit'),
                metadata=order.metadata
            )
            self.positions[position_id] = position
        
        # Update position metadata
        position.updated_at = time.time()
        
        # Update account balance (simplified)
        if order.side == OrderSide.BUY:
            self.available_balance -= order.quantity * (order.avg_fill_price or 0)
        else:  # SELL
            self.available_balance += order.quantity * (order.avg_fill_price or 0)
        
        return position
    
    async def get_open_positions(self) -> Dict[str, Position]:
        """Get all open positions."""
        return self.positions.copy()
    
    async def get_position(self, symbol: str) -> Optional[Position]:
        """Get an open position by symbol."""
        return self.positions.get(symbol)
    
    async def close_position(self, symbol: str, price: Optional[float] = None) -> bool:
        """Close an open position.
        
        Args:
            symbol: Symbol of the position to close
            price: Optional price to close at (for limit orders)
            
        Returns:
            True if the position was closed, False otherwise
        """
        if symbol not in self.positions:
            logger.warning(f"No open position found for {symbol}")
            return False
        
        position = self.positions[symbol]
        
        # In a real system, we would place an order to close the position
        logger.info(f"Closing position: {position.side.name} {position.quantity} {position.symbol} "
                   f"@ {price or 'market'}")
        
        # Calculate P&L (simplified)
        if position.side == OrderSide.BUY:
            pnl = (price - position.entry_price) * position.quantity if price else 0.0
        else:  # SELL
            pnl = (position.entry_price - price) * position.quantity if price else 0.0
        
        # Record trade history
        self.trade_history.append({
            'symbol': position.symbol,
            'side': position.side.name,
            'quantity': position.quantity,
            'entry_price': position.entry_price,
            'exit_price': price,
            'pnl': pnl,
            'pnl_pct': (pnl / (position.entry_price * position.quantity)) * 100 if position.entry_price * position.quantity > 0 else 0.0,
            'duration': time.time() - position.opened_at,
            'timestamp': int(time.time()),
            'metadata': position.metadata
        })
        
        # Update account balance
        self.account_balance += pnl
        self.available_balance += pnl + (position.quantity * (price or position.entry_price))
        
        # Remove position
        del self.positions[symbol]
        
        logger.info(f"Closed position: {position.symbol}, P&L: ${pnl:,.2f}")
        
        return True
