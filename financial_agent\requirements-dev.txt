# Development dependencies
-r requirements.txt
-r requirements-web.txt

# Testing
pytest>=7.3.1
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-xdist>=3.3.1

# Linting and formatting
black>=23.3.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.2.0
pylint>=2.17.0

# Type checking
types-python-dateutil>=2.8.0
types-requests>=2.28.0
types-pyyaml>=6.0.0

# Documentation
sphinx>=6.1.3
sphinx-rtd-theme>=1.2.0
sphinx-autodoc-typehints>=1.22.0

# Pre-commit hooks
pre-commit>=3.2.2

# Development tools
ipython>=8.12.0
jupyter>=1.0.0
jupyterlab>=3.6.0

# Debugging
ipdb>=0.13.13

# Build and packaging
build>=0.10.0
twine>=4.0.2
wheel>=0.40.0
