#!/usr/bin/env python3
"""
QUICK TEST FOR AUTONOMOUS ARCHITECTURE SEARCH
=============================================

Simple test to verify the system is working correctly.
"""

import sys
import traceback

def test_imports():
    """Test all required imports"""
    print("🔧 Testing imports...")
    
    try:
        import torch
        print("✅ PyTorch imported")
        
        import numpy as np
        print("✅ NumPy imported")
        
        import asyncio
        print("✅ AsyncIO imported")
        
        import json
        print("✅ JSON imported")
        
        # Test our modules
        from autonomous_architecture_search import ArchitectureGenome, EvolutionaryOptimizer
        print("✅ Core architecture search imported")
        
        from ai_scientist_components import HypothesisGenerator
        print("✅ AI scientist components imported")
        
        from paper_generator import PaperGenerator
        print("✅ Paper generator imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic system functionality"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        from autonomous_architecture_search import ArchitectureGenome, EvolutionaryOptimizer
        
        # Test genome creation
        genome = ArchitectureGenome()
        print(f"✅ Genome created: {genome.num_layers}L-{genome.hidden_size}H-{genome.num_heads}A")
        
        # Test parameter estimation
        params = genome.get_estimated_parameters()
        memory = genome.get_estimated_memory_gb()
        print(f"✅ Estimates: {params/1e6:.1f}M params, {memory:.2f}GB memory")
        
        # Test optimizer
        config = {
            'population_size': 5,
            'mutation_rate': 0.1,
            'crossover_rate': 0.7,
            'elite_ratio': 0.1
        }
        
        optimizer = EvolutionaryOptimizer(config)
        print("✅ Evolutionary optimizer created")
        
        # Test population initialization
        optimizer.initialize_population()
        print(f"✅ Population initialized: {len(optimizer.population)} individuals")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        traceback.print_exc()
        return False

async def test_ai_scientist():
    """Test AI scientist components"""
    print("\n🧠 Testing AI scientist...")
    
    try:
        from ai_scientist_components import HypothesisGenerator
        from autonomous_architecture_search import ArchitectureGenome
        
        # Test hypothesis generator
        config = {'test': True}
        generator = HypothesisGenerator(config)
        
        # Create test data
        population = [ArchitectureGenome() for _ in range(3)]
        research_history = [{
            'generation': 0,
            'fitness_scores': [0.5, 0.6, 0.7],
            'population': [g.to_dict() for g in population]
        }]
        
        # Generate hypotheses
        hypotheses = await generator.generate_hypotheses(research_history, population)
        print(f"✅ Generated {len(hypotheses)} hypotheses")
        
        return True
        
    except Exception as e:
        print(f"❌ AI scientist test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 AUTONOMOUS ARCHITECTURE SEARCH - QUICK TEST")
    print("=" * 55)
    
    tests = [
        ("Imports", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("AI Scientist", test_ai_scientist)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} test...")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func())
            else:
                result = test_func()
            
            results.append(result)
            
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 TEST SUMMARY")
    print("=" * 30)
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if all(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Autonomous architecture search system is working!")
        print("\n🚀 Ready to run full search:")
        print("   python run_autonomous_architecture_search.py --quick-test")
    else:
        print(f"\n⚠️ {total-passed} tests failed")
        print("🔧 Check the error messages above")
    
    return all(results)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)
