#!/usr/bin/env python3
"""
Test REAL Self-Improvement System
- Honest failure analysis
- Real strategy generation
- Measurable improvement tracking
- No simulated progress
"""

import sys
from pathlib import Path

# Add loop_singular_bit to path
sys.path.append(str(Path(__file__).parent.parent / 'loop_singular_bit'))

try:
    from loop_singular_bit import load_compressed_model
    from loop import LoopAG<PERSON>
    from real_intelligence_benchmarks import RealIntelligenceBenchmarks
    from real_self_improvement import RealSelfImprovement
    
    print("🔄 TESTING REAL SELF-IMPROVEMENT SYSTEM")
    print("=" * 70)
    
    # Initialize Loop AGI system
    print("\n🔧 Initializing Loop AGI with Real Self-Improvement...")
    loop_agi = LoopAGI()
    
    print(f"✅ System initialized with real loop_singular_bit model")
    print(f"📊 Autonomous reasoning active: {loop_agi.autonomous_reasoning_active}")
    print(f"🔁 Core engine: {loop_agi.core_engine}")
    
    # Test 1: Baseline Intelligence Measurement
    print("\n" + "="*50)
    print("📊 TEST 1: BASELINE INTELLIGENCE MEASUREMENT")
    print("="*50)
    
    # Create benchmark system
    benchmarks = RealIntelligenceBenchmarks(loop_agi.loop_singular_bit_model)
    
    # Run baseline benchmark
    print("🧠 Running baseline intelligence benchmark...")
    baseline_result = benchmarks.run_full_benchmark()
    
    print(f"📊 BASELINE RESULTS:")
    print(f"   Overall Score: {baseline_result['overall_score']:.1f}%")
    print(f"   Classification: {baseline_result['classification']}")
    print(f"   Total Problems: {baseline_result['summary']['total_problems']}")
    print(f"   Total Correct: {baseline_result['summary']['total_correct']}")
    print(f"   Accuracy: {baseline_result['summary']['accuracy']:.1f}%")
    
    # Test 2: Failure Analysis
    print("\n" + "="*50)
    print("🔍 TEST 2: REAL FAILURE ANALYSIS")
    print("="*50)
    
    # Create self-improvement system
    self_improvement = RealSelfImprovement(loop_agi)
    
    # Analyze specific failures
    print("📊 Analyzing benchmark failures...")
    failures = self_improvement.analyze_benchmark_failures(baseline_result)
    
    print(f"🔍 FAILURE ANALYSIS RESULTS:")
    for domain, domain_failures in failures.items():
        print(f"   {domain.capitalize()}: {len(domain_failures)} failures")
        if domain_failures:
            print(f"     Example failure: {domain_failures[0].get('problem', domain_failures[0].get('task', domain_failures[0].get('challenge', 'N/A')))}")
    
    total_failures = sum(len(f) for f in failures.values())
    print(f"   Total Failures: {total_failures}")
    
    # Test 3: Improvement Strategy Generation
    print("\n" + "="*50)
    print("🛠️ TEST 3: REAL IMPROVEMENT STRATEGY GENERATION")
    print("="*50)
    
    if total_failures > 0:
        # Create improvement prompts
        print("📝 Creating targeted improvement prompts...")
        improvement_prompts = self_improvement.create_improvement_prompts(failures)
        
        print(f"📋 IMPROVEMENT PROMPTS CREATED:")
        for domain, prompt in improvement_prompts.items():
            print(f"   {domain.capitalize()}: {len(prompt)} characters")
        
        # Generate strategies using Loop_Singular_Bit
        print("\n🧠 Generating improvement strategies with Loop_Singular_Bit...")
        strategies = self_improvement.generate_improvement_strategies(improvement_prompts)
        
        print(f"🎯 IMPROVEMENT STRATEGIES GENERATED:")
        for domain, strategy in strategies.items():
            print(f"   {domain.capitalize()}: {len(strategy)} characters")
            print(f"     Preview: {strategy[:100]}...")
    else:
        print("✅ No failures detected - no improvement strategies needed")
        strategies = {}
    
    # Test 4: Real Self-Improvement Cycle
    print("\n" + "="*50)
    print("🔄 TEST 4: COMPLETE REAL SELF-IMPROVEMENT CYCLE")
    print("="*50)
    
    # Execute complete improvement cycle
    print("🔄 Executing real self-improvement cycle...")
    improvement_result = self_improvement.execute_real_improvement_cycle(baseline_result)
    
    print(f"📈 IMPROVEMENT CYCLE RESULTS:")
    print(f"   Status: {improvement_result['improvement']}")
    if improvement_result['improvement'] == 'completed':
        print(f"   Failures Addressed: {improvement_result['failures_addressed']}")
        print(f"   Strategies Generated: {improvement_result['strategies_generated']}")
        print(f"   Domains Improved: {improvement_result['domains']}")
    elif improvement_result['improvement'] == 'none_needed':
        print(f"   Reason: {improvement_result['reason']}")
    
    # Test 5: Post-Improvement Benchmark
    print("\n" + "="*50)
    print("📊 TEST 5: POST-IMPROVEMENT BENCHMARK")
    print("="*50)
    
    # Run benchmark again to measure improvement
    print("🧠 Running post-improvement intelligence benchmark...")
    post_improvement_result = benchmarks.run_full_benchmark()
    
    print(f"📊 POST-IMPROVEMENT RESULTS:")
    print(f"   Overall Score: {post_improvement_result['overall_score']:.1f}%")
    print(f"   Classification: {post_improvement_result['classification']}")
    print(f"   Total Correct: {post_improvement_result['summary']['total_correct']}")
    print(f"   Accuracy: {post_improvement_result['summary']['accuracy']:.1f}%")
    
    # Test 6: Real Improvement Measurement
    print("\n" + "="*50)
    print("📈 TEST 6: REAL IMPROVEMENT MEASUREMENT")
    print("="*50)
    
    # Measure actual improvement
    improvement_measurement = self_improvement.measure_improvement_effectiveness(
        baseline_result['overall_score'],
        post_improvement_result['overall_score']
    )
    
    print(f"📈 REAL IMPROVEMENT MEASUREMENT:")
    print(f"   Before Score: {improvement_measurement['before_score']:.1f}%")
    print(f"   After Score: {improvement_measurement['after_score']:.1f}%")
    print(f"   Score Improvement: {improvement_measurement['score_improvement']:+.2f} points")
    print(f"   Percentage Improvement: {improvement_measurement['percentage_improvement']:+.1f}%")
    print(f"   Significant Improvement: {'✅ YES' if improvement_measurement['significant_improvement'] else '❌ NO'}")
    
    # Test 7: Enhanced Reasoning Application
    print("\n" + "="*50)
    print("🧠 TEST 7: ENHANCED REASONING APPLICATION")
    print("="*50)
    
    if 'enhanced_prompts' in loop_agi.memory:
        print("🧠 Testing enhanced reasoning on sample problems...")
        
        test_problems = {
            'mathematical': "Solve: 3x + 7 = 22",
            'logical': "If all birds can fly, and penguins are birds, can penguins fly?",
            'language': "Summarize this in 5 words: 'The weather today is sunny and warm'",
            'creative': "List 3 creative uses for a coffee cup"
        }
        
        for domain, problem in test_problems.items():
            if domain in loop_agi.memory['enhanced_prompts']:
                print(f"\n--- {domain.capitalize()} Enhanced Reasoning ---")
                print(f"Problem: {problem}")
                
                try:
                    enhanced_response = self_improvement.apply_enhanced_reasoning(domain, problem)
                    print(f"Enhanced Response: {enhanced_response}")
                    print(f"✅ Enhanced reasoning applied successfully")
                except Exception as e:
                    print(f"⚠️ Enhanced reasoning failed: {e}")
            else:
                print(f"⚠️ No enhanced reasoning available for {domain}")
    else:
        print("⚠️ No enhanced reasoning prompts available")
    
    # Test 8: Memory Integration Check
    print("\n" + "="*50)
    print("💾 TEST 8: MEMORY INTEGRATION CHECK")
    print("="*50)
    
    print("💾 Checking memory integration...")
    
    memory_check = {
        'improvement_strategies': 'improvement_strategies' in loop_agi.memory,
        'reasoning_modules': 'reasoning_modules' in loop_agi.memory,
        'enhanced_prompts': 'enhanced_prompts' in loop_agi.memory,
        'improvement_history': len(self_improvement.improvement_history) > 0
    }
    
    print(f"📊 MEMORY INTEGRATION STATUS:")
    for component, status in memory_check.items():
        print(f"   {component}: {'✅ ACTIVE' if status else '❌ MISSING'}")
    
    if memory_check['improvement_strategies']:
        strategies_count = len(loop_agi.memory['improvement_strategies'])
        print(f"   Improvement Strategies Count: {strategies_count}")
    
    if memory_check['reasoning_modules']:
        modules_count = len(loop_agi.memory['reasoning_modules'])
        print(f"   Reasoning Modules Count: {modules_count}")
    
    # Final Assessment
    print("\n" + "="*70)
    print("🏆 REAL SELF-IMPROVEMENT SYSTEM ASSESSMENT")
    print("="*70)
    
    assessment = {
        'baseline_measured': baseline_result['overall_score'] > 0,
        'failures_analyzed': total_failures > 0,
        'strategies_generated': len(strategies) > 0 if 'strategies' in locals() else False,
        'improvement_cycle_completed': improvement_result['improvement'] == 'completed',
        'post_improvement_measured': post_improvement_result['overall_score'] > 0,
        'real_improvement_detected': improvement_measurement['significant_improvement'],
        'memory_integration_successful': all(memory_check.values())
    }
    
    print("🎯 SYSTEM ASSESSMENT:")
    for component, status in assessment.items():
        print(f"   {component.replace('_', ' ').title()}: {'✅ SUCCESS' if status else '❌ FAILED'}")
    
    success_rate = sum(assessment.values()) / len(assessment) * 100
    print(f"\n📊 Overall Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 REAL SELF-IMPROVEMENT SYSTEM: ✅ OPERATIONAL")
    elif success_rate >= 60:
        print("⚠️ REAL SELF-IMPROVEMENT SYSTEM: 🔄 PARTIALLY OPERATIONAL")
    else:
        print("❌ REAL SELF-IMPROVEMENT SYSTEM: ⚠️ NEEDS IMPROVEMENT")
    
    print(f"\n🔬 All improvements based on real benchmark failures and Loop_Singular_Bit generated strategies!")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
