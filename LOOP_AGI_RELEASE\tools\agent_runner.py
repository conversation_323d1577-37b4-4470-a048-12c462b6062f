#!/usr/bin/env python3
"""
LOOP AGI - Agent Runner Tool
Task execution agent for autonomous operations
"""

import os
import sys
import json
import time
import datetime
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional

class AgentRunner:
    """Task execution agent for LOOP AGI system"""
    
    def __init__(self):
        self.task_history = []
        self.active_tasks = {}
        self.completed_tasks = []
        
    def execute_task(self, task_definition: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a defined task with monitoring"""
        task_id = self._generate_task_id()
        
        task_result = {
            'task_id': task_id,
            'definition': task_definition,
            'start_time': datetime.datetime.now().isoformat(),
            'status': 'running',
            'output': '',
            'error': '',
            'duration': 0.0,
            'success': False
        }
        
        self.active_tasks[task_id] = task_result
        
        try:
            start_time = time.time()
            
            # Execute based on task type
            if task_definition['type'] == 'module_generation':
                result = self._execute_module_generation(task_definition)
            elif task_definition['type'] == 'validation':
                result = self._execute_validation(task_definition)
            elif task_definition['type'] == 'performance_analysis':
                result = self._execute_performance_analysis(task_definition)
            elif task_definition['type'] == 'system_maintenance':
                result = self._execute_system_maintenance(task_definition)
            else:
                result = {'success': False, 'error': f"Unknown task type: {task_definition['type']}"}
            
            task_result['duration'] = time.time() - start_time
            task_result['success'] = result['success']
            task_result['output'] = result.get('output', '')
            task_result['error'] = result.get('error', '')
            task_result['status'] = 'completed' if result['success'] else 'failed'
            
        except Exception as e:
            task_result['error'] = str(e)
            task_result['success'] = False
            task_result['status'] = 'failed'
            task_result['duration'] = time.time() - start_time
        
        # Move from active to completed
        del self.active_tasks[task_id]
        self.completed_tasks.append(task_result)
        self.task_history.append(task_result)
        
        return task_result
    
    def _generate_task_id(self) -> str:
        """Generate unique task identifier"""
        timestamp = int(time.time() * 1000)
        return f"task_{timestamp}"
    
    def _execute_module_generation(self, task_def: Dict[str, Any]) -> Dict[str, Any]:
        """Execute module generation task"""
        try:
            # Import and use self_modify module
            sys.path.append('..')
            from self_modify import SelfModifier
            
            modifier = SelfModifier()
            
            target_weakness = task_def.get('target_weakness', 'general_improvement')
            performance_data = task_def.get('performance_data', {'intelligence': 0.8, 'safety': 0.9, 'efficiency': 0.7})
            
            module_id, module_code = modifier.generate_improvement_module(target_weakness)
            
            # Save the generated module
            metadata = {
                'target_weakness': target_weakness,
                'performance_data': performance_data,
                'generation_method': 'autonomous'
            }
            
            module_path = modifier.save_generated_module(module_id, module_code, metadata)
            
            return {
                'success': True,
                'output': f"Generated module {module_id} at {module_path}",
                'module_id': module_id,
                'module_path': str(module_path)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_validation(self, task_def: Dict[str, Any]) -> Dict[str, Any]:
        """Execute module validation task"""
        try:
            sys.path.append('..')
            from validate import ValidationSuite
            
            validator = ValidationSuite()
            module_path = task_def.get('module_path')
            
            if not module_path or not Path(module_path).exists():
                return {'success': False, 'error': 'Module path not found'}
            
            validation_result = validator.validate_module(module_path)
            
            return {
                'success': True,
                'output': f"Validation completed for {module_path}",
                'validation_result': validation_result
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_performance_analysis(self, task_def: Dict[str, Any]) -> Dict[str, Any]:
        """Execute performance analysis task"""
        try:
            # Read performance data
            performance_file = Path('../benchmarks/performance.csv')
            if not performance_file.exists():
                return {'success': False, 'error': 'Performance data not found'}
            
            with open(performance_file, 'r') as f:
                lines = f.readlines()
            
            if len(lines) < 2:
                return {'success': False, 'error': 'Insufficient performance data'}
            
            # Analyze recent performance
            recent_data = []
            for line in lines[-10:]:  # Last 10 entries
                if line.strip() and not line.startswith('timestamp'):
                    parts = line.strip().split(',')
                    if len(parts) >= 5:
                        recent_data.append({
                            'timestamp': parts[0],
                            'cycle': int(parts[1]),
                            'intelligence': float(parts[2]),
                            'safety': float(parts[3]),
                            'efficiency': float(parts[4])
                        })
            
            # Calculate trends
            if len(recent_data) >= 2:
                intelligence_trend = recent_data[-1]['intelligence'] - recent_data[0]['intelligence']
                safety_trend = recent_data[-1]['safety'] - recent_data[0]['safety']
                efficiency_trend = recent_data[-1]['efficiency'] - recent_data[0]['efficiency']
            else:
                intelligence_trend = safety_trend = efficiency_trend = 0.0
            
            analysis = {
                'total_cycles': len(recent_data),
                'current_scores': recent_data[-1] if recent_data else {},
                'trends': {
                    'intelligence': intelligence_trend,
                    'safety': safety_trend,
                    'efficiency': efficiency_trend
                },
                'recommendations': []
            }
            
            # Generate recommendations
            if intelligence_trend < 0:
                analysis['recommendations'].append("Focus on reasoning improvement modules")
            if safety_trend < 0:
                analysis['recommendations'].append("Enhance safety validation systems")
            if efficiency_trend < 0:
                analysis['recommendations'].append("Implement resource optimization")
            
            return {
                'success': True,
                'output': 'Performance analysis completed',
                'analysis': analysis
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_system_maintenance(self, task_def: Dict[str, Any]) -> Dict[str, Any]:
        """Execute system maintenance task"""
        try:
            maintenance_type = task_def.get('maintenance_type', 'cleanup')
            
            if maintenance_type == 'cleanup':
                # Clean up old log files, temporary files, etc.
                cleaned_files = []
                
                # Clean old quarantine files (older than 7 days)
                quarantine_dir = Path('../modules/quarantine')
                if quarantine_dir.exists():
                    for file in quarantine_dir.glob('*.py'):
                        if file.stat().st_mtime < time.time() - (7 * 24 * 3600):
                            file.unlink()
                            cleaned_files.append(str(file))
                
                return {
                    'success': True,
                    'output': f"Cleanup completed. Removed {len(cleaned_files)} old files",
                    'cleaned_files': cleaned_files
                }
            
            elif maintenance_type == 'backup':
                # Create system backup
                backup_dir = Path('../backups')
                backup_dir.mkdir(exist_ok=True)
                
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f"loop_agi_backup_{timestamp}"
                
                # Simple backup (copy important files)
                import shutil
                backup_path = backup_dir / backup_name
                backup_path.mkdir(exist_ok=True)
                
                # Copy configuration and data files
                files_to_backup = [
                    '../config.yaml',
                    '../memory/memory.json',
                    '../modules/history.json',
                    '../benchmarks/performance.csv'
                ]
                
                backed_up = []
                for file_path in files_to_backup:
                    src = Path(file_path)
                    if src.exists():
                        dst = backup_path / src.name
                        shutil.copy2(src, dst)
                        backed_up.append(str(src))
                
                return {
                    'success': True,
                    'output': f"Backup created at {backup_path}",
                    'backup_path': str(backup_path),
                    'backed_up_files': backed_up
                }
            
            else:
                return {'success': False, 'error': f"Unknown maintenance type: {maintenance_type}"}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific task"""
        # Check active tasks
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        
        # Check completed tasks
        for task in self.completed_tasks:
            if task['task_id'] == task_id:
                return task
        
        return None
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """Get all currently active tasks"""
        return list(self.active_tasks.values())
    
    def get_task_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get task execution history"""
        return self.task_history[-limit:]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        if not self.task_history:
            return {'total_tasks': 0, 'success_rate': 0.0, 'avg_duration': 0.0}
        
        total_tasks = len(self.task_history)
        successful_tasks = sum(1 for task in self.task_history if task['success'])
        total_duration = sum(task['duration'] for task in self.task_history)
        
        return {
            'total_tasks': total_tasks,
            'success_rate': successful_tasks / total_tasks,
            'avg_duration': total_duration / total_tasks,
            'active_tasks': len(self.active_tasks)
        }

# Module interface
def create_agent_runner():
    """Factory function to create AgentRunner instance"""
    return AgentRunner()

if __name__ == "__main__":
    # Test the agent runner
    agent = AgentRunner()
    
    # Example task execution
    test_task = {
        'type': 'performance_analysis',
        'description': 'Analyze current system performance'
    }
    
    result = agent.execute_task(test_task)
    print(f"Task result: {result}")
