<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Financial Agent</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            min-height: calc(100vh - 4rem);
        }
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .positive {
            color: #10B981;
        }
        .negative {
            color: #EF4444;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online {
            background-color: #10B981;
        }
        .status-offline {
            background-color: #EF4444;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Header -->
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-900">Financial Agent Dashboard</h1>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <span class="status-indicator status-online" id="connection-status"></span>
                    <span class="text-sm text-gray-600" id="last-updated">Updating...</span>
                </div>
                <div class="relative">
                    <button id="user-menu" class="flex items-center text-sm rounded-full focus:outline-none">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Admin&background=4F46E5&color=fff" alt="User">
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <div class="bg-indigo-700 text-white w-64 sidebar p-4">
            <nav>
                <div class="space-y-1">
                    <a href="#" class="bg-indigo-800 text-white group flex items-center px-4 py-3 text-sm font-medium rounded-md">
                        <i class="fas fa-tachometer-alt mr-3 text-indigo-300"></i>
                        Dashboard
                    </a>
                    <a href="#portfolio" class="text-indigo-100 hover:bg-indigo-600 group flex items-center px-4 py-3 text-sm font-medium rounded-md">
                        <i class="fas fa-wallet mr-3 text-indigo-300"></i>
                        Portfolio
                    </a>
                    <a href="#trades" class="text-indigo-100 hover:bg-indigo-600 group flex items-center px-4 py-3 text-sm font-medium rounded-md">
                        <i class="fas fa-exchange-alt mr-3 text-indigo-300"></i>
                        Trades
                    </a>
                    <a href="#risk" class="text-indigo-100 hover:bg-indigo-600 group flex items-center px-4 py-3 text-sm font-medium rounded-md">
                        <i class="fas fa-shield-alt mr-3 text-indigo-300"></i>
                        Risk Analysis
                    </a>
                    <a href="#performance" class="text-indigo-100 hover:bg-indigo-600 group flex items-center px-4 py-3 text-sm font-medium rounded-md">
                        <i class="fas fa-chart-line mr-3 text-indigo-300"></i>
                        Performance
                    </a>
                    <a href="#settings" class="text-indigo-100 hover:bg-indigo-600 group flex items-center px-4 py-3 text-sm font-medium rounded-md">
                        <i class="fas fa-cog mr-3 text-indigo-300"></i>
                        Settings
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Portfolio Summary -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Portfolio Overview</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <!-- Total Value -->
                    <div class="bg-white rounded-lg shadow p-6 card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Total Value</p>
                                <p class="text-2xl font-semibold text-gray-900" id="portfolio-value">$0.00</p>
                            </div>
                            <div class="p-3 rounded-full bg-indigo-100 text-indigo-600">
                                <i class="fas fa-dollar-sign text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500">
                                <span id="daily-change" class="font-medium">+0.00%</span> today
                            </p>
                        </div>
                    </div>

                    <!-- Daily P&L -->
                    <div class="bg-white rounded-lg shadow p-6 card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Today's P&L</p>
                                <p class="text-2xl font-semibold" id="daily-pnl">$0.00</p>
                            </div>
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-chart-line text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500">
                                <span id="daily-pnl-pct" class="font-medium">+0.00%</span> today
                            </p>
                        </div>
                    </div>

                    <!-- Positions -->
                    <div class="bg-white rounded-lg shadow p-6 card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Positions</p>
                                <p class="text-2xl font-semibold text-gray-900" id="positions-count">0</p>
                            </div>
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-layer-group text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500">
                                <span id="active-positions">0</span> active
                            </p>
                        </div>
                    </div>

                    <!-- Risk Level -->
                    <div class="bg-white rounded-lg shadow p-6 card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Risk Level</p>
                                <p class="text-2xl font-semibold" id="risk-level">Low</p>
                            </div>
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-shield-alt text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500">
                                <span id="drawdown">0.00%</span> max drawdown
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Performance Chart -->
                <div class="bg-white p-6 rounded-lg shadow card">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Portfolio Performance</h3>
                        <div class="flex space-x-2">
                            <button class="text-xs px-2 py-1 bg-gray-100 rounded">1D</button>
                            <button class="text-xs px-2 py-1 bg-gray-100 rounded">1W</button>
                            <button class="text-xs px-2 py-1 bg-indigo-600 text-white rounded">1M</button>
                            <button class="text-xs px-2 py-1 bg-gray-100 rounded">YTD</button>
                            <button class="text-xs px-2 py-1 bg-gray-100 rounded">ALL</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="performance-chart"></canvas>
                    </div>
                </div>

                <!-- Asset Allocation -->
                <div class="bg-white p-6 rounded-lg shadow card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Asset Allocation</h3>
                    <div class="h-64 flex items-center justify-center">
                        <div class="w-48 h-48">
                            <canvas id="allocation-chart"></canvas>
                        </div>
                        <div id="allocation-legend" class="ml-4"></div>
                    </div>
                </div>
            </div>

            <!-- Positions Table -->
            <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Current Positions</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Cost</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Price</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Value</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% of Portfolio</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Beta</th>
                            </tr>
                        </thead>
                        <tbody id="positions-body" class="bg-white divide-y divide-gray-200">
                            <!-- Positions will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Trades -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Trades</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Side</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody id="trades-body" class="bg-white divide-y divide-gray-200">
                            <!-- Trades will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/dashboard.js"></script>
</body>
</html>
