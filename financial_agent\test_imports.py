"""
Test script to verify Python imports
"""
import sys
import os

print("Python Path:")
for path in sys.path:
    print(f"- {path}")

print("\nCurrent directory:", os.getcwd())
print("Directory contents:")
for item in os.listdir('.'):
    print(f"- {item}")

print("\nAgents directory contents:")
try:
    for item in os.listdir('agents'):
        print(f"- {item}")
except Exception as e:
    print(f"Error listing agents directory: {e}")

# Try to import the agent
try:
    print("\nTrying to import DataCollectionAgent...")
    from agents.data_agent import DataCollectionAgent
    print("Successfully imported DataCollectionAgent!")
    print(f"DataCollectionAgent: {DataCollectionAgent}")
except ImportError as e:
    print(f"Failed to import DataCollectionAgent: {e}")
    import traceback
    traceback.print_exc()
