"""
Test script for AnalysisAgent with DataCollectionAgent integration.
"""
import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('analysis_agent_test.log', mode='w')
    ]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.agents.analysis_agent import AnalysisAgent, SignalType
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

async def main():
    """Main test function."""
    logger.info("Starting AnalysisAgent test...")
    
    # Initialize agents
    mock_llm = MistralWrapper()
    
    # Initialize DataCollectionAgent
    data_agent = DataCollectionAgent(llm_wrapper=mock_llm)
    
    # Initialize AnalysisAgent with custom configuration
    analysis_config = {
        'rsi_period': 14,
        'rsi_overbought': 70,
        'rsi_oversold': 30,
        'macd_fast': 12,
        'macd_slow': 26,
        'macd_signal': 9,
        'bb_period': 20,
        'bb_std': 2.0,
        'volume_ma_period': 20,
        'price_ma_periods': [20, 50, 200],
        'use_llm_analysis': False
    }
    analysis_agent = AnalysisAgent(llm_wrapper=mock_llm, config=analysis_config)
    
    try:
        # Start agents
        logger.info("Starting agents...")
        await data_agent.start()
        await analysis_agent.start()
        
        # Test symbols and timeframes
        test_cases = [
            ("AAPL", "1d", "1y"),
            ("MSFT", "1d", "1y"),
            ("GOOGL", "1d", "1y"),
            ("AMZN", "1d", "1y"),
            ("META", "1d", "1y"),
        ]
        
        for symbol, interval, period in test_cases:
            try:
                logger.info("\n" + "="*80)
                logger.info(f"Testing {symbol} - {interval} - {period}")
                logger.info("="*80)
                
                # Fetch data
                logger.info(f"Fetching data for {symbol}...")
                ohlcv_data = await data_agent.fetch_ohlcv(
                    symbol=symbol,
                    interval=interval,
                    period=period,
                    timeout=30,
                    max_retries=3
                )
                
                if not ohlcv_data:
                    logger.error(f"Failed to fetch data for {symbol}")
                    continue
                
                logger.info(f"Fetched {len(ohlcv_data.timestamp)} data points for {symbol}")
                
                # Analyze data
                logger.info(f"Analyzing {symbol} data...")
                analysis_result = await analysis_agent.analyze(ohlcv_data)
                
                # Display results
                logger.info("\nANALYSIS RESULTS:")
                logger.info("-" * 40)
                logger.info(f"Symbol: {analysis_result.symbol}")
                logger.info(f"Timestamp: {datetime.fromtimestamp(analysis_result.timestamp)}")
                logger.info(f"Overall Signal: {analysis_result.overall_signal.name}")
                logger.info(f"Confidence: {analysis_result.confidence:.2f}")
                
                logger.info("\nINDICATORS:")
                for indicator in analysis_result.indicators:
                    logger.info(f"- {indicator.name}: {indicator.value:.2f} - {indicator.signal.name}")
                
                # Show some price action
                latest_close = ohlcv_data.close[-1]
                prev_close = ohlcv_data.close[-2] if len(ohlcv_data.close) > 1 else latest_close
                change_pct = ((latest_close - prev_close) / prev_close) * 100 if prev_close != 0 else 0
                
                logger.info("\nPRICE ACTION:")
                logger.info(f"Latest Close: ${latest_close:.2f}")
                logger.info(f"Previous Close: ${prev_close:.2f}")
                logger.info(f"Change: {change_pct:+.2f}%")
                
                # Generate trade recommendation
                recommendation = generate_trade_recommendation(analysis_result, latest_close, change_pct)
                logger.info("\nTRADE RECOMMENDATION:")
                logger.info(recommendation)
                
            except Exception as e:
                logger.error(f"Error processing {symbol}: {str(e)}", exc_info=True)
                continue
            
            # Add a small delay between symbols
            await asyncio.sleep(1)
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}", exc_info=True)
    finally:
        # Clean up
        logger.info("\nCleaning up...")
        if data_agent.is_running:
            await data_agent.stop()
        if analysis_agent.is_running:
            await analysis_agent.stop()
        logger.info("Test completed.")

def generate_trade_recommendation(analysis: 'AnalysisResult', price: float, change_pct: float) -> str:
    """Generate a trade recommendation based on analysis results."""
    signal = analysis.overall_signal
    confidence = analysis.confidence
    
    # Get indicator signals
    indicators = {ind.name: ind for ind in analysis.indicators}
    
    # Base recommendation on signal and confidence
    if signal == SignalType.STRONG_BUY:
        action = "STRONG BUY"
        reasoning = ["Very bullish technical indicators across the board."]
    elif signal == SignalType.BUY:
        action = "BUY"
        reasoning = ["Bullish technical indicators suggest potential upside."]
    elif signal == SignalType.STRONG_SELL:
        action = "STRONG SELL"
        reasoning = ["Very bearish technical indicators across the board."]
    elif signal == SignalType.SELL:
        action = "SELL"
        reasoning = ["Bearish technical indicators suggest potential downside."]
    else:
        action = "HOLD"
        reasoning = ["Neutral technical indicators show no clear trend."]
    
    # Add confidence level
    if confidence > 0.7:
        confidence_str = "High confidence"
    elif confidence > 0.4:
        confidence_str = "Moderate confidence"
    else:
        confidence_str = "Low confidence"
    
    # Add price action context
    if abs(change_pct) > 3.0:
        price_action = "Significant " + ("gain" if change_pct > 0 else "decline") + f" of {abs(change_pct):.1f}%"
        reasoning.append(f"{price_action} in the latest period.")
    
    # Add indicator-specific reasoning
    if 'RSI' in indicators:
        rsi = indicators['RSI']
        if rsi.value > 70:
            reasoning.append(f"RSI at {rsi.value:.1f} indicates overbought conditions.")
        elif rsi.value < 30:
            reasoning.append(f"RSI at {rsi.value:.1f} indicates oversold conditions.")
    
    if 'Trend' in indicators:
        trend = indicators['Trend']
        if trend.signal in [SignalType.STRONG_BUY, SignalType.BUY]:
            reasoning.append("Price is in an uptrend.")
        elif trend.signal in [SignalType.STRONG_SELL, SignalType.SELL]:
            reasoning.append("Price is in a downtrend.")
    
    # Compile recommendation
    recommendation = [
        f"{action} - {confidence_str} ({confidence*100:.0f}%)",
        "",
        "Key Factors:",
        *[f"- {point}" for point in reasoning],
        "",
        f"Current Price: ${price:.2f} ({'+' if change_pct >= 0 else ''}{change_pct:.2f}%)"
    ]
    
    return "\n".join(recommendation)

if __name__ == "__main__":
    asyncio.run(main())
