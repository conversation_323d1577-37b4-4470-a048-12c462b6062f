"""
Setup script for Loop 7B SW - Streaming Weights Compression
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="loop-7b-sw",
    version="0.1.0",
    author="Loop Research Team",
    author_email="<EMAIL>",
    description="Ultra-efficient 7B parameter model inference using streaming weight compression",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/Loop-7B-SW",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mkdocs>=1.4.0",
            "mkdocs-material>=9.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "loop-7b-sw=loop_7b_sw.cli:main",
        ],
    },
    keywords="ai, machine learning, llm, compression, streaming, inference, memory optimization",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/Loop-7B-SW/issues",
        "Source": "https://github.com/yourusername/Loop-7B-SW",
        "Documentation": "https://github.com/yourusername/Loop-7B-SW/docs",
    },
)
