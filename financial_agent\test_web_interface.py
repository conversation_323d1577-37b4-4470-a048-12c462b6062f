#!/usr/bin/env python3
"""
Test script for the Financial Agent web interface.
"""
import os
import sys
import asyncio
import logging
import aiohttp
import json
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 8000))
BASE_URL = f"http://{HOST}:{PORT}"

async def test_api_endpoint(endpoint: str, method: str = "GET", data: dict = None) -> dict:
    """Test an API endpoint and return the response."""
    url = f"{BASE_URL}{endpoint}"
    logger.info(f"Testing {method} {url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            if method.upper() == "GET":
                async with session.get(url) as response:
                    if response.status != 200:
                        logger.error(f"Error: {response.status} - {await response.text()}")
                        return None
                    return await response.json()
            elif method.upper() == "POST":
                async with session.post(url, json=data) as response:
                    if response.status != 200:
                        logger.error(f"Error: {response.status} - {await response.text()}")
                        return None
                    return await response.json()
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None
    except Exception as e:
        logger.error(f"Error testing {url}: {e}")
        return None

async def test_web_interface():
    """Run tests against the web interface."""
    logger.info("Starting web interface tests...")
    
    # Test 1: Check if the web server is running
    logger.info("\n=== Test 1: Server Status ===")
    response = await test_api_endpoint("/")
    if response is None:
        logger.error("❌ Failed to connect to the web server")
        return False
    logger.info("✅ Web server is running")
    
    # Test 2: Check portfolio endpoint
    logger.info("\n=== Test 2: Portfolio Endpoint ===")
    portfolio = await test_api_endpoint("/api/portfolio")
    if portfolio is None:
        logger.error("❌ Failed to fetch portfolio data")
        return False
    logger.info(f"✅ Portfolio data: {json.dumps(portfolio, indent=2, default=str)}")
    
    # Test 3: Check trades endpoint
    logger.info("\n=== Test 3: Trades Endpoint ===")
    trades = await test_api_endpoint("/api/trades")
    if trades is None:
        logger.error("❌ Failed to fetch trades data")
        return False
    logger.info(f"✅ Trades data: {json.dumps(trades, indent=2, default=str)}")
    
    # Test 4: Check risk metrics endpoint
    logger.info("\n=== Test 4: Risk Metrics Endpoint ===")
    risk_metrics = await test_api_endpoint("/api/risk")
    if risk_metrics is None:
        logger.error("❌ Failed to fetch risk metrics")
        return False
    logger.info(f"✅ Risk metrics: {json.dumps(risk_metrics, indent=2, default=str)}")
    
    # Test 5: Check performance metrics endpoint
    logger.info("\n=== Test 5: Performance Metrics Endpoint ===")
    performance = await test_api_endpoint("/api/performance")
    if performance is None:
        logger.error("❌ Failed to fetch performance metrics")
        return False
    logger.info(f"✅ Performance metrics: {json.dumps(performance, indent=2, default=str)}")
    
    logger.info("\n✅ All tests passed successfully!")
    return True

if __name__ == "__main__":
    # Run the tests
    success = asyncio.run(test_web_interface())
    sys.exit(0 if success else 1)
