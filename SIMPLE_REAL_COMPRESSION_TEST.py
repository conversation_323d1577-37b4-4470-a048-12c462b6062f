#!/usr/bin/env python3
"""
SIMPLE REAL COMPRESSION TEST
============================

Direct, simple test of real compression techniques
No complex frameworks - just direct measurements
"""

import torch
import psutil
import time
import json
import gc
import os
from safetensors import safe_open

def measure_ram():
    """Simple RAM measurement"""
    process = psutil.Process()
    ram_gb = process.memory_info().rss / (1024**3)
    print(f"📊 RAM: {ram_gb:.3f}GB")
    return ram_gb

def test_layer_loading():
    """Test loading single vs multiple layers"""
    
    print("🧪 TESTING LAYER LOADING")
    print("=" * 30)
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Load model index
    index_path = os.path.join(model_path, "model.safetensors.index.json")
    with open(index_path, 'r') as f:
        weight_index = json.load(f)
    
    # Find 3 test layers
    test_layers = []
    for name in weight_index['weight_map'].keys():
        if 'q_proj.weight' in name:
            test_layers.append(name)
            if len(test_layers) >= 3:
                break
    
    print(f"📊 Testing with {len(test_layers)} layers")
    
    # Test 1: Load all at once
    print("\n🔄 Loading all layers simultaneously...")
    ram_before = measure_ram()
    
    loaded_layers = {}
    for layer_name in test_layers:
        file_name = weight_index['weight_map'][layer_name]
        file_path = os.path.join(model_path, file_name)
        
        with safe_open(file_path, framework="pt", device="cpu") as f:
            tensor = f.get_tensor(layer_name)
            loaded_layers[layer_name] = tensor.clone()
            print(f"   Loaded: {layer_name} {tensor.shape}")
    
    ram_all_loaded = measure_ram()
    ram_increase_all = ram_all_loaded - ram_before
    
    # Clear memory
    loaded_layers.clear()
    gc.collect()
    
    # Test 2: Load one at a time
    print("\n🔄 Loading layers one by one...")
    ram_before_streaming = measure_ram()
    
    max_ram_streaming = ram_before_streaming
    
    for i, layer_name in enumerate(test_layers):
        file_name = weight_index['weight_map'][layer_name]
        file_path = os.path.join(model_path, file_name)
        
        with safe_open(file_path, framework="pt", device="cpu") as f:
            tensor = f.get_tensor(layer_name)
            
            current_ram = measure_ram()
            max_ram_streaming = max(max_ram_streaming, current_ram)
            
            print(f"   Layer {i+1}: {layer_name} {tensor.shape}")
            
            # Simulate computation
            if tensor.dim() == 2:
                test_input = torch.randn(1, tensor.shape[1])
                output = torch.matmul(test_input, tensor.t())
                print(f"   Computation: {test_input.shape} × {tensor.shape} = {output.shape}")
            
            del tensor
            gc.collect()
    
    ram_streaming_increase = max_ram_streaming - ram_before_streaming
    
    # Results
    print(f"\n📊 RESULTS:")
    print(f"   All at once: +{ram_increase_all:.3f}GB")
    print(f"   Streaming: +{ram_streaming_increase:.3f}GB")
    print(f"   Savings: {ram_increase_all - ram_streaming_increase:.3f}GB")
    print(f"   Efficiency: {ram_increase_all / ram_streaming_increase:.2f}×")
    
    return {
        'all_at_once_gb': ram_increase_all,
        'streaming_gb': ram_streaming_increase,
        'savings_gb': ram_increase_all - ram_streaming_increase,
        'efficiency': ram_increase_all / ram_streaming_increase if ram_streaming_increase > 0 else 1.0
    }

def test_1bit_quantization():
    """Test 1-bit quantization on a single layer"""
    
    print("\n⚡ TESTING 1-BIT QUANTIZATION")
    print("=" * 35)
    
    model_path = "downloaded_models/mistral-7b-v0.1"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    # Load model index
    index_path = os.path.join(model_path, "model.safetensors.index.json")
    with open(index_path, 'r') as f:
        weight_index = json.load(f)
    
    # Find a test layer
    test_layer = None
    for name in weight_index['weight_map'].keys():
        if 'q_proj.weight' in name:
            test_layer = name
            break
    
    if not test_layer:
        print("❌ No test layer found")
        return
    
    print(f"📊 Testing layer: {test_layer}")
    
    # Load layer
    file_name = weight_index['weight_map'][test_layer]
    file_path = os.path.join(model_path, file_name)
    
    ram_before = measure_ram()
    
    with safe_open(file_path, framework="pt", device="cpu") as f:
        tensor = f.get_tensor(test_layer)
        
        ram_after_load = measure_ram()
        
        print(f"   Original shape: {tensor.shape}")
        print(f"   Original dtype: {tensor.dtype}")
        
        # Calculate original size
        original_size_mb = tensor.numel() * tensor.element_size() / (1024**2)
        print(f"   Original size: {original_size_mb:.1f}MB")
        
        # 1-bit quantization
        print("\n🔬 Applying 1-bit quantization...")
        
        # Convert to float32 for processing
        tensor_f32 = tensor.to(torch.float32)
        
        # Calculate mean and apply sign quantization
        tensor_mean = torch.mean(tensor_f32)
        centered = tensor_f32 - tensor_mean
        binary_weights = torch.sign(centered)  # -1 or +1
        
        # Convert to uint8 for storage (0 or 1)
        binary_uint8 = ((binary_weights + 1) / 2).to(torch.uint8)
        
        ram_after_quantization = measure_ram()
        
        # Calculate compressed size
        compressed_size_mb = binary_uint8.numel() * binary_uint8.element_size() / (1024**2)
        compression_ratio = original_size_mb / compressed_size_mb
        
        print(f"   Compressed size: {compressed_size_mb:.1f}MB")
        print(f"   Compression ratio: {compression_ratio:.1f}×")
        
        # Quality test - reconstruct and compare
        print("\n🔍 Quality assessment...")
        
        # Reconstruct
        tensor_std = torch.std(tensor_f32)
        reconstructed = (binary_uint8.to(torch.float32) * 2 - 1) * tensor_std + tensor_mean
        
        # Calculate errors
        mse_error = torch.mean((tensor_f32 - reconstructed) ** 2).item()
        mae_error = torch.mean(torch.abs(tensor_f32 - reconstructed)).item()
        
        tensor_range = torch.max(tensor_f32) - torch.min(tensor_f32)
        relative_error = mae_error / tensor_range.item() if tensor_range > 0 else 0
        
        print(f"   MSE error: {mse_error:.6f}")
        print(f"   MAE error: {mae_error:.6f}")
        print(f"   Relative error: {relative_error*100:.2f}%")
        
        # Test computation with quantized weights
        print("\n🧮 Testing computation...")
        
        if tensor.dim() == 2:
            test_input = torch.randn(1, tensor.shape[1])
            
            # Original computation
            original_output = torch.matmul(test_input, tensor.t())
            
            # Quantized computation
            quantized_output = torch.matmul(test_input, reconstructed.t())
            
            # Compare outputs
            output_error = torch.mean(torch.abs(original_output - quantized_output)).item()
            output_relative_error = output_error / torch.mean(torch.abs(original_output)).item()
            
            print(f"   Output error: {output_error:.6f}")
            print(f"   Output relative error: {output_relative_error*100:.2f}%")
        
        ram_final = measure_ram()
        
        return {
            'original_size_mb': original_size_mb,
            'compressed_size_mb': compressed_size_mb,
            'compression_ratio': compression_ratio,
            'quality_loss_percent': relative_error * 100,
            'output_error_percent': output_relative_error * 100 if 'output_relative_error' in locals() else 0,
            'ram_increase_gb': ram_final - ram_before
        }

def main():
    """Run simple real compression tests"""
    
    print("🚀 SIMPLE REAL COMPRESSION TEST")
    print("=" * 50)
    print("GOAL: Test real compression techniques directly")
    print("BASELINE: 2.58GB → 1.72GB (1.5× verified)")
    print()
    
    # Test layer streaming
    streaming_results = test_layer_loading()
    
    # Test 1-bit quantization
    quantization_results = test_1bit_quantization()
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print("=" * 20)
    
    if streaming_results:
        print(f"LAYER STREAMING:")
        print(f"   Efficiency: {streaming_results['efficiency']:.2f}×")
        print(f"   RAM savings: {streaming_results['savings_gb']:.3f}GB")
    
    if quantization_results:
        print(f"\n1-BIT QUANTIZATION:")
        print(f"   Compression: {quantization_results['compression_ratio']:.1f}×")
        print(f"   Quality loss: {quantization_results['quality_loss_percent']:.2f}%")
        print(f"   Output error: {quantization_results['output_error_percent']:.2f}%")
    
    # Combined projection
    if streaming_results and quantization_results:
        combined_compression = streaming_results['efficiency'] * quantization_results['compression_ratio']
        
        print(f"\nCOMBINED APPROACH:")
        print(f"   Total compression: {combined_compression:.1f}×")
        
        # Project to 675B
        baseline_7b = 2.58  # GB
        compressed_7b = baseline_7b / combined_compression
        scaling_factor = 675 / 7  # 675B / 7B
        projected_675b = compressed_7b * scaling_factor
        
        print(f"   7B projection: {baseline_7b:.2f}GB → {compressed_7b:.3f}GB")
        print(f"   675B projection: {projected_675b:.1f}GB")
        print(f"   Fits 8GB laptop: {'✅ YES' if projected_675b <= 6.0 else '❌ NO'}")
    
    # Save results
    results = {
        'timestamp': time.time(),
        'streaming_results': streaming_results,
        'quantization_results': quantization_results
    }
    
    with open('simple_compression_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✅ Results saved: simple_compression_test_results.json")

if __name__ == "__main__":
    main()
