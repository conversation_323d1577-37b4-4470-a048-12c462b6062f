#!/usr/bin/env python3
"""
REAL Intelligence Benchmarking System
- Actual problem-solving tests
- Real reasoning capability measurement
- Genuine domain expertise validation
- Measurable performance improvements
"""

import sys
import time
import json
import math
import random
from pathlib import Path

# Add loop_singular_bit to path
sys.path.append(str(Path(__file__).parent.parent / 'loop_singular_bit'))

class RealIntelligenceBenchmarks:
    def __init__(self, model):
        self.model = model
        self.benchmark_history = []
        
    def test_mathematical_reasoning(self) -> dict:
        """Test actual mathematical problem-solving capability"""
        problems = [
            {"problem": "Solve: 2x + 5 = 17", "answer": "6", "difficulty": 1},
            {"problem": "Find derivative of x^3 + 2x^2 - 5x + 1", "answer": "3x^2 + 4x - 5", "difficulty": 2},
            {"problem": "Integrate: ∫(2x + 3)dx", "answer": "x^2 + 3x + C", "difficulty": 2},
            {"problem": "Solve system: 2x + y = 7, x - y = 2", "answer": "x=3, y=1", "difficulty": 3},
            {"problem": "Find limit: lim(x→0) sin(x)/x", "answer": "1", "difficulty": 4}
        ]
        
        correct = 0
        total_difficulty = 0
        results = []
        
        for prob in problems:
            prompt = f"Solve this mathematical problem step by step: {prob['problem']}"
            try:
                response = self.model.generate(prompt, max_length=100)
                
                # Simple answer checking (contains key elements)
                answer_found = any(key in response.lower() for key in prob['answer'].lower().split())
                
                if answer_found:
                    correct += 1
                    total_difficulty += prob['difficulty']
                
                results.append({
                    'problem': prob['problem'],
                    'expected': prob['answer'],
                    'response': response,
                    'correct': answer_found,
                    'difficulty': prob['difficulty']
                })
                
            except Exception as e:
                results.append({
                    'problem': prob['problem'],
                    'error': str(e),
                    'correct': False,
                    'difficulty': prob['difficulty']
                })
        
        score = (correct / len(problems)) * 100
        weighted_score = (total_difficulty / sum(p['difficulty'] for p in problems)) * 100
        
        return {
            'test_type': 'mathematical_reasoning',
            'score': score,
            'weighted_score': weighted_score,
            'correct': correct,
            'total': len(problems),
            'results': results
        }
    
    def test_logical_reasoning(self) -> dict:
        """Test actual logical problem-solving capability"""
        problems = [
            {
                "problem": "If all cats are mammals, and Fluffy is a cat, what is Fluffy?",
                "answer": "mammal",
                "difficulty": 1
            },
            {
                "problem": "A train leaves at 2 PM going 60 mph. Another leaves at 3 PM going 80 mph. When do they meet if 240 miles apart?",
                "answer": "5 PM",
                "difficulty": 3
            },
            {
                "problem": "If A implies B, and B implies C, and A is true, what can we conclude about C?",
                "answer": "true",
                "difficulty": 2
            },
            {
                "problem": "In a group of 100 people, 70 like coffee, 60 like tea. How many like both if everyone likes at least one?",
                "answer": "30",
                "difficulty": 4
            }
        ]
        
        correct = 0
        total_difficulty = 0
        results = []
        
        for prob in problems:
            prompt = f"Solve this logical reasoning problem: {prob['problem']}"
            try:
                response = self.model.generate(prompt, max_length=100)
                
                # Check if answer is in response
                answer_found = prob['answer'].lower() in response.lower()
                
                if answer_found:
                    correct += 1
                    total_difficulty += prob['difficulty']
                
                results.append({
                    'problem': prob['problem'],
                    'expected': prob['answer'],
                    'response': response,
                    'correct': answer_found,
                    'difficulty': prob['difficulty']
                })
                
            except Exception as e:
                results.append({
                    'problem': prob['problem'],
                    'error': str(e),
                    'correct': False,
                    'difficulty': prob['difficulty']
                })
        
        score = (correct / len(problems)) * 100
        weighted_score = (total_difficulty / sum(p['difficulty'] for p in problems)) * 100
        
        return {
            'test_type': 'logical_reasoning',
            'score': score,
            'weighted_score': weighted_score,
            'correct': correct,
            'total': len(problems),
            'results': results
        }
    
    def test_language_understanding(self) -> dict:
        """Test actual language comprehension and generation"""
        tasks = [
            {
                "task": "Summarize: 'The quick brown fox jumps over the lazy dog' in 5 words",
                "check": lambda r: len(r.split()) <= 7,  # Allow some flexibility
                "difficulty": 1
            },
            {
                "task": "What is the main theme of this text: 'Climate change affects global weather patterns'",
                "check": lambda r: any(word in r.lower() for word in ['climate', 'weather', 'environment']),
                "difficulty": 2
            },
            {
                "task": "Generate a rhyming couplet about technology",
                "check": lambda r: len(r.split('\n')) >= 2 or len(r.split()) >= 8,
                "difficulty": 3
            },
            {
                "task": "Explain the difference between 'affect' and 'effect'",
                "check": lambda r: 'verb' in r.lower() and 'noun' in r.lower(),
                "difficulty": 2
            }
        ]
        
        correct = 0
        total_difficulty = 0
        results = []
        
        for task in tasks:
            try:
                response = self.model.generate(task['task'], max_length=80)
                
                # Check if response meets criteria
                meets_criteria = task['check'](response)
                
                if meets_criteria:
                    correct += 1
                    total_difficulty += task['difficulty']
                
                results.append({
                    'task': task['task'],
                    'response': response,
                    'correct': meets_criteria,
                    'difficulty': task['difficulty']
                })
                
            except Exception as e:
                results.append({
                    'task': task['task'],
                    'error': str(e),
                    'correct': False,
                    'difficulty': task['difficulty']
                })
        
        score = (correct / len(tasks)) * 100
        weighted_score = (total_difficulty / sum(t['difficulty'] for t in tasks)) * 100
        
        return {
            'test_type': 'language_understanding',
            'score': score,
            'weighted_score': weighted_score,
            'correct': correct,
            'total': len(tasks),
            'results': results
        }
    
    def test_creative_problem_solving(self) -> dict:
        """Test actual creative and novel problem-solving"""
        challenges = [
            {
                "challenge": "List 3 unusual uses for a paperclip",
                "check": lambda r: len([x for x in r.split('\n') if x.strip()]) >= 2,
                "difficulty": 2
            },
            {
                "challenge": "How would you measure the height of a building using only a barometer?",
                "check": lambda r: len(r.split()) >= 15,  # Requires explanation
                "difficulty": 4
            },
            {
                "challenge": "Design a simple solution for reducing plastic waste",
                "check": lambda r: any(word in r.lower() for word in ['reuse', 'recycle', 'reduce', 'alternative']),
                "difficulty": 3
            }
        ]
        
        correct = 0
        total_difficulty = 0
        results = []
        
        for challenge in challenges:
            try:
                response = self.model.generate(challenge['challenge'], max_length=100)
                
                # Check creativity criteria
                meets_criteria = challenge['check'](response)
                
                if meets_criteria:
                    correct += 1
                    total_difficulty += challenge['difficulty']
                
                results.append({
                    'challenge': challenge['challenge'],
                    'response': response,
                    'correct': meets_criteria,
                    'difficulty': challenge['difficulty']
                })
                
            except Exception as e:
                results.append({
                    'challenge': challenge['challenge'],
                    'error': str(e),
                    'correct': False,
                    'difficulty': challenge['difficulty']
                })
        
        score = (correct / len(challenges)) * 100
        weighted_score = (total_difficulty / sum(c['difficulty'] for c in challenges)) * 100
        
        return {
            'test_type': 'creative_problem_solving',
            'score': score,
            'weighted_score': weighted_score,
            'correct': correct,
            'total': len(challenges),
            'results': results
        }
    
    def run_full_benchmark(self) -> dict:
        """Run complete intelligence benchmark suite"""
        print("🧠 Running REAL Intelligence Benchmarks...")
        
        start_time = time.time()
        
        # Run all benchmark tests
        math_results = self.test_mathematical_reasoning()
        logic_results = self.test_logical_reasoning()
        language_results = self.test_language_understanding()
        creative_results = self.test_creative_problem_solving()
        
        end_time = time.time()
        
        # Calculate overall intelligence score
        all_tests = [math_results, logic_results, language_results, creative_results]
        overall_score = sum(test['weighted_score'] for test in all_tests) / len(all_tests)
        
        # Determine intelligence classification based on REAL performance
        if overall_score >= 90:
            classification = "EXPERT_LEVEL"
        elif overall_score >= 75:
            classification = "ADVANCED"
        elif overall_score >= 60:
            classification = "INTERMEDIATE"
        elif overall_score >= 40:
            classification = "BASIC"
        else:
            classification = "DEVELOPING"
        
        benchmark_result = {
            'timestamp': time.time(),
            'overall_score': overall_score,
            'classification': classification,
            'test_duration': end_time - start_time,
            'detailed_results': {
                'mathematical_reasoning': math_results,
                'logical_reasoning': logic_results,
                'language_understanding': language_results,
                'creative_problem_solving': creative_results
            },
            'summary': {
                'total_problems': sum(test['total'] for test in all_tests),
                'total_correct': sum(test['correct'] for test in all_tests),
                'accuracy': sum(test['correct'] for test in all_tests) / sum(test['total'] for test in all_tests) * 100
            }
        }
        
        # Store in history
        self.benchmark_history.append(benchmark_result)
        
        return benchmark_result
    
    def measure_improvement(self) -> dict:
        """Measure REAL improvement over time"""
        if len(self.benchmark_history) < 2:
            return {'improvement': 0, 'message': 'Need at least 2 benchmarks to measure improvement'}
        
        latest = self.benchmark_history[-1]
        previous = self.benchmark_history[-2]
        
        score_improvement = latest['overall_score'] - previous['overall_score']
        accuracy_improvement = latest['summary']['accuracy'] - previous['summary']['accuracy']
        
        return {
            'score_improvement': score_improvement,
            'accuracy_improvement': accuracy_improvement,
            'classification_change': {
                'from': previous['classification'],
                'to': latest['classification']
            },
            'real_improvement': score_improvement > 1.0  # Significant improvement threshold
        }
