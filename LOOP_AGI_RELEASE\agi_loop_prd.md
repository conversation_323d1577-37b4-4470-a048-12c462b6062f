# 🗂️ Project Planning Document: Autonomous Recursive AGI (LOOP AGI)

## 🧭 Overview
Codename: **LOOP AGI**
Goal: Build a self-improving, safe, autonomous AGI that can evolve its own capabilities using recursive reasoning, modular code evolution, and self-testing on a local machine.

---

## 📅 Milestone-Based Timeline

### Week 1 — MVP Initiation
- [x] Set up working folder structure and environment
- [x] Implement base LLM engine wrapper (BitNet++.cpp or Mistral GGUF)
- [x] Create minimal `loop.py` file for 1 cycle test
- [ ] Log input, output, and thoughts of 1 cycle
- [ ] Add config and safety rules engine (static YAML)

### Week 2 — Basic Recursive Loop
- [ ] Implement file tracker: `/loop/logs/input_commands.log`
- [ ] Add module generator (`self_modify.py`)
- [ ] Create test harness (unit + integration)
- [ ] Enable module mutation and rollback
- [ ] Log new module into `/loop/modules/history.json`

### Week 3 — Thought Logging and Metrics
- [ ] Enable meta-cognitive logging (`/loop/thoughts.log`)
- [ ] Create performance metrics tracker (`/loop/benchmarks/performance.csv`)
- [ ] Display improvement rate over time (CLI chart or simple dashboard)
- [ ] Add safety verification engine (test + confirm safe code only)

### Week 4 — Self-Reflection and Continuous Evolution
- [ ] Self-evaluate performance and reasoning quality
- [ ] Create goal-setting system for future cycles
- [ ] Trigger loop scheduler with goal-oriented cycle initiation
- [ ] Add memory store (simple file or SQLite)

### Week 5 — Stability and Scaling
- [ ] Stress test 100 cycles
- [ ] Optimize RAM and CPU usage for laptop constraints
- [ ] Add interrupt + emergency stop
- [ ] Add reproducibility tools (zip logs, git-compatible backups)

---

## 🧰 Folder Structure
```
/loop_agi
├── loop.py                  # Core execution engine
├── config.yaml              # Global rules and safety policies
├── self_modify.py           # Code evolution module
├── validate.py              # Safety + functional test suite
├── memory/                  # Persistent memory store
│   └── memory.json
├── modules/                 # Generated modules
│   └── history.json
├── logs/
│   ├── thoughts.log         # Meta-cognitive thoughts
│   ├── recursion.log        # Loop cycle logs
│   └── input_commands.log   # Human interaction scan
├── benchmarks/
│   └── performance.csv      # Timestamped metrics
├── status_report.md         # Summary report of all evolution cycles
└── tools/
    └── agent_runner.py      # Task execution agent
```

---

## ✅ Loop Cycle Checklist (per iteration)
1. [ ] Scan for new human input (must be 0)
2. [ ] Analyze recent performance metrics
3. [ ] Set new goal based on weakness
4. [ ] Generate or mutate code module
5. [ ] Run safety and performance tests
6. [ ] If valid: integrate & log, else rollback
7. [ ] Save all logs + memory snapshot
8. [ ] Schedule next cycle based on conditions

---

## 🔐 Safety Rules Examples (config.yaml)
```yaml
prohibited_actions:
  - os.system("rm -rf")
  - open("/etc/passwd")
  - network_calls unless approved
max_cpu: 75%
max_ram: 7GB
allow_code_execution: true
rollback_on_failure: true
safety_score_threshold: 0.95
```

---

## 📈 Success Criteria
- 100 autonomous cycles
- ≥ 4x intelligence multiplier from initial baseline
- ≥ 90% module success rate
- ≤ 8GB RAM, ≤ 5GB disk
- Fully functional rollback system
- Documented proof of evolution

---

This is the **master plan** for LOOP AGI development. Let’s now create `todo-list.md`.

