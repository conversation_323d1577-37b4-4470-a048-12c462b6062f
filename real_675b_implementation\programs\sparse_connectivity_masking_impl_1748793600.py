def sparse_connectivity_masking(basis_tensors, target_sparsity=0.95, learning_rate=0.01):
    '''
    Real sparse connectivity with learned binary masks
    
    Args:
        basis_tensors: List of basis tensors
        target_sparsity: Target sparsity level (0.95 = 95% sparse)
        learning_rate: Learning rate for mask optimization
    
    Returns:
        dict: {
            'sparse_masks': Learned binary masks,
            'sparse_tensors': Masked sparse tensors,
            'actual_sparsity': Achieved sparsity level,
            'mask_gradients': Gradients for mask learning
        }
    '''
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    
    sparse_masks = []
    sparse_tensors = []
    mask_gradients = []
    
    for tensor in basis_tensors:
        # 1. Initialize learnable mask parameters (logits)
        mask_logits = nn.Parameter(torch.randn_like(tensor))
        
        # 2. Gumbel-Softmax sampling for differentiable binarization
        def gumbel_softmax(logits, temperature=1.0, hard=False):
            gumbel_noise = -torch.log(-torch.log(torch.rand_like(logits)))
            y = logits + gumbel_noise
            y = torch.sigmoid(y / temperature)  # Use sigmoid for binary masks

            if hard:
                y_hard = (y > 0.5).float()
                y = (y_hard - y).detach() + y
            return y

        # Optimization loop to achieve target sparsity
        optimizer = torch.optim.Adam([mask_logits], lr=learning_rate)
        
        for i in range(1000):  # Iterate to reach sparsity
            optimizer.zero_grad()
            
            # Sample mask using Gumbel-Softmax
            mask = gumbel_softmax(mask_logits, temperature=0.5, hard=True) # Hard=True for straight-through estimator
            
            # Apply mask to tensor
            masked_tensor = tensor * mask
            
            # Calculate sparsity regularization loss
            sparsity_loss = torch.abs(mask.mean() - (1 - target_sparsity))
            
            # Backpropagate to update mask logits
            sparsity_loss.backward()
            optimizer.step()
        
        # Evaluate achieved sparsity
        final_mask = gumbel_softmax(mask_logits, temperature=0.5, hard=True)
        actual_sparsity = (final_mask == 0).float().mean().item()

        # Store results
        sparse_masks.append(final_mask.detach())
        sparse_tensors.append(tensor * final_mask.detach())
        mask_gradients.append(mask_logits.grad) # Capture gradient after optimization
        
    result = {
        'sparse_masks': sparse_masks,
        'sparse_tensors': sparse_tensors,
        'actual_sparsity': actual_sparsity,
        'mask_gradients': mask_gradients
    }
    
    return result