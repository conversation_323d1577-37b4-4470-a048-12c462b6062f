"""
Web interface for monitoring the trading system.
"""
import os
import json
import logging
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Set

from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import uvicorn

# Import trading system
from .integration import TradingSystem

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Financial Agent Dashboard",
    description="Web interface for monitoring the trading system",
    version="1.0.0"
)

# Set up templates
templates = Jinja2Templates(directory=os.path.join(os.path.dirname(__file__), "templates"))

# Mount static files
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

# Configuration
CONFIG = {
    'initial_portfolio': 100000.0,
    'watchlist': ['SPY', 'QQQ', 'IWM', 'DIA', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META'],
    'timeframe': '1d',
    'data_agent': {
        'api_key': os.getenv('ALPHA_VANTAGE_API_KEY', ''),
        'cache_ttl': 300  # 5 minutes
    },
    'analysis_agent': {
        'indicators': ['sma', 'rsi', 'macd', 'bollinger_bands']
    },
    'strategy_agent': {
        'strategy': 'etf_rotation',
        'rebalance_frequency': 'monthly',
        'max_positions': 5,
        'position_size': 0.2  # 20% per position
    },
    'execution_agent': {
        'paper_trading': True,
        'slippage': 0.001,  # 0.1% slippage
        'commission': 0.005  # $0.005 per share
    },
    'risk_agent': {
        'max_drawdown': 0.10,  # 10%
        'max_position_risk': 0.02,  # 2% of portfolio
        'max_sector_exposure': 0.30,  # 30% per sector
        'max_leverage': 1.0,  # No leverage
        'daily_loss_limit': 0.05,  # 5% daily loss limit
        'position_concentration': 0.20,  # 20% max per position
        'volatility_threshold': 0.30,  # 30% annualized
        'min_volume': 100000,  # 100k shares daily volume
        'min_price': 5.0  # $5 minimum price
    }
}

# Initialize trading system
trading_system = TradingSystem(config=CONFIG)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"New WebSocket connection. Total connections: {len(self.active_connections)}")
        
        # Register with trading system
        await trading_system.connect_client(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket disconnected. Remaining connections: {len(self.active_connections)}")
        
        # Unregister from trading system
        trading_system.disconnect_client(websocket)

    async def broadcast(self, message: str):
        if not self.active_connections:
            return
            
        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                disconnected.add(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

# Initialize WebSocket manager
manager = ConnectionManager()

# API Endpoints
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serve the main dashboard page."""
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "title": "Trading Dashboard"}
    )

@app.get("/api/portfolio", response_model=Dict[str, Any])
async def get_portfolio():
    """Get current portfolio data."""
    return await trading_system.get_portfolio()

@app.get("/api/trades", response_model=List[Dict[str, Any]])
async def get_trades(limit: int = 50):
    """Get recent trades."""
    return await trading_system.get_trades(limit=limit)

@app.get("/api/risk", response_model=Dict[str, Any])
async def get_risk_metrics():
    """Get current risk metrics."""
    return await trading_system.get_risk_metrics()

@app.get("/api/performance", response_model=Dict[str, Any])
async def get_performance_metrics():
    """Get performance metrics."""
    return await trading_system.get_performance()

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the application."""
    logger.info("Starting trading system...")
    asyncio.create_task(trading_system.start())

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    logger.info("Shutting down trading system...")
    await trading_system.stop()

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
