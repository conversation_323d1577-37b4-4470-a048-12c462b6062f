#!/usr/bin/env python3
"""
RAG (Retrieval-Augmented Generation) System
External knowledge retrieval to boost intelligence beyond 100%
Goal: Enhance complex reasoning with external knowledge
"""

import time
import json
import requests
import hashlib
from typing import Dict, Any, List, Optional
from pathlib import Path

class KnowledgeRetriever:
    """Retrieves external knowledge for enhanced reasoning"""
    
    def __init__(self):
        self.knowledge_cache = {}
        self.cache_file = "rag_knowledge_cache.json"
        self.load_cache()
        
        # Built-in knowledge base for offline operation
        self.offline_knowledge = {
            # Mathematical knowledge
            "calculus": {
                "derivative_rules": {
                    "power_rule": "d/dx(x^n) = n*x^(n-1)",
                    "product_rule": "d/dx(uv) = u'v + uv'",
                    "chain_rule": "d/dx(f(g(x))) = f'(g(x)) * g'(x)",
                    "quotient_rule": "d/dx(u/v) = (u'v - uv')/v^2"
                },
                "integration_rules": {
                    "power_rule": "∫x^n dx = x^(n+1)/(n+1) + C",
                    "substitution": "∫f(g(x))g'(x) dx = ∫f(u) du where u = g(x)",
                    "by_parts": "∫u dv = uv - ∫v du"
                },
                "limits": {
                    "lhopital": "If lim f(x)/g(x) = 0/0 or ∞/∞, then lim f(x)/g(x) = lim f'(x)/g'(x)",
                    "squeeze_theorem": "If g(x) ≤ f(x) ≤ h(x) and lim g(x) = lim h(x) = L, then lim f(x) = L"
                }
            },
            
            # Physics knowledge
            "physics": {
                "mechanics": {
                    "newton_laws": [
                        "F = ma (Second Law)",
                        "For every action, there is an equal and opposite reaction (Third Law)",
                        "An object at rest stays at rest unless acted upon by a force (First Law)"
                    ],
                    "kinematics": {
                        "velocity": "v = dx/dt",
                        "acceleration": "a = dv/dt = d²x/dt²",
                        "equations": ["v = v₀ + at", "x = x₀ + v₀t + ½at²", "v² = v₀² + 2a(x-x₀)"]
                    }
                },
                "constants": {
                    "speed_of_light": "c = 299,792,458 m/s",
                    "gravity": "g = 9.81 m/s²",
                    "planck_constant": "h = 6.626 × 10⁻³⁴ J⋅s"
                }
            },
            
            # Logic and reasoning
            "logic": {
                "formal_logic": {
                    "modus_ponens": "If P → Q and P, then Q",
                    "modus_tollens": "If P → Q and ¬Q, then ¬P",
                    "hypothetical_syllogism": "If P → Q and Q → R, then P → R",
                    "disjunctive_syllogism": "If P ∨ Q and ¬P, then Q"
                },
                "set_theory": {
                    "union": "A ∪ B = {x : x ∈ A or x ∈ B}",
                    "intersection": "A ∩ B = {x : x ∈ A and x ∈ B}",
                    "complement": "A' = {x : x ∉ A}",
                    "difference": "A - B = {x : x ∈ A and x ∉ B}"
                }
            },
            
            # Computer science
            "computer_science": {
                "algorithms": {
                    "time_complexity": {
                        "O(1)": "Constant time",
                        "O(log n)": "Logarithmic time",
                        "O(n)": "Linear time",
                        "O(n log n)": "Linearithmic time",
                        "O(n²)": "Quadratic time"
                    },
                    "sorting": {
                        "quicksort": "Average O(n log n), worst O(n²)",
                        "mergesort": "Always O(n log n)",
                        "heapsort": "Always O(n log n)"
                    }
                }
            }
        }
        
        print("📚 RAG Knowledge Retriever initialized")
        print(f"   Offline knowledge domains: {len(self.offline_knowledge)}")
        print(f"   Cache entries: {len(self.knowledge_cache)}")
    
    def retrieve_knowledge(self, query: str, domain: str = "general") -> Dict[str, Any]:
        """Retrieve relevant knowledge for a query"""
        
        # Check cache first
        cache_key = hashlib.md5(f"{query}_{domain}".encode()).hexdigest()
        if cache_key in self.knowledge_cache:
            print(f"📖 Retrieved from cache: {query[:30]}...")
            return self.knowledge_cache[cache_key]
        
        # Search offline knowledge
        offline_results = self._search_offline_knowledge(query, domain)
        
        # Try online retrieval (with fallback)
        online_results = self._search_online_knowledge(query, domain)
        
        # Combine results
        combined_results = {
            'query': query,
            'domain': domain,
            'offline_results': offline_results,
            'online_results': online_results,
            'timestamp': time.time(),
            'success': len(offline_results) > 0 or len(online_results) > 0
        }
        
        # Cache the results
        self.knowledge_cache[cache_key] = combined_results
        self.save_cache()
        
        return combined_results
    
    def _search_offline_knowledge(self, query: str, domain: str) -> List[Dict[str, Any]]:
        """Search built-in offline knowledge base"""
        
        results = []
        query_lower = query.lower()
        
        # Search all domains if domain is general
        search_domains = [domain] if domain != "general" else list(self.offline_knowledge.keys())
        
        for search_domain in search_domains:
            if search_domain in self.offline_knowledge:
                domain_data = self.offline_knowledge[search_domain]
                
                # Recursive search through nested dictionaries
                matches = self._recursive_search(domain_data, query_lower, search_domain)
                results.extend(matches)
        
        # Sort by relevance
        results.sort(key=lambda x: x.get('relevance', 0), reverse=True)
        
        return results[:5]  # Top 5 results
    
    def _recursive_search(self, data: Any, query: str, path: str = "") -> List[Dict[str, Any]]:
        """Recursively search through nested knowledge structures"""
        
        matches = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # Check if key matches query
                if any(word in key.lower() for word in query.split()):
                    relevance = self._calculate_relevance(query, key)
                    matches.append({
                        'path': current_path,
                        'key': key,
                        'value': value,
                        'relevance': relevance,
                        'type': 'key_match'
                    })
                
                # Recursively search value
                if isinstance(value, (dict, list)):
                    sub_matches = self._recursive_search(value, query, current_path)
                    matches.extend(sub_matches)
                elif isinstance(value, str):
                    # Check if value content matches query
                    if any(word in value.lower() for word in query.split()):
                        relevance = self._calculate_relevance(query, value)
                        matches.append({
                            'path': current_path,
                            'key': key,
                            'value': value,
                            'relevance': relevance,
                            'type': 'content_match'
                        })
        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                if isinstance(item, str):
                    if any(word in item.lower() for word in query.split()):
                        relevance = self._calculate_relevance(query, item)
                        matches.append({
                            'path': current_path,
                            'key': f"item_{i}",
                            'value': item,
                            'relevance': relevance,
                            'type': 'list_match'
                        })
                else:
                    sub_matches = self._recursive_search(item, query, current_path)
                    matches.extend(sub_matches)
        
        return matches
    
    def _calculate_relevance(self, query: str, text: str) -> float:
        """Calculate relevance score between query and text"""
        
        query_words = set(query.lower().split())
        text_words = set(text.lower().split())
        
        if not query_words or not text_words:
            return 0.0
        
        # Jaccard similarity
        intersection = query_words.intersection(text_words)
        union = query_words.union(text_words)
        
        jaccard = len(intersection) / len(union) if union else 0.0
        
        # Boost score for exact matches
        exact_matches = sum(1 for word in query_words if word in text.lower())
        exact_boost = exact_matches / len(query_words)
        
        return min(1.0, jaccard + exact_boost * 0.3)
    
    def _search_online_knowledge(self, query: str, domain: str) -> List[Dict[str, Any]]:
        """Search online knowledge sources (with fallback)"""
        
        # For now, return empty list (can be extended with actual API calls)
        # This would integrate with Wikipedia API, Wolfram Alpha, etc.
        
        return []
    
    def load_cache(self):
        """Load knowledge cache from file"""
        try:
            if Path(self.cache_file).exists():
                with open(self.cache_file, 'r') as f:
                    self.knowledge_cache = json.load(f)
        except Exception as e:
            print(f"⚠️ Could not load cache: {e}")
            self.knowledge_cache = {}
    
    def save_cache(self):
        """Save knowledge cache to file"""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self.knowledge_cache, f, indent=2)
        except Exception as e:
            print(f"⚠️ Could not save cache: {e}")

class RAGEnhancedReasoning:
    """RAG-enhanced reasoning system"""
    
    def __init__(self, model):
        self.model = model
        self.knowledge_retriever = KnowledgeRetriever()
        self.rag_history = []
        
        print("🧠 RAG-Enhanced Reasoning System initialized")
    
    def solve_with_rag(self, problem: str, problem_type: str = "general") -> Dict[str, Any]:
        """Solve problem using RAG-enhanced reasoning"""
        
        print(f"📚 Solving with RAG: {problem[:50]}...")
        
        # Step 1: Analyze problem and identify knowledge needs
        knowledge_needs = self._analyze_knowledge_needs(problem, problem_type)
        
        # Step 2: Retrieve relevant knowledge
        retrieved_knowledge = []
        for need in knowledge_needs:
            knowledge = self.knowledge_retriever.retrieve_knowledge(need['query'], need['domain'])
            if knowledge['success']:
                retrieved_knowledge.append(knowledge)
        
        # Step 3: Generate enhanced solution using retrieved knowledge
        enhanced_solution = self._generate_rag_solution(problem, retrieved_knowledge, problem_type)
        
        # Step 4: Record RAG usage
        rag_record = {
            'problem': problem,
            'problem_type': problem_type,
            'knowledge_needs': knowledge_needs,
            'retrieved_knowledge': retrieved_knowledge,
            'solution': enhanced_solution,
            'timestamp': time.time()
        }
        
        self.rag_history.append(rag_record)
        
        return enhanced_solution
    
    def _analyze_knowledge_needs(self, problem: str, problem_type: str) -> List[Dict[str, str]]:
        """Analyze what knowledge is needed to solve the problem"""
        
        needs = []
        problem_lower = problem.lower()
        
        # Mathematical knowledge needs
        if any(term in problem_lower for term in ['derivative', 'differentiate', 'dx']):
            needs.append({'query': 'derivative rules', 'domain': 'calculus'})
        
        if any(term in problem_lower for term in ['integral', 'integrate', '∫']):
            needs.append({'query': 'integration rules', 'domain': 'calculus'})
        
        if any(term in problem_lower for term in ['limit', 'lim', 'approaches']):
            needs.append({'query': 'limit rules', 'domain': 'calculus'})
        
        # Physics knowledge needs
        if any(term in problem_lower for term in ['force', 'acceleration', 'velocity', 'motion']):
            needs.append({'query': 'mechanics laws', 'domain': 'physics'})
        
        if any(term in problem_lower for term in ['speed of light', 'gravity', 'constant']):
            needs.append({'query': 'physical constants', 'domain': 'physics'})
        
        # Logic knowledge needs
        if any(term in problem_lower for term in ['implies', 'if then', 'syllogism', 'logic']):
            needs.append({'query': 'logical rules', 'domain': 'logic'})
        
        if any(term in problem_lower for term in ['set', 'union', 'intersection']):
            needs.append({'query': 'set theory', 'domain': 'logic'})
        
        # Computer science knowledge needs
        if any(term in problem_lower for term in ['algorithm', 'complexity', 'big o']):
            needs.append({'query': 'algorithm complexity', 'domain': 'computer_science'})
        
        # General knowledge if no specific domain identified
        if not needs:
            needs.append({'query': problem, 'domain': 'general'})
        
        return needs
    
    def _generate_rag_solution(self, problem: str, knowledge: List[Dict[str, Any]], problem_type: str) -> Dict[str, Any]:
        """Generate solution using problem + retrieved knowledge"""
        
        # Compile knowledge context
        knowledge_context = self._compile_knowledge_context(knowledge)
        
        # Create enhanced prompt
        enhanced_prompt = f"""
        Problem: {problem}
        Type: {problem_type}
        
        Relevant Knowledge:
        {knowledge_context}
        
        Using the knowledge provided above, solve this problem step by step.
        Apply the relevant rules, formulas, or principles.
        Show your reasoning clearly and provide a final answer.
        """
        
        try:
            # Generate solution using model + knowledge
            response = self.model.generate(enhanced_prompt, max_length=250)
            
            return {
                'problem': problem,
                'final_answer': response,
                'knowledge_used': len(knowledge),
                'knowledge_sources': [k.get('domain', 'unknown') for k in knowledge],
                'confidence': 0.9,  # Higher confidence with knowledge augmentation
                'enhancement': 'rag_enhanced',
                'success': True
            }
            
        except Exception as e:
            return {
                'problem': problem,
                'error': str(e),
                'knowledge_used': len(knowledge),
                'success': False
            }
    
    def _compile_knowledge_context(self, knowledge_list: List[Dict[str, Any]]) -> str:
        """Compile retrieved knowledge into context string"""
        
        context_parts = []
        
        for knowledge in knowledge_list:
            if knowledge.get('success', False):
                offline_results = knowledge.get('offline_results', [])
                
                for result in offline_results:
                    if result.get('relevance', 0) > 0.3:  # Only include relevant results
                        context_parts.append(f"- {result['key']}: {result['value']}")
        
        if context_parts:
            return "\n".join(context_parts)
        else:
            return "No specific knowledge retrieved for this problem."
    
    def get_rag_statistics(self) -> Dict[str, Any]:
        """Get RAG usage statistics"""
        
        if not self.rag_history:
            return {'total_problems': 0}
        
        total_problems = len(self.rag_history)
        successful_rag = len([r for r in self.rag_history if r['solution']['success']])
        
        knowledge_domains = {}
        total_knowledge_used = 0
        
        for record in self.rag_history:
            total_knowledge_used += record['solution'].get('knowledge_used', 0)
            for domain in record['solution'].get('knowledge_sources', []):
                knowledge_domains[domain] = knowledge_domains.get(domain, 0) + 1
        
        return {
            'total_problems': total_problems,
            'successful_rag': successful_rag,
            'success_rate': successful_rag / total_problems if total_problems > 0 else 0,
            'avg_knowledge_per_problem': total_knowledge_used / total_problems if total_problems > 0 else 0,
            'knowledge_domains_used': knowledge_domains,
            'cache_entries': len(self.knowledge_retriever.knowledge_cache)
        }
