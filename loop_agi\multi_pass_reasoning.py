#!/usr/bin/env python3
"""
Multi-Pass Reasoning System
Implements chain-of-thought optimization with iterative refinement
Goal: Boost intelligence from 50.9% to 65-70%
"""

import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# Add loop_singular_bit to path
sys.path.append(str(Path(__file__).parent.parent / 'loop_singular_bit'))

class MultiPassReasoning:
    """Multi-pass reasoning system for enhanced intelligence"""
    
    def __init__(self, model):
        self.model = model
        self.reasoning_history = []
        
    def solve_with_multi_pass(self, problem: str, problem_type: str = "general") -> Dict[str, Any]:
        """Solve problem using multi-pass reasoning"""
        
        print(f"🧠 Starting multi-pass reasoning for: {problem[:50]}...")
        
        # Pass 1: Initial analysis
        pass1_result = self._pass1_initial_analysis(problem, problem_type)
        
        # Pass 2: Detailed reasoning
        pass2_result = self._pass2_detailed_reasoning(problem, pass1_result, problem_type)
        
        # Pass 3: Verification and refinement
        pass3_result = self._pass3_verification(problem, pass1_result, pass2_result, problem_type)
        
        # Synthesize final answer
        final_result = self._synthesize_final_answer(problem, pass1_result, pass2_result, pass3_result)
        
        # Store reasoning history
        reasoning_record = {
            'problem': problem,
            'problem_type': problem_type,
            'pass1': pass1_result,
            'pass2': pass2_result,
            'pass3': pass3_result,
            'final_answer': final_result,
            'timestamp': time.time()
        }
        
        self.reasoning_history.append(reasoning_record)
        
        return final_result
    
    def _pass1_initial_analysis(self, problem: str, problem_type: str) -> str:
        """Pass 1: Initial problem analysis and approach identification"""
        
        prompt = f"""
        PASS 1 - INITIAL ANALYSIS:
        
        Problem: {problem}
        Type: {problem_type}
        
        Analyze this problem step by step:
        1. What type of problem is this?
        2. What information is given?
        3. What is being asked?
        4. What approach should I use?
        5. What are the key steps needed?
        
        Provide a clear analysis and initial approach.
        """
        
        try:
            response = self.model.generate(prompt, max_length=150)
            print(f"✅ Pass 1 completed: Initial analysis")
            return response
        except Exception as e:
            print(f"⚠️ Pass 1 failed: {e}")
            return f"Pass 1 analysis failed: {e}"
    
    def _pass2_detailed_reasoning(self, problem: str, pass1_result: str, problem_type: str) -> str:
        """Pass 2: Detailed step-by-step reasoning"""
        
        prompt = f"""
        PASS 2 - DETAILED REASONING:
        
        Problem: {problem}
        
        Initial Analysis: {pass1_result}
        
        Now solve this problem step by step:
        1. Apply the approach identified in Pass 1
        2. Show all calculations and reasoning steps
        3. Work through each step carefully
        4. Explain your reasoning at each step
        
        Provide detailed solution with clear steps.
        """
        
        try:
            response = self.model.generate(prompt, max_length=200)
            print(f"✅ Pass 2 completed: Detailed reasoning")
            return response
        except Exception as e:
            print(f"⚠️ Pass 2 failed: {e}")
            return f"Pass 2 reasoning failed: {e}"
    
    def _pass3_verification(self, problem: str, pass1_result: str, pass2_result: str, problem_type: str) -> str:
        """Pass 3: Verification and refinement"""
        
        prompt = f"""
        PASS 3 - VERIFICATION:
        
        Problem: {problem}
        
        Initial Analysis: {pass1_result}
        Detailed Solution: {pass2_result}
        
        Verify and refine the solution:
        1. Check if the solution makes sense
        2. Verify calculations if applicable
        3. Consider alternative approaches
        4. Identify any errors or improvements
        5. Provide the most accurate answer
        
        Give final verified answer with confidence level.
        """
        
        try:
            response = self.model.generate(prompt, max_length=150)
            print(f"✅ Pass 3 completed: Verification")
            return response
        except Exception as e:
            print(f"⚠️ Pass 3 failed: {e}")
            return f"Pass 3 verification failed: {e}"
    
    def _synthesize_final_answer(self, problem: str, pass1: str, pass2: str, pass3: str) -> Dict[str, Any]:
        """Synthesize final answer from all passes"""
        
        # Extract key information from each pass
        final_answer = self._extract_answer(pass3)
        if not final_answer:
            final_answer = self._extract_answer(pass2)
        if not final_answer:
            final_answer = self._extract_answer(pass1)
        
        # Calculate confidence based on consistency across passes
        confidence = self._calculate_confidence(pass1, pass2, pass3)
        
        return {
            'problem': problem,
            'final_answer': final_answer,
            'confidence': confidence,
            'reasoning_passes': 3,
            'pass1_analysis': pass1,
            'pass2_reasoning': pass2,
            'pass3_verification': pass3,
            'multi_pass_used': True
        }
    
    def _extract_answer(self, response: str) -> str:
        """Extract the main answer from a response"""
        
        # Look for common answer patterns
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if any(marker in line.lower() for marker in ['answer:', 'solution:', 'result:', 'final:']):
                return line
            elif line and len(line) < 100:  # Short, likely answer line
                return line
        
        # Fallback: return first substantial line
        for line in lines:
            line = line.strip()
            if line and len(line) > 10:
                return line
        
        return response[:100] + "..." if len(response) > 100 else response
    
    def _calculate_confidence(self, pass1: str, pass2: str, pass3: str) -> float:
        """Calculate confidence based on consistency across passes"""
        
        # Simple consistency check
        answers = [self._extract_answer(pass1), self._extract_answer(pass2), self._extract_answer(pass3)]
        
        # Check for common elements
        common_elements = 0
        total_comparisons = 0
        
        for i in range(len(answers)):
            for j in range(i+1, len(answers)):
                total_comparisons += 1
                # Simple similarity check
                if any(word in answers[j].lower() for word in answers[i].lower().split() if len(word) > 3):
                    common_elements += 1
        
        if total_comparisons > 0:
            consistency = common_elements / total_comparisons
            return min(0.9, 0.5 + consistency * 0.4)  # 0.5-0.9 range
        
        return 0.6  # Default confidence
    
    def get_reasoning_statistics(self) -> Dict[str, Any]:
        """Get statistics on multi-pass reasoning performance"""
        
        if not self.reasoning_history:
            return {'total_problems': 0}
        
        total_problems = len(self.reasoning_history)
        avg_confidence = sum(r['final_answer']['confidence'] for r in self.reasoning_history) / total_problems
        
        problem_types = {}
        for record in self.reasoning_history:
            ptype = record['problem_type']
            problem_types[ptype] = problem_types.get(ptype, 0) + 1
        
        return {
            'total_problems': total_problems,
            'average_confidence': avg_confidence,
            'problem_types': problem_types,
            'multi_pass_enabled': True
        }

class EnhancedIntelligenceBenchmarks:
    """Enhanced benchmarks using multi-pass reasoning"""
    
    def __init__(self, model):
        self.model = model
        self.multi_pass = MultiPassReasoning(model)
        
    def test_mathematical_reasoning_enhanced(self) -> Dict[str, Any]:
        """Test mathematical reasoning with multi-pass"""
        
        problems = [
            {"problem": "Solve: 2x + 5 = 17", "answer": "6", "difficulty": 1},
            {"problem": "Find derivative of x^3 + 2x^2 - 5x + 1", "answer": "3x^2 + 4x - 5", "difficulty": 2},
            {"problem": "Integrate: ∫(2x + 3)dx", "answer": "x^2 + 3x + C", "difficulty": 2},
            {"problem": "Solve system: 2x + y = 7, x - y = 2", "answer": "x=3, y=1", "difficulty": 3},
            {"problem": "Find limit: lim(x→0) sin(x)/x", "answer": "1", "difficulty": 4}
        ]
        
        correct = 0
        total_difficulty = 0
        results = []
        
        print("🧮 Testing mathematical reasoning with multi-pass...")
        
        for prob in problems:
            print(f"\n--- Problem: {prob['problem']} ---")
            
            # Use multi-pass reasoning
            multi_pass_result = self.multi_pass.solve_with_multi_pass(
                prob['problem'], 
                "mathematical"
            )
            
            # Check answer
            answer_text = multi_pass_result['final_answer']
            answer_found = any(key in answer_text.lower() for key in prob['answer'].lower().split())
            
            if answer_found:
                correct += 1
                total_difficulty += prob['difficulty']
                print(f"✅ Correct answer found")
            else:
                print(f"❌ Incorrect answer")
            
            results.append({
                'problem': prob['problem'],
                'expected': prob['answer'],
                'multi_pass_answer': answer_text,
                'confidence': multi_pass_result['confidence'],
                'correct': answer_found,
                'difficulty': prob['difficulty'],
                'reasoning_passes': 3
            })
        
        score = (correct / len(problems)) * 100
        weighted_score = (total_difficulty / sum(p['difficulty'] for p in problems)) * 100
        
        return {
            'test_type': 'mathematical_reasoning_enhanced',
            'score': score,
            'weighted_score': weighted_score,
            'correct': correct,
            'total': len(problems),
            'results': results,
            'enhancement': 'multi_pass_reasoning'
        }
    
    def test_logical_reasoning_enhanced(self) -> Dict[str, Any]:
        """Test logical reasoning with multi-pass"""
        
        problems = [
            {
                "problem": "If all cats are mammals, and Fluffy is a cat, what is Fluffy?",
                "answer": "mammal",
                "difficulty": 1
            },
            {
                "problem": "A train leaves at 2 PM going 60 mph. Another leaves at 3 PM going 80 mph. When do they meet if 240 miles apart?",
                "answer": "5 PM",
                "difficulty": 3
            },
            {
                "problem": "If A implies B, and B implies C, and A is true, what can we conclude about C?",
                "answer": "true",
                "difficulty": 2
            },
            {
                "problem": "In a group of 100 people, 70 like coffee, 60 like tea. How many like both if everyone likes at least one?",
                "answer": "30",
                "difficulty": 4
            }
        ]
        
        correct = 0
        total_difficulty = 0
        results = []
        
        print("🧠 Testing logical reasoning with multi-pass...")
        
        for prob in problems:
            print(f"\n--- Problem: {prob['problem']} ---")
            
            # Use multi-pass reasoning
            multi_pass_result = self.multi_pass.solve_with_multi_pass(
                prob['problem'], 
                "logical"
            )
            
            # Check answer
            answer_text = multi_pass_result['final_answer']
            answer_found = prob['answer'].lower() in answer_text.lower()
            
            if answer_found:
                correct += 1
                total_difficulty += prob['difficulty']
                print(f"✅ Correct answer found")
            else:
                print(f"❌ Incorrect answer")
            
            results.append({
                'problem': prob['problem'],
                'expected': prob['answer'],
                'multi_pass_answer': answer_text,
                'confidence': multi_pass_result['confidence'],
                'correct': answer_found,
                'difficulty': prob['difficulty'],
                'reasoning_passes': 3
            })
        
        score = (correct / len(problems)) * 100
        weighted_score = (total_difficulty / sum(p['difficulty'] for p in problems)) * 100
        
        return {
            'test_type': 'logical_reasoning_enhanced',
            'score': score,
            'weighted_score': weighted_score,
            'correct': correct,
            'total': len(problems),
            'results': results,
            'enhancement': 'multi_pass_reasoning'
        }
    
    def run_enhanced_benchmark_suite(self) -> Dict[str, Any]:
        """Run complete enhanced benchmark suite"""
        
        print("🚀 Running Enhanced Intelligence Benchmarks with Multi-Pass Reasoning")
        print("=" * 70)
        
        start_time = time.time()
        
        # Run enhanced tests
        math_results = self.test_mathematical_reasoning_enhanced()
        logic_results = self.test_logical_reasoning_enhanced()
        
        end_time = time.time()
        
        # Calculate overall enhanced score
        all_tests = [math_results, logic_results]
        overall_score = sum(test['weighted_score'] for test in all_tests) / len(all_tests)
        
        # Determine enhanced classification
        if overall_score >= 90:
            classification = "EXPERT_LEVEL"
        elif overall_score >= 75:
            classification = "ADVANCED"
        elif overall_score >= 60:
            classification = "INTERMEDIATE"
        elif overall_score >= 40:
            classification = "BASIC"
        else:
            classification = "DEVELOPING"
        
        # Get multi-pass statistics
        multi_pass_stats = self.multi_pass.get_reasoning_statistics()
        
        return {
            'timestamp': time.time(),
            'overall_score': overall_score,
            'classification': classification,
            'test_duration': end_time - start_time,
            'enhancement_type': 'multi_pass_reasoning',
            'detailed_results': {
                'mathematical_reasoning_enhanced': math_results,
                'logical_reasoning_enhanced': logic_results
            },
            'summary': {
                'total_problems': sum(test['total'] for test in all_tests),
                'total_correct': sum(test['correct'] for test in all_tests),
                'accuracy': sum(test['correct'] for test in all_tests) / sum(test['total'] for test in all_tests) * 100
            },
            'multi_pass_statistics': multi_pass_stats
        }
