#!/usr/bin/env python3
"""
Test script for the Financial Agent web API endpoints.
"""
import os
import sys
import asyncio
import logging
import aiohttp
import json
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 8000))
BASE_URL = f"http://{HOST}:{PORT}"

class WebAPITester:
    """Test the Financial Agent web API endpoints."""
    
    def __init__(self):
        """Initialize the test client."""
        self.session = None
    
    async def setup(self):
        """Set up the test client."""
        self.session = aiohttp.ClientSession()
    
    async def teardown(self):
        """Tear down the test client."""
        if self.session:
            await self.session.close()
    
    async def test_root_endpoint(self):
        """Test the root endpoint."""
        logger.info("\n=== Testing Root Endpoint ===")
        url = f"{BASE_URL}/"
        
        try:
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error(f"❌ Root endpoint failed with status {response.status}")
                    return False
                
                content_type = response.headers.get('content-type', '')
                if 'text/html' in content_type:
                    logger.info("✅ Root endpoint returned HTML (expected)")
                    return True
                else:
                    logger.warning(f"Root endpoint returned unexpected content type: {content_type}")
                    return False
        except Exception as e:
            logger.error(f"❌ Error testing root endpoint: {e}")
            return False
    
    async def test_api_endpoint(self, endpoint: str, method: str = "GET", data: dict = None):
        """Test an API endpoint."""
        url = f"{BASE_URL}{endpoint}"
        logger.info(f"Testing {method} {url}")
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    if response.status != 200:
                        logger.error(f"❌ {endpoint} failed with status {response.status}")
                        return None
                    return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    if response.status != 200:
                        logger.error(f"❌ {endpoint} failed with status {response.status}")
                        return None
                    return await response.json()
            else:
                logger.error(f"❌ Unsupported HTTP method: {method}")
                return None
        except Exception as e:
            logger.error(f"❌ Error testing {endpoint}: {e}")
            return None
    
    async def run_tests(self):
        """Run all tests."""
        logger.info("Starting web API tests...")
        
        # Test root endpoint
        if not await self.test_root_endpoint():
            return False
        
        # Test API endpoints
        endpoints = [
            ("/api/portfolio", "GET"),
            ("/api/trades", "GET"),
            ("/api/risk", "GET"),
            ("/api/performance", "GET")
        ]
        
        all_passed = True
        for endpoint, method in endpoints:
            result = await self.test_api_endpoint(endpoint, method)
            if result is not None:
                logger.info(f"✅ {endpoint} returned data: {json.dumps(result, indent=2, default=str)[:200]}...")
            else:
                logger.error(f"❌ {endpoint} test failed")
                all_passed = False
        
        return all_passed

async def main():
    """Run the tests."""
    tester = WebAPITester()
    await tester.setup()
    try:
        success = await tester.run_tests()
        if success:
            logger.info("\n✅ All tests passed successfully!")
        else:
            logger.error("\n❌ Some tests failed!")
            sys.exit(1)
    finally:
        await tester.teardown()

if __name__ == "__main__":
    asyncio.run(main())
