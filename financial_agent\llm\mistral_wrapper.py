import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from typing import Dict, List, Optional, Union
import logging
from dataclasses import dataclass
import json
import os

logger = logging.getLogger(__name__)

@dataclass
class GenerationConfig:
    """Configuration for text generation"""
    max_new_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    repetition_penalty: float = 1.1
    do_sample: bool = True
    return_full_text: bool = False

class MistralWrapper:
    """Wrapper for Mistral LLM with financial domain optimizations"""
    
    def __init__(self, model_name: str = "mistralai/Mistral-7B-v0.1", device: str = None):
        """
        Initialize the Mistral wrapper
        
        Args:
            model_name: Name or path of the Mistral model
            device: Device to run the model on ('cuda', 'mps', 'cpu')
        """
        self.model_name = model_name
        self.device = device or ('cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu')
        self.model = None
        self.tokenizer = None
        self.generation_config = GenerationConfig()
        
        logger.info(f"Initializing Mistral wrapper with model: {model_name}")
        logger.info(f"Using device: {self.device}")
    
    def load_model(self, quantize: bool = True):
        """Load the model with optional quantization"""
        try:
            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
            logger.info("Loading model...")
            
            if quantize and self.device == 'cuda':
                logger.info("Using 4-bit quantization")
                bnb_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_quant_type="nf4",
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                )
                
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    quantization_config=bnb_config,
                    device_map="auto",
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    device_map="auto",
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if self.device != 'cpu' else torch.float32
                )
            
            logger.info("Model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            raise
    
    def generate(
        self,
        prompt: str,
        config: Optional[GenerationConfig] = None,
        **kwargs
    ) -> str:
        """
        Generate text from a prompt
        
        Args:
            prompt: Input text prompt
            config: Optional generation configuration
            **kwargs: Additional generation parameters
            
        Returns:
            Generated text
        """
        if not self.model or not self.tokenizer:
            raise RuntimeError("Model and tokenizer must be loaded first")
        
        # Use provided config or default
        config = config or self.generation_config
        
        try:
            # Tokenize input
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=2048
            ).to(self.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=kwargs.get('max_new_tokens', config.max_new_tokens),
                    temperature=kwargs.get('temperature', config.temperature),
                    top_p=kwargs.get('top_p', config.top_p),
                    top_k=kwargs.get('top_k', config.top_k),
                    repetition_penalty=kwargs.get('repetition_penalty', config.repetition_penalty),
                    do_sample=kwargs.get('do_sample', config.do_sample),
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode and clean up
            response = self.tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Generation failed: {str(e)}")
            raise
    
    def financial_analysis(self, market_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """
        Perform financial analysis using the model
        
        Args:
            market_data: Market data to analyze
            analysis_type: Type of analysis to perform
            
        Returns:
            Analysis results
        """
        prompt = self._create_financial_prompt(market_data, analysis_type)
        response = self.generate(prompt)
        return self._parse_financial_response(response, analysis_type)
    
    def _create_financial_prompt(self, market_data: Dict[str, Any], analysis_type: str) -> str:
        """Create a prompt for financial analysis"""
        # This is a simplified example - you'd want to customize this based on your needs
        return f"""
        You are a financial analyst AI. Analyze the following {analysis_type} data:
        
        {json.dumps(market_data, indent=2)}
        
        Provide a detailed analysis including:
        1. Key observations
        2. Potential opportunities
        3. Risk factors
        4. Recommended actions
        """
    
    def _parse_financial_response(self, response: str, analysis_type: str) -> Dict[str, Any]:
        """Parse the model's financial analysis response"""
        # In a real implementation, you'd parse the response into a structured format
        return {
            'analysis_type': analysis_type,
            'raw_response': response,
            'timestamp': datetime.utcnow().isoformat()
        }
