#!/usr/bin/env python3
"""
Meta-Cognitive Monitoring System
Self-reflection, strategy adaptation, and performance optimization
Goal: Continuous improvement and adaptive reasoning
"""

import time
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ConfidenceLevel(Enum):
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9

class MetaCognitiveState(Enum):
    EXPLORING = "exploring"
    EXPLOITING = "exploiting"
    ADAPTING = "adapting"
    REFLECTING = "reflecting"

@dataclass
class PerformanceMetrics:
    """Performance metrics for meta-cognitive analysis"""
    accuracy: float
    confidence: float
    response_time: float
    strategy_diversity: float
    improvement_rate: float

class MetaCognitiveMonitor:
    """Meta-cognitive monitoring and adaptation system"""
    
    def __init__(self):
        self.performance_history = []
        self.strategy_effectiveness = {}
        self.current_state = MetaCognitiveState.EXPLORING
        self.adaptation_threshold = 0.1
        self.reflection_interval = 5  # Reflect every 5 problems
        self.problem_count = 0
        
        # Meta-cognitive parameters
        self.confidence_calibration = {}
        self.strategy_preferences = {}
        self.learning_rate = 0.1
        
        print("🧠 Meta-Cognitive Monitor initialized")
        print(f"   Initial state: {self.current_state.value}")
        print(f"   Reflection interval: {self.reflection_interval} problems")
    
    def monitor_problem_solving(self, problem: str, solution_process: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor and analyze problem-solving process"""
        
        self.problem_count += 1
        
        # Extract performance metrics
        metrics = self._extract_performance_metrics(solution_process)
        
        # Meta-cognitive analysis
        meta_analysis = self._perform_meta_analysis(problem, solution_process, metrics)
        
        # Update internal state
        self._update_cognitive_state(metrics, meta_analysis)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(meta_analysis)
        
        # Record performance
        performance_record = {
            'problem_count': self.problem_count,
            'problem': problem,
            'metrics': metrics,
            'meta_analysis': meta_analysis,
            'cognitive_state': self.current_state.value,
            'recommendations': recommendations,
            'timestamp': time.time()
        }
        
        self.performance_history.append(performance_record)
        
        # Trigger reflection if needed
        if self.problem_count % self.reflection_interval == 0:
            reflection_results = self._perform_reflection()
            performance_record['reflection'] = reflection_results
        
        return performance_record
    
    def _extract_performance_metrics(self, solution_process: Dict[str, Any]) -> PerformanceMetrics:
        """Extract performance metrics from solution process"""
        
        # Accuracy estimation (simplified)
        accuracy = solution_process.get('confidence', 0.5)
        if solution_process.get('success', False):
            accuracy = max(accuracy, 0.7)
        
        # Confidence from solution
        confidence = solution_process.get('confidence', 0.5)
        
        # Response time (simplified)
        response_time = solution_process.get('execution_time', 1.0)
        
        # Strategy diversity
        strategies_used = solution_process.get('strategies_used', 1)
        strategy_diversity = min(1.0, strategies_used / 5.0)  # Normalize to 0-1
        
        # Improvement rate (based on recent performance)
        improvement_rate = self._calculate_improvement_rate()
        
        return PerformanceMetrics(
            accuracy=accuracy,
            confidence=confidence,
            response_time=response_time,
            strategy_diversity=strategy_diversity,
            improvement_rate=improvement_rate
        )
    
    def _calculate_improvement_rate(self) -> float:
        """Calculate recent improvement rate"""
        
        if len(self.performance_history) < 3:
            return 0.0
        
        # Compare recent performance to earlier performance
        recent_performance = [h['metrics'].accuracy for h in self.performance_history[-3:]]
        earlier_performance = [h['metrics'].accuracy for h in self.performance_history[-6:-3]] if len(self.performance_history) >= 6 else [0.5]
        
        recent_avg = statistics.mean(recent_performance)
        earlier_avg = statistics.mean(earlier_performance)
        
        return recent_avg - earlier_avg
    
    def _perform_meta_analysis(self, problem: str, solution_process: Dict[str, Any], metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Perform meta-cognitive analysis of problem-solving"""
        
        analysis = {
            'confidence_calibration': self._analyze_confidence_calibration(metrics),
            'strategy_effectiveness': self._analyze_strategy_effectiveness(solution_process),
            'problem_difficulty': self._estimate_problem_difficulty(problem, metrics),
            'cognitive_load': self._estimate_cognitive_load(solution_process),
            'learning_opportunity': self._identify_learning_opportunity(problem, solution_process, metrics)
        }
        
        return analysis
    
    def _analyze_confidence_calibration(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Analyze how well confidence matches actual performance"""
        
        confidence_accuracy_diff = abs(metrics.confidence - metrics.accuracy)
        
        if confidence_accuracy_diff < 0.1:
            calibration_quality = "well_calibrated"
        elif confidence_accuracy_diff < 0.2:
            calibration_quality = "moderately_calibrated"
        else:
            calibration_quality = "poorly_calibrated"
        
        # Determine if overconfident or underconfident
        if metrics.confidence > metrics.accuracy + 0.1:
            bias = "overconfident"
        elif metrics.confidence < metrics.accuracy - 0.1:
            bias = "underconfident"
        else:
            bias = "well_calibrated"
        
        return {
            'quality': calibration_quality,
            'bias': bias,
            'difference': confidence_accuracy_diff,
            'confidence': metrics.confidence,
            'accuracy': metrics.accuracy
        }
    
    def _analyze_strategy_effectiveness(self, solution_process: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze effectiveness of strategies used"""

        strategies_used = solution_process.get('strategies_used', [])
        # Handle case where strategies_used might be an integer
        if isinstance(strategies_used, int):
            strategies_used = [f'strategy_{i}' for i in range(strategies_used)]
        elif not isinstance(strategies_used, list):
            strategies_used = ['unknown_strategy']

        best_strategy = solution_process.get('best_strategy', 'unknown')
        success = solution_process.get('success', False)

        # Update strategy effectiveness tracking
        for strategy in strategies_used:
            if strategy not in self.strategy_effectiveness:
                self.strategy_effectiveness[strategy] = {
                    'uses': 0,
                    'successes': 0,
                    'success_rate': 0.0,
                    'avg_confidence': 0.0
                }
            
            self.strategy_effectiveness[strategy]['uses'] += 1
            if success:
                self.strategy_effectiveness[strategy]['successes'] += 1
            
            self.strategy_effectiveness[strategy]['success_rate'] = (
                self.strategy_effectiveness[strategy]['successes'] / 
                self.strategy_effectiveness[strategy]['uses']
            )
        
        # Identify most and least effective strategies
        if self.strategy_effectiveness:
            most_effective = max(self.strategy_effectiveness.items(), 
                               key=lambda x: x[1]['success_rate'])
            least_effective = min(self.strategy_effectiveness.items(), 
                                key=lambda x: x[1]['success_rate'])
        else:
            most_effective = ("unknown", {"success_rate": 0.0})
            least_effective = ("unknown", {"success_rate": 0.0})
        
        return {
            'strategies_used': strategies_used,
            'best_strategy': best_strategy,
            'most_effective_overall': most_effective[0],
            'least_effective_overall': least_effective[0],
            'strategy_diversity': len(set(strategies_used))
        }
    
    def _estimate_problem_difficulty(self, problem: str, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Estimate problem difficulty based on performance"""
        
        # Difficulty indicators
        low_accuracy = metrics.accuracy < 0.6
        low_confidence = metrics.confidence < 0.6
        high_response_time = metrics.response_time > 2.0
        low_strategy_diversity = metrics.strategy_diversity < 0.4
        
        difficulty_score = sum([low_accuracy, low_confidence, high_response_time, low_strategy_diversity]) / 4
        
        if difficulty_score >= 0.75:
            difficulty_level = "very_hard"
        elif difficulty_score >= 0.5:
            difficulty_level = "hard"
        elif difficulty_score >= 0.25:
            difficulty_level = "medium"
        else:
            difficulty_level = "easy"
        
        return {
            'level': difficulty_level,
            'score': difficulty_score,
            'indicators': {
                'low_accuracy': low_accuracy,
                'low_confidence': low_confidence,
                'high_response_time': high_response_time,
                'low_strategy_diversity': low_strategy_diversity
            }
        }
    
    def _estimate_cognitive_load(self, solution_process: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate cognitive load of problem-solving process"""

        strategies_raw = solution_process.get('strategies_used', [])
        if isinstance(strategies_raw, int):
            strategies_used = strategies_raw
        elif isinstance(strategies_raw, list):
            strategies_used = len(strategies_raw)
        else:
            strategies_used = 1

        successful_strategies = solution_process.get('successful_strategies', 0)
        
        # High cognitive load indicators
        many_strategies = strategies_used > 4
        low_success_rate = (successful_strategies / strategies_used) < 0.5 if strategies_used > 0 else False
        
        if many_strategies and low_success_rate:
            load_level = "high"
        elif many_strategies or low_success_rate:
            load_level = "medium"
        else:
            load_level = "low"
        
        return {
            'level': load_level,
            'strategies_used': strategies_used,
            'successful_strategies': successful_strategies,
            'efficiency': successful_strategies / strategies_used if strategies_used > 0 else 0
        }
    
    def _identify_learning_opportunity(self, problem: str, solution_process: Dict[str, Any], metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Identify learning opportunities from problem-solving"""
        
        opportunities = []
        
        # Low accuracy suggests need for better strategies
        if metrics.accuracy < 0.7:
            opportunities.append("improve_accuracy")
        
        # Poor confidence calibration suggests need for better self-assessment
        if abs(metrics.confidence - metrics.accuracy) > 0.2:
            opportunities.append("calibrate_confidence")
        
        # Low strategy diversity suggests need for broader approach
        if metrics.strategy_diversity < 0.4:
            opportunities.append("increase_strategy_diversity")
        
        # Negative improvement rate suggests need for adaptation
        if metrics.improvement_rate < -0.1:
            opportunities.append("adapt_strategies")
        
        return {
            'opportunities': opportunities,
            'priority': len(opportunities),
            'focus_area': opportunities[0] if opportunities else "maintain_performance"
        }
    
    def _update_cognitive_state(self, metrics: PerformanceMetrics, meta_analysis: Dict[str, Any]):
        """Update meta-cognitive state based on analysis"""
        
        # State transition logic
        current_performance = metrics.accuracy
        improvement_rate = metrics.improvement_rate
        
        if self.current_state == MetaCognitiveState.EXPLORING:
            if current_performance > 0.8 and improvement_rate > 0:
                self.current_state = MetaCognitiveState.EXPLOITING
        
        elif self.current_state == MetaCognitiveState.EXPLOITING:
            if improvement_rate < -0.1:
                self.current_state = MetaCognitiveState.ADAPTING
            elif self.problem_count % self.reflection_interval == 0:
                self.current_state = MetaCognitiveState.REFLECTING
        
        elif self.current_state == MetaCognitiveState.ADAPTING:
            if improvement_rate > 0.1:
                self.current_state = MetaCognitiveState.EXPLOITING
            else:
                self.current_state = MetaCognitiveState.EXPLORING
        
        elif self.current_state == MetaCognitiveState.REFLECTING:
            self.current_state = MetaCognitiveState.EXPLORING
    
    def _generate_recommendations(self, meta_analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on meta-analysis"""
        
        recommendations = []
        
        # Confidence calibration recommendations
        calibration = meta_analysis['confidence_calibration']
        if calibration['bias'] == 'overconfident':
            recommendations.append("Reduce confidence estimates - you may be overconfident")
        elif calibration['bias'] == 'underconfident':
            recommendations.append("Increase confidence estimates - you may be underconfident")
        
        # Strategy effectiveness recommendations
        strategy_analysis = meta_analysis['strategy_effectiveness']
        if strategy_analysis['strategy_diversity'] < 2:
            recommendations.append("Try using more diverse reasoning strategies")
        
        # Problem difficulty recommendations
        difficulty = meta_analysis['problem_difficulty']
        if difficulty['level'] in ['hard', 'very_hard']:
            recommendations.append("Consider breaking down complex problems into smaller parts")
        
        # Cognitive load recommendations
        cognitive_load = meta_analysis['cognitive_load']
        if cognitive_load['level'] == 'high':
            recommendations.append("Focus on more efficient strategy selection")
        
        # Learning opportunity recommendations
        learning = meta_analysis['learning_opportunity']
        if 'improve_accuracy' in learning['opportunities']:
            recommendations.append("Focus on improving solution accuracy")
        if 'increase_strategy_diversity' in learning['opportunities']:
            recommendations.append("Experiment with different reasoning approaches")
        
        return recommendations
    
    def _perform_reflection(self) -> Dict[str, Any]:
        """Perform periodic reflection on performance"""
        
        if len(self.performance_history) < self.reflection_interval:
            return {'reflection_type': 'insufficient_data'}
        
        recent_history = self.performance_history[-self.reflection_interval:]
        
        # Analyze trends
        accuracy_trend = self._analyze_trend([h['metrics'].accuracy for h in recent_history])
        confidence_trend = self._analyze_trend([h['metrics'].confidence for h in recent_history])
        
        # Identify patterns
        common_difficulties = self._identify_common_difficulties(recent_history)
        effective_strategies = self._identify_effective_strategies(recent_history)
        
        # Generate insights
        insights = self._generate_insights(accuracy_trend, confidence_trend, common_difficulties, effective_strategies)
        
        return {
            'reflection_type': 'periodic',
            'period_analyzed': self.reflection_interval,
            'accuracy_trend': accuracy_trend,
            'confidence_trend': confidence_trend,
            'common_difficulties': common_difficulties,
            'effective_strategies': effective_strategies,
            'insights': insights,
            'timestamp': time.time()
        }
    
    def _analyze_trend(self, values: List[float]) -> str:
        """Analyze trend in a series of values"""
        
        if len(values) < 2:
            return "insufficient_data"
        
        # Simple linear trend analysis
        x = list(range(len(values)))
        n = len(values)
        
        # Calculate slope
        sum_x = sum(x)
        sum_y = sum(values)
        sum_xy = sum(x[i] * values[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        if slope > 0.05:
            return "improving"
        elif slope < -0.05:
            return "declining"
        else:
            return "stable"
    
    def _identify_common_difficulties(self, history: List[Dict[str, Any]]) -> List[str]:
        """Identify common difficulties in recent performance"""
        
        difficulties = []
        
        # Check for consistent low accuracy
        low_accuracy_count = sum(1 for h in history if h['metrics'].accuracy < 0.7)
        if low_accuracy_count > len(history) * 0.6:
            difficulties.append("consistent_low_accuracy")
        
        # Check for poor confidence calibration
        poor_calibration_count = sum(1 for h in history 
                                   if abs(h['metrics'].confidence - h['metrics'].accuracy) > 0.2)
        if poor_calibration_count > len(history) * 0.5:
            difficulties.append("poor_confidence_calibration")
        
        return difficulties
    
    def _identify_effective_strategies(self, history: List[Dict[str, Any]]) -> List[str]:
        """Identify most effective strategies in recent performance"""
        
        strategy_success = {}
        
        for h in history:
            best_strategy = h.get('meta_analysis', {}).get('strategy_effectiveness', {}).get('best_strategy', 'unknown')
            if best_strategy != 'unknown':
                if best_strategy not in strategy_success:
                    strategy_success[best_strategy] = 0
                strategy_success[best_strategy] += 1
        
        # Return strategies used most often as "best"
        effective_strategies = sorted(strategy_success.items(), key=lambda x: x[1], reverse=True)
        
        return [strategy for strategy, count in effective_strategies[:3]]
    
    def _generate_insights(self, accuracy_trend: str, confidence_trend: str, 
                          difficulties: List[str], effective_strategies: List[str]) -> List[str]:
        """Generate insights from reflection analysis"""
        
        insights = []
        
        # Trend insights
        if accuracy_trend == "improving":
            insights.append("Performance is improving - continue current approach")
        elif accuracy_trend == "declining":
            insights.append("Performance is declining - consider strategy changes")
        
        # Difficulty insights
        if "consistent_low_accuracy" in difficulties:
            insights.append("Focus on improving solution accuracy")
        if "poor_confidence_calibration" in difficulties:
            insights.append("Work on better self-assessment of confidence")
        
        # Strategy insights
        if effective_strategies:
            insights.append(f"Most effective strategies: {', '.join(effective_strategies[:2])}")
        
        return insights
    
    def get_meta_cognitive_state(self) -> Dict[str, Any]:
        """Get current meta-cognitive state and statistics"""
        
        if not self.performance_history:
            return {'state': self.current_state.value, 'problems_processed': 0}
        
        recent_performance = self.performance_history[-5:] if len(self.performance_history) >= 5 else self.performance_history
        
        avg_accuracy = statistics.mean(h['metrics'].accuracy for h in recent_performance)
        avg_confidence = statistics.mean(h['metrics'].confidence for h in recent_performance)
        
        return {
            'current_state': self.current_state.value,
            'problems_processed': self.problem_count,
            'recent_avg_accuracy': avg_accuracy,
            'recent_avg_confidence': avg_confidence,
            'strategy_effectiveness': self.strategy_effectiveness,
            'reflection_count': len([h for h in self.performance_history if 'reflection' in h])
        }
