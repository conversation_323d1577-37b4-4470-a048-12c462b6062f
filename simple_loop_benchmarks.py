#!/usr/bin/env python3
"""
Simple Loop Singular Bit Benchmarks
Direct testing of the Loop Singular Bit system
"""

import os
import sys
import time
import json
import psutil
from datetime import datetime

def test_loop_singular_bit():
    """Test the actual Loop Singular Bit system"""
    
    print("🔬 SIMPLE LOOP SINGULAR BIT BENCHMARKS")
    print("=" * 50)
    print("📊 Testing actual system performance")
    print()
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'tests': {},
        'overall_status': 'UNKNOWN'
    }
    
    # Test 1: System Import and Initialization
    print("📦 Test 1: System Import and Initialization")
    print("-" * 45)
    
    try:
        # Import the system directly
        sys.path.append('loop_singular_bit')
        
        # Import main module
        import loop_singular_bit as lsb
        
        print("✅ Module imported successfully")
        
        # Test system info
        info = lsb.get_system_info()
        print(f"✅ System version: {info.get('version', 'Unknown')}")
        print(f"✅ System status: {info.get('status', 'Unknown')}")
        
        results['tests']['import_test'] = {
            'status': 'PASSED',
            'version': info.get('version', 'Unknown'),
            'capabilities': info.get('capabilities', {})
        }
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        results['tests']['import_test'] = {
            'status': 'FAILED',
            'error': str(e)
        }
        return results
    
    # Test 2: Model Loading
    print(f"\n🤖 Test 2: Model Loading")
    print("-" * 25)
    
    try:
        # Load compressed model
        start_time = time.time()
        model = lsb.load_compressed_model("mistral-7b-v0.1")
        load_time = time.time() - start_time
        
        if model:
            model_info = model.get_info()
            print(f"✅ Model loaded in {load_time:.2f}s")
            print(f"✅ Compression ratio: {model_info.get('compression_ratio', 'Unknown')}")
            print(f"✅ RAM usage: {model_info.get('ram_usage_mb', 'Unknown')}MB")
            print(f"✅ Real compression: {model.is_real}")
            
            results['tests']['model_loading'] = {
                'status': 'PASSED',
                'load_time': load_time,
                'model_info': model_info,
                'is_real': model.is_real
            }
        else:
            print("❌ Model loading returned None")
            results['tests']['model_loading'] = {
                'status': 'FAILED',
                'error': 'Model loading returned None'
            }
            return results
            
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        results['tests']['model_loading'] = {
            'status': 'FAILED',
            'error': str(e)
        }
        return results
    
    # Test 3: Text Generation
    print(f"\n🔮 Test 3: Text Generation")
    print("-" * 27)
    
    try:
        # Test prompts
        test_prompts = [
            "The future of AI is",
            "Machine learning can",
            "Neural networks are"
        ]
        
        generation_results = []
        
        for i, prompt in enumerate(test_prompts):
            print(f"📝 Testing prompt {i+1}: '{prompt}'")
            
            start_time = time.time()
            output = model.generate(prompt, max_length=30)
            generation_time = time.time() - start_time
            
            print(f"   ✅ Generated in {generation_time:.3f}s")
            print(f"   📝 Output: {output[:100]}...")
            
            generation_results.append({
                'prompt': prompt,
                'output': output,
                'generation_time': generation_time,
                'success': True
            })
        
        avg_time = sum(r['generation_time'] for r in generation_results) / len(generation_results)
        print(f"✅ Average generation time: {avg_time:.3f}s")
        
        results['tests']['text_generation'] = {
            'status': 'PASSED',
            'prompts_tested': len(test_prompts),
            'average_time': avg_time,
            'results': generation_results
        }
        
    except Exception as e:
        print(f"❌ Text generation failed: {e}")
        results['tests']['text_generation'] = {
            'status': 'FAILED',
            'error': str(e)
        }
    
    # Test 4: Memory Usage
    print(f"\n💾 Test 4: Memory Usage")
    print("-" * 22)
    
    try:
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        print(f"✅ Current memory usage: {memory_mb:.1f}MB")
        
        # Test memory during generation
        start_memory = process.memory_info().rss / 1024 / 1024
        output = model.generate("Memory test prompt", max_length=50)
        end_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = end_memory - start_memory
        
        print(f"✅ Memory increase during generation: {memory_increase:.1f}MB")
        print(f"✅ Total memory usage: {end_memory:.1f}MB")
        
        results['tests']['memory_usage'] = {
            'status': 'PASSED',
            'current_memory_mb': memory_mb,
            'generation_memory_increase_mb': memory_increase,
            'total_memory_mb': end_memory
        }
        
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        results['tests']['memory_usage'] = {
            'status': 'FAILED',
            'error': str(e)
        }
    
    # Test 5: Performance Metrics
    print(f"\n⚡ Test 5: Performance Metrics")
    print("-" * 30)
    
    try:
        # Multiple load time tests
        load_times = []
        for i in range(3):
            start_time = time.time()
            test_model = lsb.load_compressed_model("mistral-7b-v0.1")
            load_time = time.time() - start_time
            load_times.append(load_time)
            print(f"   Load test {i+1}: {load_time:.3f}s")
        
        avg_load_time = sum(load_times) / len(load_times)
        print(f"✅ Average load time: {avg_load_time:.3f}s")
        
        # Generation speed test
        start_time = time.time()
        long_output = model.generate("Performance test with longer output", max_length=100)
        long_generation_time = time.time() - start_time
        
        words_generated = len(long_output.split())
        words_per_second = words_generated / long_generation_time if long_generation_time > 0 else 0
        
        print(f"✅ Long generation time: {long_generation_time:.3f}s")
        print(f"✅ Words generated: {words_generated}")
        print(f"✅ Words per second: {words_per_second:.1f}")
        
        results['tests']['performance'] = {
            'status': 'PASSED',
            'average_load_time': avg_load_time,
            'long_generation_time': long_generation_time,
            'words_per_second': words_per_second,
            'load_times': load_times
        }
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        results['tests']['performance'] = {
            'status': 'FAILED',
            'error': str(e)
        }
    
    # Calculate overall status
    passed_tests = sum(1 for test in results['tests'].values() if test.get('status') == 'PASSED')
    total_tests = len(results['tests'])
    
    if passed_tests == total_tests:
        results['overall_status'] = 'ALL_PASSED'
        status_icon = "🎉"
    elif passed_tests > total_tests / 2:
        results['overall_status'] = 'MOSTLY_PASSED'
        status_icon = "✅"
    else:
        results['overall_status'] = 'MOSTLY_FAILED'
        status_icon = "⚠️"
    
    # Summary
    print(f"\n{status_icon} BENCHMARK SUMMARY")
    print("=" * 30)
    print(f"📊 Tests passed: {passed_tests}/{total_tests}")
    print(f"📊 Overall status: {results['overall_status']}")
    
    # Detailed results
    for test_name, test_result in results['tests'].items():
        status_icon = "✅" if test_result.get('status') == 'PASSED' else "❌"
        print(f"{status_icon} {test_name}: {test_result.get('status', 'UNKNOWN')}")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"loop_benchmarks_{timestamp}.json"
    
    try:
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved to: {results_file}")
    except Exception as e:
        print(f"⚠️ Failed to save results: {e}")
    
    return results

def analyze_results(results):
    """Analyze benchmark results"""
    
    print(f"\n📊 DETAILED ANALYSIS")
    print("=" * 25)
    
    # System capabilities
    import_test = results['tests'].get('import_test', {})
    if import_test.get('status') == 'PASSED':
        capabilities = import_test.get('capabilities', {})
        print(f"🔧 System Capabilities:")
        for cap, status in capabilities.items():
            icon = "✅" if status else "❌"
            print(f"   {icon} {cap}")
    
    # Model performance
    model_test = results['tests'].get('model_loading', {})
    if model_test.get('status') == 'PASSED':
        model_info = model_test.get('model_info', {})
        print(f"\n🤖 Model Performance:")
        print(f"   📊 Compression ratio: {model_info.get('compression_ratio', 'Unknown')}")
        print(f"   💾 RAM usage: {model_info.get('ram_usage_mb', 'Unknown')}MB")
        print(f"   ✨ Quality preservation: {model_info.get('quality_preservation', 'Unknown')}%")
        print(f"   🔧 Real compression: {model_test.get('is_real', False)}")
    
    # Generation performance
    gen_test = results['tests'].get('text_generation', {})
    if gen_test.get('status') == 'PASSED':
        print(f"\n🔮 Generation Performance:")
        print(f"   📝 Prompts tested: {gen_test.get('prompts_tested', 0)}")
        print(f"   ⏱️ Average time: {gen_test.get('average_time', 0):.3f}s")
    
    # Memory efficiency
    mem_test = results['tests'].get('memory_usage', {})
    if mem_test.get('status') == 'PASSED':
        print(f"\n💾 Memory Efficiency:")
        print(f"   📊 Total memory: {mem_test.get('total_memory_mb', 0):.1f}MB")
        print(f"   📈 Generation increase: {mem_test.get('generation_memory_increase_mb', 0):.1f}MB")
    
    # Performance metrics
    perf_test = results['tests'].get('performance', {})
    if perf_test.get('status') == 'PASSED':
        print(f"\n⚡ Performance Metrics:")
        print(f"   🚀 Average load time: {perf_test.get('average_load_time', 0):.3f}s")
        print(f"   📝 Words per second: {perf_test.get('words_per_second', 0):.1f}")

def main():
    """Run simple benchmarks"""
    
    print("🚀 Starting Loop Singular Bit benchmarks...")
    
    # Run tests
    results = test_loop_singular_bit()
    
    # Analyze results
    analyze_results(results)
    
    print(f"\n🎯 BENCHMARK COMPLETE!")
    print(f"📊 Overall Status: {results['overall_status']}")
    
    return results

if __name__ == "__main__":
    results = main()
