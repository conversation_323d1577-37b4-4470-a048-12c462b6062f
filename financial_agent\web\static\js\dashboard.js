// Dashboard JavaScript for Financial Agent

// WebSocket connection
let socket;
let performanceChart;
let allocationChart;
let lastUpdateTime = new Date();

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', { 
        style: 'currency', 
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Format percentage
function formatPercent(value) {
    return new Intl.NumberFormat('en-US', { 
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
}

// Initialize WebSocket connection
function connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    const wsUrl = protocol + window.location.host + '/ws';
    
    socket = new WebSocket(wsUrl);
    
    socket.onopen = function(e) {
        console.log('WebSocket connected');
        document.getElementById('connection-status').className = 'status-indicator status-online';
        updateLastUpdated();
    };
    
    socket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type === 'update') {
                updateDashboard(data.data);
                lastUpdateTime = new Date();
                updateLastUpdated();
            }
        } catch (error) {
            console.error('Error processing WebSocket message:', error);
        }
    };
    
    socket.onclose = function(event) {
        console.log('WebSocket disconnected');
        document.getElementById('connection-status').className = 'status-indicator status-offline';
        
        // Try to reconnect after 5 seconds
        setTimeout(connectWebSocket, 5000);
    };
    
    socket.onerror = function(error) {
        console.error('WebSocket error:', error);
    };
}

// Update last updated time
function updateLastUpdated() {
    const now = new Date();
    const diff = Math.floor((now - lastUpdateTime) / 1000);
    
    let text;
    if (diff < 5) {
        text = 'Just now';
    } else if (diff < 60) {
        text = `${diff} seconds ago`;
    } else if (diff < 3600) {
        const mins = Math.floor(diff / 60);
        text = `${mins} minute${mins > 1 ? 's' : ''} ago`;
    } else {
        text = lastUpdateTime.toLocaleTimeString();
    }
    
    document.getElementById('last-updated').textContent = text;
}

// Update dashboard with new data
function updateDashboard(data) {
    const portfolio = data.portfolio || {};
    const risk = data.risk || {};
    
    // Update portfolio summary
    document.getElementById('portfolio-value').textContent = formatCurrency(portfolio.current_value || 0);
    
    const dailyReturn = portfolio.performance?.daily || 0;
    const dailyPnl = portfolio.current_value * dailyReturn;
    
    document.getElementById('daily-pnl').textContent = formatCurrency(dailyPnl);
    document.getElementById('daily-pnl').className = `text-2xl font-semibold ${dailyPnl >= 0 ? 'text-green-600' : 'text-red-600'}`;
    
    const dailyChangeElement = document.getElementById('daily-change');
    dailyChangeElement.textContent = formatPercent(dailyReturn);
    dailyChangeElement.className = dailyReturn >= 0 ? 'font-medium positive' : 'font-medium negative';
    
    const dailyPnlPctElement = document.getElementById('daily-pnl-pct');
    dailyPnlPctElement.textContent = formatPercent(dailyReturn);
    dailyPnlPctElement.className = dailyReturn >= 0 ? 'font-medium positive' : 'font-medium negative';
    
    // Update positions
    const positions = Object.values(portfolio.positions || {});
    document.getElementById('positions-count').textContent = positions.length;
    document.getElementById('active-positions').textContent = positions.length;
    
    // Update risk level
    const drawdown = (portfolio.peak_value - portfolio.current_value) / portfolio.peak_value * 100 || 0;
    document.getElementById('drawdown').textContent = drawdown.toFixed(2) + '%';
    document.getElementById('risk-level').textContent = getRiskLevel(drawdown);
    
    // Update positions table
    updatePositionsTable(positions, portfolio.current_value);
    
    // Update charts
    updatePerformanceChart(portfolio);
    updateAllocationChart(portfolio);
}

// Get risk level based on drawdown
function getRiskLevel(drawdown) {
    if (drawdown < 2) return 'Low';
    if (drawdown < 5) return 'Moderate';
    if (drawdown < 10) return 'High';
    return 'Extreme';
}

// Update positions table
function updatePositionsTable(positions, portfolioValue) {
    const tbody = document.getElementById('positions-body');
    tbody.innerHTML = '';
    
    positions.forEach(position => {
        const pnl = position.pnl || 0;
        const pnlPct = position.pnl_pct || 0;
        const pctOfPortfolio = (position.value / portfolioValue) * 100;
        
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span class="text-indigo-600 font-medium">${position.symbol[0]}</span>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${position.symbol}</div>
                        <div class="text-sm text-gray-500">${position.sector || 'N/A'}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${position.quantity.toLocaleString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatCurrency(position.entry_price)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatCurrency(position.current_price)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                ${formatCurrency(position.value)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="${pnl >= 0 ? 'text-green-600' : 'text-red-600'} font-medium">
                    ${formatCurrency(pnl)} (${pnlPct.toFixed(2)}%)
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${pctOfPortfolio.toFixed(2)}%
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${(position.beta || 0).toFixed(2)}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Initialize performance chart
function initPerformanceChart() {
    const ctx = document.getElementById('performance-chart').getContext('2d');
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Portfolio Value',
                data: [],
                borderColor: '#4F46E5',
                backgroundColor: 'rgba(79, 70, 229, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

// Update performance chart
function updatePerformanceChart(portfolio) {
    if (!performanceChart) {
        initPerformanceChart();
    }
    
    // Generate sample data for the last 30 days
    const days = 30;
    const labels = [];
    const data = [];
    const baseValue = (portfolio.current_value || 100000) * 0.8; // Start at 80% of current value
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        
        // Add some random variation to the data
        const variation = 1 + (Math.random() * 0.02 - 0.01);
        const prevValue = data.length > 0 ? data[data.length - 1] : baseValue;
        data.push(prevValue * variation);
    }
    
    // Update chart data
    performanceChart.data.labels = labels;
    performanceChart.data.datasets[0].data = data;
    performanceChart.update();
}

// Initialize allocation chart
function initAllocationChart() {
    const ctx = document.getElementById('allocation-chart').getContext('2d');
    allocationChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#4F46E5',
                    '#10B981',
                    '#F59E0B',
                    '#EF4444',
                    '#8B5CF6',
                    '#EC4899',
                    '#06B6D4'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Update allocation chart
function updateAllocationChart(portfolio) {
    if (!allocationChart) {
        initAllocationChart();
    }
    
    const positions = Object.values(portfolio.positions || {});
    
    if (positions.length === 0) {
        // If no positions, show 100% cash
        allocationChart.data.labels = ['Cash'];
        allocationChart.data.datasets[0].data = [portfolio.cash || 0];
        allocationChart.data.datasets[0].backgroundColor = ['#4F46E5'];
    } else {
        // Group by sector
        const sectors = {};
        let totalValue = 0;
        
        // Add cash as a sector
        if (portfolio.cash > 0) {
            sectors['Cash'] = portfolio.cash;
            totalValue += portfolio.cash;
        }
        
        // Add positions by sector
        positions.forEach(position => {
            const sector = position.sector || 'Other';
            sectors[sector] = (sectors[sector] || 0) + position.value;
            totalValue += position.value;
        });
        
        // Convert to chart data
        const labels = [];
        const data = [];
        const backgroundColors = [
            '#4F46E5', '#10B981', '#F59E0B', '#EF4444', 
            '#8B5CF6', '#EC4899', '#06B6D4', '#F97316',
            '#14B8A6', '#8B5CF6', '#EC4899', '#06B6D4'
        ];
        
        Object.entries(sectors).forEach(([sector, value], index) => {
            const pct = (value / totalValue) * 100;
            if (pct >= 1) { // Only show sectors with >1% allocation
                labels.push(`${sector} (${pct.toFixed(1)}%)`);
                data.push(value);
            }
        });
        
        // Update chart data
        allocationChart.data.labels = labels;
        allocationChart.data.datasets[0].data = data;
        allocationChart.data.datasets[0].backgroundColor = backgroundColors.slice(0, labels.length);
    }
    
    allocationChart.update();
    
    // Update legend
    updateAllocationLegend(allocationChart);
}

// Update allocation chart legend
function updateAllocationLegend(chart) {
    const legendContainer = document.getElementById('allocation-legend');
    if (!legendContainer) return;
    
    const ul = document.createElement('ul');
    ul.className = 'space-y-2';
    
    chart.data.labels.forEach((label, i) => {
        const li = document.createElement('li');
        li.className = 'flex items-center text-sm';
        
        const colorBox = document.createElement('span');
        colorBox.className = 'w-3 h-3 rounded-full mr-2';
        colorBox.style.backgroundColor = chart.data.datasets[0].backgroundColor[i];
        
        li.appendChild(colorBox);
        li.appendChild(document.createTextNode(label));
        ul.appendChild(li);
    });
    
    legendContainer.innerHTML = '';
    legendContainer.appendChild(ul);
}

// Initialize the dashboard when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initPerformanceChart();
    initAllocationChart();
    
    // Connect to WebSocket
    connectWebSocket();
    
    // Set up periodic refresh
    setInterval(updateLastUpdated, 1000);
});
