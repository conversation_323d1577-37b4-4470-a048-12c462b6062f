#!/usr/bin/env python3
"""
Command-line interface for Loop 7B SW streaming inference.

Usage:
    python run_inference.py --model_path /path/to/mistral-7b --prompt "Hello world"
    python run_inference.py --model_path /path/to/mistral-7b --prompt "The future of AI is" --max_tokens 20
"""

import argparse
import sys
import os
import time

# Add src to path for development
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from loop_7b_sw import StreamingInference


def main():
    parser = argparse.ArgumentParser(
        description="Loop 7B SW - Streaming Weights Inference",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_inference.py --model_path ./mistral-7b --prompt "Hello world"
  python run_inference.py --model_path ./mistral-7b --prompt "The future of AI is" --max_tokens 20
  python run_inference.py --model_path ./mistral-7b --prompt "Technology will" --memory_limit 4000
        """
    )
    
    parser.add_argument(
        "--model_path",
        type=str,
        required=True,
        help="Path to the model directory (e.g., downloaded Mistral 7B)"
    )
    
    parser.add_argument(
        "--prompt",
        type=str,
        required=True,
        help="Input text prompt for generation"
    )
    
    parser.add_argument(
        "--max_tokens",
        type=int,
        default=10,
        help="Maximum number of tokens to generate (default: 10)"
    )
    
    parser.add_argument(
        "--memory_limit",
        type=int,
        default=8000,
        help="Memory limit in MB (default: 8000 = 8GB)"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--benchmark",
        action="store_true",
        help="Run multiple iterations for benchmarking"
    )
    
    args = parser.parse_args()
    
    # Validate model path
    if not os.path.exists(args.model_path):
        print(f"❌ Error: Model path '{args.model_path}' does not exist")
        sys.exit(1)
    
    # Check for required model files
    required_files = ["config.json", "model.safetensors.index.json"]
    for file_name in required_files:
        file_path = os.path.join(args.model_path, file_name)
        if not os.path.exists(file_path):
            print(f"❌ Error: Required file '{file_name}' not found in model directory")
            sys.exit(1)
    
    print("🧬 Loop 7B SW - Streaming Weights Inference")
    print("=" * 50)
    print(f"Model path: {args.model_path}")
    print(f"Prompt: '{args.prompt}'")
    print(f"Max tokens: {args.max_tokens}")
    print(f"Memory limit: {args.memory_limit}MB")
    print()
    
    try:
        # Initialize streaming inference
        print("🚀 Initializing streaming inference system...")
        model = StreamingInference(args.model_path, memory_limit_mb=args.memory_limit)
        
        if args.benchmark:
            # Run multiple iterations for benchmarking
            print("📊 Running benchmark (3 iterations)...")
            results = []
            
            for i in range(3):
                print(f"\n--- Iteration {i+1}/3 ---")
                result = model.generate(args.prompt, max_tokens=args.max_tokens)
                results.append(result)
                
                if not result.get('success'):
                    print(f"❌ Iteration {i+1} failed: {result.get('error', 'Unknown error')}")
                    continue
            
            # Calculate benchmark statistics
            successful_results = [r for r in results if r.get('success')]
            if successful_results:
                avg_speed = sum(r['tokens_per_second'] for r in successful_results) / len(successful_results)
                avg_memory = sum(r['peak_memory_mb'] for r in successful_results) / len(successful_results)
                avg_time = sum(r['inference_time_s'] for r in successful_results) / len(successful_results)
                
                print(f"\n📊 BENCHMARK RESULTS:")
                print(f"   Successful runs: {len(successful_results)}/3")
                print(f"   Average speed: {avg_speed:.2f} tokens/sec")
                print(f"   Average memory: {avg_memory:.1f}MB")
                print(f"   Average time: {avg_time:.2f}s")
            else:
                print("❌ All benchmark iterations failed")
        
        else:
            # Single inference run
            result = model.generate(args.prompt, max_tokens=args.max_tokens)
            
            if result.get('success'):
                print(f"\n✅ SUCCESS!")
                print(f"Generated text: '{result['generated_text']}'")
                print(f"Tokens generated: {result['tokens_generated']}")
                print(f"Speed: {result['tokens_per_second']:.2f} tokens/sec")
                print(f"Memory usage: {result['peak_memory_mb']:.1f}MB")
                print(f"Under memory limit: {'✅ YES' if result['memory_under_8gb'] else '❌ NO'}")
                
                if args.verbose:
                    print(f"\nDetailed metrics:")
                    print(f"   Start memory: {result['start_memory_mb']:.1f}MB")
                    print(f"   Peak memory: {result['peak_memory_mb']:.1f}MB")
                    print(f"   Memory increase: {result['peak_memory_mb'] - result['start_memory_mb']:.1f}MB")
                    print(f"   Inference time: {result['inference_time_s']:.2f}s")
                    print(f"   Weights loaded: {result['weights_loaded']}")
            else:
                print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
                sys.exit(1)
        
        # Show cumulative stats
        stats = model.get_performance_stats()
        if stats['total_inferences'] > 0:
            print(f"\n📈 Session Statistics:")
            print(f"   Total inferences: {stats['total_inferences']}")
            print(f"   Total tokens: {stats['total_tokens_generated']}")
            print(f"   Average speed: {stats['average_tokens_per_second']:.2f} tokens/sec")
            print(f"   Peak memory: {stats['peak_memory_mb']:.1f}MB")
    
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
