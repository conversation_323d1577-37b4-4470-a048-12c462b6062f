import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Financial Agent System',
  description: 'Next.js frontend for the Financial Agent System',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-white">
          <header className="bg-blue-600 text-white p-4">
            <div className="container mx-auto">
              <h1 className="text-2xl font-bold">Financial Agent System</h1>
            </div>
          </header>
          <main>
            {children}
          </main>
          <footer className="bg-gray-100 p-4 mt-8">
            <div className="container mx-auto text-center text-gray-600">
              <p>© {new Date().getFullYear()} Financial Agent System. All rights reserved.</p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
