#!/usr/bin/env python3
"""
Knowledge Graph System
Structured knowledge representation for enhanced reasoning
Integrates with RAG system for intelligent knowledge retrieval
"""

import json
import time
from typing import Dict, Any, List, Set, Optional, Tuple
from collections import defaultdict, deque

class KnowledgeNode:
    """Represents a node in the knowledge graph"""
    
    def __init__(self, node_id: str, node_type: str, properties: Dict[str, Any] = None):
        self.id = node_id
        self.type = node_type
        self.properties = properties or {}
        self.relationships = defaultdict(list)  # relationship_type -> [target_nodes]
        self.created_at = time.time()
    
    def add_relationship(self, relationship_type: str, target_node: str, properties: Dict[str, Any] = None):
        """Add a relationship to another node"""
        self.relationships[relationship_type].append({
            'target': target_node,
            'properties': properties or {},
            'created_at': time.time()
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert node to dictionary representation"""
        return {
            'id': self.id,
            'type': self.type,
            'properties': self.properties,
            'relationships': dict(self.relationships),
            'created_at': self.created_at
        }

class KnowledgeGraph:
    """Knowledge graph for structured knowledge representation"""
    
    def __init__(self):
        self.nodes = {}  # node_id -> KnowledgeNode
        self.type_index = defaultdict(set)  # node_type -> {node_ids}
        self.relationship_index = defaultdict(set)  # relationship_type -> {(source, target)}
        
        # Initialize with mathematical and scientific knowledge
        self._initialize_core_knowledge()
        
        print("🕸️ Knowledge Graph initialized")
        print(f"   Nodes: {len(self.nodes)}")
        print(f"   Node types: {len(self.type_index)}")
    
    def add_node(self, node_id: str, node_type: str, properties: Dict[str, Any] = None) -> KnowledgeNode:
        """Add a node to the knowledge graph"""
        
        if node_id in self.nodes:
            return self.nodes[node_id]
        
        node = KnowledgeNode(node_id, node_type, properties)
        self.nodes[node_id] = node
        self.type_index[node_type].add(node_id)
        
        return node
    
    def add_relationship(self, source_id: str, relationship_type: str, target_id: str, properties: Dict[str, Any] = None):
        """Add a relationship between two nodes"""
        
        if source_id not in self.nodes or target_id not in self.nodes:
            return False
        
        self.nodes[source_id].add_relationship(relationship_type, target_id, properties)
        self.relationship_index[relationship_type].add((source_id, target_id))
        
        return True
    
    def find_nodes_by_type(self, node_type: str) -> List[KnowledgeNode]:
        """Find all nodes of a specific type"""
        return [self.nodes[node_id] for node_id in self.type_index.get(node_type, set())]
    
    def find_related_nodes(self, node_id: str, relationship_type: str = None, max_depth: int = 2) -> List[Dict[str, Any]]:
        """Find nodes related to a given node"""
        
        if node_id not in self.nodes:
            return []
        
        visited = set()
        queue = deque([(node_id, 0)])  # (node_id, depth)
        related_nodes = []
        
        while queue:
            current_id, depth = queue.popleft()
            
            if current_id in visited or depth > max_depth:
                continue
            
            visited.add(current_id)
            current_node = self.nodes[current_id]
            
            if depth > 0:  # Don't include the starting node
                related_nodes.append({
                    'node': current_node,
                    'depth': depth,
                    'path_length': depth
                })
            
            # Explore relationships
            for rel_type, relationships in current_node.relationships.items():
                if relationship_type is None or rel_type == relationship_type:
                    for rel in relationships:
                        target_id = rel['target']
                        if target_id not in visited:
                            queue.append((target_id, depth + 1))
        
        return related_nodes
    
    def search_knowledge(self, query: str, node_types: List[str] = None) -> List[Dict[str, Any]]:
        """Search for knowledge relevant to a query"""
        
        results = []
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        # Search through all nodes or specific types
        search_nodes = []
        if node_types:
            for node_type in node_types:
                search_nodes.extend(self.find_nodes_by_type(node_type))
        else:
            search_nodes = list(self.nodes.values())
        
        for node in search_nodes:
            relevance = self._calculate_node_relevance(node, query_words)
            
            if relevance > 0.1:  # Minimum relevance threshold
                results.append({
                    'node': node,
                    'relevance': relevance,
                    'match_type': self._get_match_type(node, query_words)
                })
        
        # Sort by relevance
        results.sort(key=lambda x: x['relevance'], reverse=True)
        
        return results[:10]  # Top 10 results
    
    def _calculate_node_relevance(self, node: KnowledgeNode, query_words: Set[str]) -> float:
        """Calculate relevance of a node to query words"""
        
        # Check node ID
        id_words = set(node.id.lower().replace('_', ' ').split())
        id_score = len(query_words.intersection(id_words)) / len(query_words) if query_words else 0
        
        # Check node properties
        property_score = 0
        property_word_count = 0
        
        for key, value in node.properties.items():
            if isinstance(value, str):
                prop_words = set(value.lower().split())
                property_word_count += len(prop_words)
                property_score += len(query_words.intersection(prop_words))
            elif isinstance(key, str):
                key_words = set(key.lower().replace('_', ' ').split())
                property_word_count += len(key_words)
                property_score += len(query_words.intersection(key_words))
        
        property_score = property_score / property_word_count if property_word_count > 0 else 0
        
        # Combine scores
        total_score = (id_score * 0.4) + (property_score * 0.6)
        
        return min(1.0, total_score)
    
    def _get_match_type(self, node: KnowledgeNode, query_words: Set[str]) -> str:
        """Determine what type of match this is"""
        
        id_words = set(node.id.lower().replace('_', ' ').split())
        if query_words.intersection(id_words):
            return "id_match"
        
        for key, value in node.properties.items():
            if isinstance(value, str):
                prop_words = set(value.lower().split())
                if query_words.intersection(prop_words):
                    return "property_match"
        
        return "weak_match"
    
    def get_knowledge_context(self, query: str, max_nodes: int = 5) -> str:
        """Get formatted knowledge context for a query"""
        
        search_results = self.search_knowledge(query)
        context_parts = []
        
        for i, result in enumerate(search_results[:max_nodes]):
            node = result['node']
            relevance = result['relevance']
            
            # Format node information
            node_info = f"{node.id} ({node.type})"
            
            # Add key properties
            key_properties = []
            for key, value in node.properties.items():
                if isinstance(value, str) and len(value) < 100:
                    key_properties.append(f"{key}: {value}")
                elif isinstance(value, (int, float)):
                    key_properties.append(f"{key}: {value}")
            
            if key_properties:
                node_info += " - " + "; ".join(key_properties[:3])
            
            context_parts.append(f"• {node_info}")
        
        return "\n".join(context_parts) if context_parts else "No relevant knowledge found."
    
    def _initialize_core_knowledge(self):
        """Initialize the knowledge graph with core mathematical and scientific knowledge"""
        
        # Mathematical concepts
        self.add_node("calculus", "mathematical_field", {
            "description": "Branch of mathematics dealing with derivatives and integrals",
            "applications": ["physics", "engineering", "economics"]
        })
        
        self.add_node("derivative", "mathematical_concept", {
            "definition": "Rate of change of a function",
            "notation": "f'(x) or df/dx",
            "geometric_meaning": "Slope of tangent line"
        })
        
        self.add_node("power_rule", "mathematical_rule", {
            "formula": "d/dx(x^n) = n*x^(n-1)",
            "applies_to": "polynomial functions",
            "example": "d/dx(x³) = 3x²"
        })
        
        self.add_node("integral", "mathematical_concept", {
            "definition": "Antiderivative or area under curve",
            "notation": "∫f(x)dx",
            "relationship": "Inverse of derivative"
        })
        
        self.add_node("fundamental_theorem_calculus", "mathematical_theorem", {
            "statement": "Derivative and integral are inverse operations",
            "part1": "d/dx ∫f(t)dt = f(x)",
            "part2": "∫[a to b] f'(x)dx = f(b) - f(a)"
        })
        
        # Physics concepts
        self.add_node("newton_second_law", "physical_law", {
            "formula": "F = ma",
            "description": "Force equals mass times acceleration",
            "units": "Newtons (kg⋅m/s²)"
        })
        
        self.add_node("kinematics", "physics_field", {
            "description": "Study of motion without considering forces",
            "key_equations": ["v = v₀ + at", "x = x₀ + v₀t + ½at²", "v² = v₀² + 2a(x-x₀)"]
        })
        
        # Logic concepts
        self.add_node("modus_ponens", "logical_rule", {
            "form": "If P → Q and P, then Q",
            "description": "Valid form of logical inference",
            "example": "If it rains, then the ground is wet. It rains. Therefore, the ground is wet."
        })
        
        self.add_node("syllogism", "logical_structure", {
            "form": "Major premise, minor premise, conclusion",
            "example": "All men are mortal. Socrates is a man. Therefore, Socrates is mortal."
        })
        
        # Add relationships
        self.add_relationship("derivative", "belongs_to", "calculus")
        self.add_relationship("integral", "belongs_to", "calculus")
        self.add_relationship("power_rule", "applies_to", "derivative")
        self.add_relationship("derivative", "inverse_of", "integral")
        self.add_relationship("fundamental_theorem_calculus", "connects", "derivative")
        self.add_relationship("fundamental_theorem_calculus", "connects", "integral")
        self.add_relationship("newton_second_law", "belongs_to", "kinematics")
        self.add_relationship("modus_ponens", "type_of", "syllogism")
    
    def save_to_file(self, filename: str):
        """Save knowledge graph to JSON file"""
        
        graph_data = {
            'nodes': {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            'metadata': {
                'created_at': time.time(),
                'node_count': len(self.nodes),
                'type_count': len(self.type_index)
            }
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(graph_data, f, indent=2)
            print(f"💾 Knowledge graph saved to {filename}")
        except Exception as e:
            print(f"❌ Failed to save knowledge graph: {e}")
    
    def load_from_file(self, filename: str):
        """Load knowledge graph from JSON file"""
        
        try:
            with open(filename, 'r') as f:
                graph_data = json.load(f)
            
            # Clear existing graph
            self.nodes.clear()
            self.type_index.clear()
            self.relationship_index.clear()
            
            # Load nodes
            for node_id, node_data in graph_data['nodes'].items():
                node = KnowledgeNode(
                    node_data['id'],
                    node_data['type'],
                    node_data['properties']
                )
                node.relationships = defaultdict(list, node_data['relationships'])
                node.created_at = node_data['created_at']
                
                self.nodes[node_id] = node
                self.type_index[node.type].add(node_id)
                
                # Rebuild relationship index
                for rel_type, relationships in node.relationships.items():
                    for rel in relationships:
                        self.relationship_index[rel_type].add((node_id, rel['target']))
            
            print(f"📖 Knowledge graph loaded from {filename}")
            print(f"   Nodes: {len(self.nodes)}")
            
        except Exception as e:
            print(f"❌ Failed to load knowledge graph: {e}")
            # Reinitialize with core knowledge
            self._initialize_core_knowledge()
