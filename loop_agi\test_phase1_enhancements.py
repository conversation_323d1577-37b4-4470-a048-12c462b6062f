#!/usr/bin/env python3
"""
Test Phase 1 Intelligence Enhancements
- Multi-pass reasoning
- Tool augmentation (calculator, knowledge)
- Real benchmark comparison
Goal: Measure improvement from 50.9% baseline
"""

import sys
import time
from pathlib import Path

# Add loop_singular_bit to path
sys.path.append(str(Path(__file__).parent.parent / 'loop_singular_bit'))

try:
    from loop_singular_bit import load_compressed_model
    from loop import LoopAGI
    from real_intelligence_benchmarks import RealIntelligenceBenchmarks
    from multi_pass_reasoning import MultiPassReasoning, EnhancedIntelligenceBenchmarks
    from tool_augmentation import ToolAugmentedReasoning
    
    print("🚀 TESTING PHASE 1 INTELLIGENCE ENHANCEMENTS")
    print("=" * 70)
    print("🎯 Goal: Boost intelligence from 50.9% baseline to 65-70%")
    print("🔧 Enhancements: Multi-pass reasoning + Tool augmentation")
    print()
    
    # Initialize Loop AGI system
    print("🔧 Initializing Loop AGI system...")
    loop_agi = LoopAGI()
    
    if not loop_agi.loop_singular_bit_model:
        print("❌ Loop_Singular_Bit model not available")
        exit(1)
    
    print(f"✅ System initialized with real loop_singular_bit model")
    print(f"📊 Core engine: {loop_agi.core_engine}")
    
    # Test 1: Baseline Measurement
    print("\n" + "="*50)
    print("📊 TEST 1: BASELINE MEASUREMENT")
    print("="*50)
    
    baseline_benchmarks = RealIntelligenceBenchmarks(loop_agi.loop_singular_bit_model)
    baseline_result = baseline_benchmarks.run_full_benchmark()
    
    baseline_score = baseline_result['overall_score']
    baseline_classification = baseline_result['classification']
    
    print(f"📊 BASELINE RESULTS:")
    print(f"   Overall Score: {baseline_score:.1f}%")
    print(f"   Classification: {baseline_classification}")
    print(f"   Mathematical: {baseline_result['detailed_results']['mathematical_reasoning']['score']:.1f}%")
    print(f"   Logical: {baseline_result['detailed_results']['logical_reasoning']['score']:.1f}%")
    print(f"   Language: {baseline_result['detailed_results']['language_understanding']['score']:.1f}%")
    print(f"   Creative: {baseline_result['detailed_results']['creative_problem_solving']['score']:.1f}%")
    
    # Test 2: Multi-Pass Reasoning Enhancement
    print("\n" + "="*50)
    print("🧠 TEST 2: MULTI-PASS REASONING ENHANCEMENT")
    print("="*50)
    
    enhanced_benchmarks = EnhancedIntelligenceBenchmarks(loop_agi.loop_singular_bit_model)
    multipass_result = enhanced_benchmarks.run_enhanced_benchmark_suite()
    
    multipass_score = multipass_result['overall_score']
    multipass_classification = multipass_result['classification']
    multipass_improvement = multipass_score - baseline_score
    
    print(f"📊 MULTI-PASS RESULTS:")
    print(f"   Overall Score: {multipass_score:.1f}%")
    print(f"   Classification: {multipass_classification}")
    print(f"   Improvement: {multipass_improvement:+.1f} points")
    print(f"   Mathematical Enhanced: {multipass_result['detailed_results']['mathematical_reasoning_enhanced']['score']:.1f}%")
    print(f"   Logical Enhanced: {multipass_result['detailed_results']['logical_reasoning_enhanced']['score']:.1f}%")
    
    # Show multi-pass statistics
    multipass_stats = multipass_result['multi_pass_statistics']
    print(f"   Problems Processed: {multipass_stats['total_problems']}")
    print(f"   Average Confidence: {multipass_stats['average_confidence']:.2f}")
    
    # Test 3: Tool Augmentation Enhancement
    print("\n" + "="*50)
    print("🛠️ TEST 3: TOOL AUGMENTATION ENHANCEMENT")
    print("="*50)
    
    tool_augmented = ToolAugmentedReasoning(loop_agi.loop_singular_bit_model)
    
    # Test tool-augmented problem solving
    test_problems = [
        {
            "problem": "Solve: 2x + 5 = 17",
            "type": "mathematical",
            "expected": "6"
        },
        {
            "problem": "Calculate: 15 * 8 + 32",
            "type": "mathematical", 
            "expected": "152"
        },
        {
            "problem": "What is the derivative power rule?",
            "type": "knowledge",
            "expected": "d/dx(x^n) = n*x^(n-1)"
        },
        {
            "problem": "If 3x - 7 = 14, what is x?",
            "type": "mathematical",
            "expected": "7"
        }
    ]
    
    tool_correct = 0
    tool_results = []
    
    for problem in test_problems:
        print(f"\n--- Testing: {problem['problem']} ---")
        
        tool_result = tool_augmented.solve_with_tools(problem['problem'], problem['type'])
        
        if tool_result['success']:
            answer_text = tool_result['final_answer']
            expected = problem['expected']
            
            # Check if expected answer is in the response
            answer_found = expected in answer_text or any(word in answer_text for word in expected.split())
            
            if answer_found:
                tool_correct += 1
                print(f"✅ Correct answer found with tools")
            else:
                print(f"❌ Incorrect answer despite tool usage")
            
            print(f"🛠️ Tools used: {tool_result['tools_used']}")
            
            tool_results.append({
                'problem': problem['problem'],
                'expected': expected,
                'answer': answer_text,
                'tools_used': tool_result['tools_used'],
                'correct': answer_found,
                'success': True
            })
        else:
            print(f"⚠️ Tool-augmented solving failed")
            tool_results.append({
                'problem': problem['problem'],
                'success': False,
                'error': tool_result.get('error', 'Unknown error')
            })
    
    tool_score = (tool_correct / len(test_problems)) * 100
    tool_improvement = tool_score - baseline_score  # Approximate comparison
    
    print(f"\n📊 TOOL AUGMENTATION RESULTS:")
    print(f"   Tool-Augmented Score: {tool_score:.1f}%")
    print(f"   Problems Solved: {tool_correct}/{len(test_problems)}")
    print(f"   Estimated Improvement: {tool_improvement:+.1f} points")
    
    # Get tool usage statistics
    tool_stats = tool_augmented.get_tool_usage_statistics()
    print(f"   Tool Success Rate: {tool_stats['success_rate']:.1%}")
    print(f"   Calculator Usage: {tool_stats['calculator_calculations']} calculations")
    print(f"   Knowledge Queries: {tool_stats['knowledge_queries']} queries")
    
    # Test 4: Combined Enhancement (Multi-pass + Tools)
    print("\n" + "="*50)
    print("🚀 TEST 4: COMBINED ENHANCEMENT")
    print("="*50)
    
    # Test combining both enhancements
    combined_correct = 0
    combined_results = []
    
    print("🧠 Testing combined multi-pass reasoning + tool augmentation...")
    
    for problem in test_problems[:2]:  # Test subset for combined approach
        print(f"\n--- Combined test: {problem['problem']} ---")
        
        # First use tools to gather information
        tool_result = tool_augmented.solve_with_tools(problem['problem'], problem['type'])
        
        # Then apply multi-pass reasoning to the enhanced information
        if tool_result['success']:
            enhanced_problem = f"{problem['problem']}\n\nTool information: {tool_result.get('tool_results', {})}"
            
            multi_pass = MultiPassReasoning(loop_agi.loop_singular_bit_model)
            multipass_result = multi_pass.solve_with_multi_pass(enhanced_problem, problem['type'])
            
            # Check combined result
            final_answer = multipass_result['final_answer']
            expected = problem['expected']
            
            answer_found = expected in final_answer or any(word in final_answer for word in expected.split())
            
            if answer_found:
                combined_correct += 1
                print(f"✅ Correct answer with combined enhancement")
            else:
                print(f"❌ Incorrect answer despite combined enhancement")
            
            combined_results.append({
                'problem': problem['problem'],
                'expected': expected,
                'tools_used': tool_result['tools_used'],
                'multipass_confidence': multipass_result['confidence'],
                'correct': answer_found,
                'enhancement': 'combined'
            })
    
    combined_score = (combined_correct / len(test_problems[:2])) * 100
    
    print(f"\n📊 COMBINED ENHANCEMENT RESULTS:")
    print(f"   Combined Score: {combined_score:.1f}%")
    print(f"   Problems Solved: {combined_correct}/{len(test_problems[:2])}")
    
    # Final Assessment
    print("\n" + "="*70)
    print("🏆 PHASE 1 ENHANCEMENT ASSESSMENT")
    print("="*70)
    
    print(f"📊 INTELLIGENCE PROGRESSION:")
    print(f"   Baseline: {baseline_score:.1f}% ({baseline_classification})")
    print(f"   Multi-pass: {multipass_score:.1f}% ({multipass_classification}) [{multipass_improvement:+.1f}]")
    print(f"   Tool-augmented: {tool_score:.1f}% [estimated {tool_improvement:+.1f}]")
    print(f"   Combined: {combined_score:.1f}% [limited test]")
    
    # Determine best enhancement
    best_score = max(baseline_score, multipass_score, tool_score)
    best_improvement = best_score - baseline_score
    
    print(f"\n🎯 PHASE 1 RESULTS:")
    print(f"   Best Score Achieved: {best_score:.1f}%")
    print(f"   Total Improvement: {best_improvement:+.1f} points")
    
    if best_score >= 65:
        print(f"   🎉 PHASE 1 TARGET ACHIEVED! (65-70% goal)")
        phase1_success = True
    elif best_improvement >= 10:
        print(f"   ✅ SIGNIFICANT IMPROVEMENT ACHIEVED!")
        phase1_success = True
    elif best_improvement >= 5:
        print(f"   📈 MODERATE IMPROVEMENT ACHIEVED")
        phase1_success = False
    else:
        print(f"   ⚠️ LIMITED IMPROVEMENT - NEED REFINEMENT")
        phase1_success = False
    
    # Recommendations for Phase 2
    print(f"\n🔮 PHASE 2 RECOMMENDATIONS:")
    if multipass_improvement > tool_improvement:
        print(f"   🧠 Multi-pass reasoning shows most promise (+{multipass_improvement:.1f})")
        print(f"   🔧 Focus on refining multi-pass strategies")
    else:
        print(f"   🛠️ Tool augmentation shows most promise (+{tool_improvement:.1f})")
        print(f"   🔧 Focus on expanding tool capabilities")
    
    if phase1_success:
        print(f"   🚀 Ready to implement RAG system (Phase 2)")
        print(f"   🎯 Target: {best_score:.1f}% → 80-85%")
    else:
        print(f"   🔄 Refine Phase 1 enhancements before Phase 2")
        print(f"   🎯 Target: Achieve 65%+ before proceeding")
    
    print(f"\n🔬 All measurements based on real benchmarks - no simulation!")
    
except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
