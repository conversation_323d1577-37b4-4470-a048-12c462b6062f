#!/usr/bin/env python3
"""
ADVANCED PATTERN DETECTION
==========================

Phase 1: Implement advanced pattern detection for 5-10× compression
Target: Bridge the gap from 2× to 34× compression for 675B on 8GB
"""

import torch
import torch.nn as nn
import numpy as np
import time
from typing import Dict, List, Tuple, Any
from sklearn.cluster import KMeans
import gc
import json
from collections import defaultdict

class AdvancedPatternDetector:
    """Advanced pattern detection for extreme compression"""
    
    def __init__(self, target_compression: float = 10.0):
        self.target_compression = target_compression
        self.pattern_libraries = {}
        self.compression_stats = {}
        
        print(f"🔍 ADVANCED PATTERN DETECTION")
        print(f"🎯 Target compression: {target_compression}×")
    
    def hierarchical_pattern_analysis(self, weight_tensor: torch.Tensor, 
                                    block_sizes: List[Tuple[int, int]] = [(2,2), (4,4), (8,8)]) -> Dict[str, Any]:
        """Multi-scale hierarchical pattern analysis"""
        
        print(f"🔍 Hierarchical pattern analysis on {weight_tensor.shape}")
        
        hierarchical_patterns = {}
        total_compression = 1.0
        
        for block_size in block_sizes:
            print(f"  📊 Analyzing {block_size} blocks...")
            
            # Extract blocks at this scale
            blocks = self.extract_blocks_efficient(weight_tensor, block_size)
            
            if len(blocks) == 0:
                continue
            
            # Find patterns at this scale
            patterns, indices, stats = self.find_fractal_patterns(blocks, block_size)
            
            compression_ratio = stats['compression_ratio']
            total_compression *= compression_ratio
            
            hierarchical_patterns[f"{block_size[0]}x{block_size[1]}"] = {
                'patterns': patterns,
                'indices': indices,
                'stats': stats,
                'compression_ratio': compression_ratio
            }
            
            print(f"    ✅ {len(patterns)} patterns, {compression_ratio:.1f}× compression")
        
        print(f"🎯 Total hierarchical compression: {total_compression:.1f}×")
        
        return {
            'hierarchical_patterns': hierarchical_patterns,
            'total_compression': total_compression,
            'method': 'hierarchical_fractal'
        }
    
    def extract_blocks_efficient(self, tensor: torch.Tensor, block_size: Tuple[int, int], 
                                max_blocks: int = 10000) -> List[torch.Tensor]:
        """Extract blocks efficiently with sampling"""
        
        h, w = tensor.shape
        bh, bw = block_size
        
        if h < bh or w < bw:
            return []
        
        # Calculate total possible blocks
        total_blocks = ((h - bh) // bh + 1) * ((w - bw) // bw + 1)
        
        blocks = []
        
        if total_blocks <= max_blocks:
            # Extract all blocks
            for i in range(0, h - bh + 1, bh):
                for j in range(0, w - bw + 1, bw):
                    block = tensor[i:i+bh, j:j+bw].clone()
                    blocks.append(block)
        else:
            # Sample blocks uniformly
            step_h = max(1, (h - bh) // int(np.sqrt(max_blocks)))
            step_w = max(1, (w - bw) // int(np.sqrt(max_blocks)))
            
            for i in range(0, h - bh + 1, step_h):
                for j in range(0, w - bw + 1, step_w):
                    if len(blocks) >= max_blocks:
                        break
                    block = tensor[i:i+bh, j:j+bw].clone()
                    blocks.append(block)
                if len(blocks) >= max_blocks:
                    break
        
        return blocks
    
    def find_fractal_patterns(self, blocks: List[torch.Tensor], block_size: Tuple[int, int], 
                            similarity_threshold: float = 0.98) -> Tuple[List[torch.Tensor], List[int], Dict]:
        """Find fractal and self-similar patterns"""
        
        if len(blocks) == 0:
            return [], [], {'compression_ratio': 1.0}
        
        # Normalize blocks for pattern matching
        normalized_blocks = []
        normalization_params = []
        
        for block in blocks:
            mean = torch.mean(block)
            std = torch.std(block)
            
            if std > 1e-8:
                normalized = (block - mean) / std
            else:
                normalized = block - mean
            
            normalized_blocks.append(normalized)
            normalization_params.append({'mean': mean.item(), 'std': std.item()})
        
        # Advanced pattern matching with fractal analysis
        patterns = []
        block_indices = []
        pattern_frequencies = defaultdict(int)
        
        # Use more sophisticated similarity matching
        for i, norm_block in enumerate(normalized_blocks):
            best_match_idx = -1
            best_similarity = -1
            
            # Check against existing patterns
            for pattern_idx, pattern in enumerate(patterns):
                similarity = self.calculate_advanced_similarity(norm_block, pattern)
                
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match_idx = pattern_idx
            
            # Use existing pattern or create new one
            if best_similarity >= similarity_threshold:
                block_indices.append(best_match_idx)
                pattern_frequencies[best_match_idx] += 1
            else:
                # Add new pattern
                patterns.append(norm_block.clone())
                new_pattern_idx = len(patterns) - 1
                block_indices.append(new_pattern_idx)
                pattern_frequencies[new_pattern_idx] = 1
        
        # Calculate compression statistics
        total_blocks = len(blocks)
        unique_patterns = len(patterns)
        compression_ratio = total_blocks / unique_patterns if unique_patterns > 0 else 1.0
        
        # Calculate pattern efficiency
        most_frequent_patterns = sorted(pattern_frequencies.items(), key=lambda x: x[1], reverse=True)
        top_10_coverage = sum(freq for _, freq in most_frequent_patterns[:10]) / total_blocks * 100
        
        stats = {
            'total_blocks': total_blocks,
            'unique_patterns': unique_patterns,
            'compression_ratio': compression_ratio,
            'similarity_threshold': similarity_threshold,
            'top_10_pattern_coverage': top_10_coverage,
            'pattern_frequencies': dict(pattern_frequencies),
            'normalization_params': normalization_params
        }
        
        return patterns, block_indices, stats
    
    def calculate_advanced_similarity(self, block1: torch.Tensor, block2: torch.Tensor) -> float:
        """Calculate advanced similarity including rotation and reflection invariance"""
        
        flat1 = block1.flatten()
        flat2 = block2.flatten()
        
        # Basic cosine similarity
        dot_product = torch.dot(flat1, flat2)
        norm1 = torch.norm(flat1)
        norm2 = torch.norm(flat2)
        
        if norm1 > 1e-8 and norm2 > 1e-8:
            cosine_sim = (dot_product / (norm1 * norm2)).item()
        else:
            cosine_sim = 1.0 if torch.allclose(flat1, flat2, atol=1e-8) else 0.0
        
        # Check rotations and reflections for small blocks
        if block1.shape[0] <= 8 and block1.shape[1] <= 8:
            max_similarity = cosine_sim
            
            # 90-degree rotations
            for k in range(1, 4):
                rotated = torch.rot90(block2, k)
                rot_flat = rotated.flatten()
                if torch.norm(rot_flat) > 1e-8:
                    rot_sim = (torch.dot(flat1, rot_flat) / (norm1 * torch.norm(rot_flat))).item()
                    max_similarity = max(max_similarity, rot_sim)
            
            # Horizontal and vertical flips
            flipped_h = torch.flip(block2, [1])
            flipped_v = torch.flip(block2, [0])
            
            for flipped in [flipped_h, flipped_v]:
                flip_flat = flipped.flatten()
                if torch.norm(flip_flat) > 1e-8:
                    flip_sim = (torch.dot(flat1, flip_flat) / (norm1 * torch.norm(flip_flat))).item()
                    max_similarity = max(max_similarity, flip_sim)
            
            return max_similarity
        
        return cosine_sim
    
    def optimize_pattern_dictionary(self, patterns: List[torch.Tensor], 
                                  pattern_frequencies: Dict[int, int],
                                  target_dict_size: int = 1000) -> Tuple[List[torch.Tensor], Dict[int, int]]:
        """Optimize pattern dictionary for maximum compression"""
        
        if len(patterns) <= target_dict_size:
            return patterns, pattern_frequencies
        
        print(f"🔧 Optimizing dictionary: {len(patterns)} → {target_dict_size} patterns")
        
        # Sort patterns by frequency
        sorted_patterns = sorted(pattern_frequencies.items(), key=lambda x: x[1], reverse=True)
        
        # Keep most frequent patterns
        kept_pattern_indices = [idx for idx, _ in sorted_patterns[:target_dict_size]]
        
        # Create new pattern list
        optimized_patterns = [patterns[i] for i in kept_pattern_indices]
        
        # Create mapping from old to new indices
        index_mapping = {old_idx: new_idx for new_idx, old_idx in enumerate(kept_pattern_indices)}
        
        # Update frequencies
        optimized_frequencies = {new_idx: pattern_frequencies[old_idx] 
                               for new_idx, old_idx in enumerate(kept_pattern_indices)}
        
        print(f"✅ Dictionary optimized: {len(optimized_patterns)} patterns kept")
        
        return optimized_patterns, optimized_frequencies
    
    def compress_layer_advanced(self, weight_tensor: torch.Tensor, layer_name: str) -> Dict[str, Any]:
        """Apply advanced pattern compression to a layer"""
        
        print(f"\n🔍 Advanced compression: {layer_name}")
        print(f"📊 Input shape: {weight_tensor.shape}")
        
        start_time = time.time()
        
        # Apply hierarchical pattern analysis
        hierarchical_result = self.hierarchical_pattern_analysis(weight_tensor)
        
        # Calculate total compression achieved
        total_compression = hierarchical_result['total_compression']
        
        # Calculate memory savings
        original_size_mb = weight_tensor.numel() * weight_tensor.element_size() / (1024**2)
        compressed_size_mb = original_size_mb / total_compression
        memory_savings_mb = original_size_mb - compressed_size_mb
        
        duration = time.time() - start_time
        
        result = {
            'layer_name': layer_name,
            'original_shape': list(weight_tensor.shape),
            'original_size_mb': original_size_mb,
            'compressed_size_mb': compressed_size_mb,
            'memory_savings_mb': memory_savings_mb,
            'compression_ratio': total_compression,
            'compression_method': 'advanced_hierarchical_pattern',
            'hierarchical_analysis': hierarchical_result,
            'processing_time_s': duration,
            'target_achieved': total_compression >= self.target_compression
        }
        
        print(f"✅ Compression: {total_compression:.1f}× (target: {self.target_compression}×)")
        print(f"📊 Size: {original_size_mb:.1f}MB → {compressed_size_mb:.1f}MB")
        print(f"⏱️ Time: {duration:.1f}s")
        
        # Store in pattern library
        self.pattern_libraries[layer_name] = result
        
        return result
    
    def batch_compress_model(self, model_path: str, max_layers: int = 10) -> Dict[str, Any]:
        """Compress multiple layers with advanced pattern detection"""
        
        print(f"\n🚀 BATCH ADVANCED COMPRESSION")
        print(f"📁 Model: {model_path}")
        print(f"🎯 Target: {self.target_compression}× per layer")
        
        # Load model index
        import os
        from safetensors import safe_open
        
        index_path = os.path.join(model_path, "model.safetensors.index.json")
        if not os.path.exists(index_path):
            print(f"❌ Model index not found: {index_path}")
            return {}
        
        with open(index_path, 'r') as f:
            weight_index = json.load(f)
        
        # Select layers to compress
        layer_names = list(weight_index['weight_map'].keys())
        
        # Focus on large layers (attention and MLP)
        priority_layers = [name for name in layer_names 
                          if any(keyword in name for keyword in ['q_proj', 'k_proj', 'v_proj', 'o_proj', 
                                                               'gate_proj', 'up_proj', 'down_proj'])]
        
        selected_layers = priority_layers[:max_layers]
        
        print(f"📊 Selected {len(selected_layers)} layers for compression")
        
        batch_results = {}
        total_original_size = 0
        total_compressed_size = 0
        successful_compressions = 0
        
        for i, layer_name in enumerate(selected_layers):
            print(f"\n📊 Layer {i+1}/{len(selected_layers)}: {layer_name}")
            
            try:
                # Load layer
                file_name = weight_index['weight_map'][layer_name]
                file_path = os.path.join(model_path, file_name)
                
                with safe_open(file_path, framework="pt", device="cpu") as f:
                    weight_tensor = f.get_tensor(layer_name)
                    
                    # Skip non-2D tensors
                    if len(weight_tensor.shape) != 2:
                        print(f"⚠️ Skipping non-2D tensor: {weight_tensor.shape}")
                        continue
                    
                    # Apply advanced compression
                    result = self.compress_layer_advanced(weight_tensor, layer_name)
                    
                    batch_results[layer_name] = result
                    total_original_size += result['original_size_mb']
                    total_compressed_size += result['compressed_size_mb']
                    
                    if result['target_achieved']:
                        successful_compressions += 1
                    
                    # Memory cleanup
                    del weight_tensor
                    gc.collect()
            
            except Exception as e:
                print(f"❌ Error processing {layer_name}: {e}")
                continue
        
        # Calculate overall statistics
        overall_compression = total_original_size / total_compressed_size if total_compressed_size > 0 else 1.0
        success_rate = successful_compressions / len(batch_results) * 100 if batch_results else 0
        
        batch_summary = {
            'timestamp': time.time(),
            'model_path': model_path,
            'layers_processed': len(batch_results),
            'successful_compressions': successful_compressions,
            'success_rate_percent': success_rate,
            'total_original_size_mb': total_original_size,
            'total_compressed_size_mb': total_compressed_size,
            'overall_compression_ratio': overall_compression,
            'target_compression': self.target_compression,
            'target_achieved': overall_compression >= self.target_compression,
            'layer_results': batch_results
        }
        
        print(f"\n📊 BATCH COMPRESSION SUMMARY:")
        print(f"   Layers processed: {len(batch_results)}")
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Overall compression: {overall_compression:.1f}×")
        print(f"   Target achieved: {'✅ YES' if batch_summary['target_achieved'] else '❌ NO'}")
        
        return batch_summary

def main():
    """Test advanced pattern detection"""
    
    print("🚀🚀🚀 ADVANCED PATTERN DETECTION 🚀🚀🚀")
    print("=" * 70)
    print("🎯 Phase 1: Achieve 5-10× compression with pattern detection")
    print()
    
    # Initialize detector
    detector = AdvancedPatternDetector(target_compression=8.0)
    
    # Test on model
    model_path = "../downloaded_models/mistral-7b-v0.1"
    if not os.path.exists(model_path):
        model_path = "downloaded_models/mistral-7b-v0.1"
    
    if os.path.exists(model_path):
        # Run batch compression
        results = detector.batch_compress_model(model_path, max_layers=5)
        
        # Save results
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        results_file = f"advanced_pattern_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n✅ Results saved: {results_file}")
        
        if results.get('target_achieved', False):
            print(f"🎉 PHASE 1 SUCCESS: {results['overall_compression_ratio']:.1f}× compression achieved!")
        else:
            print(f"🔧 Continue optimization: {results['overall_compression_ratio']:.1f}× achieved, target: {detector.target_compression}×")
    
    else:
        print(f"❌ Model not found: {model_path}")
        print("🔧 Testing with synthetic data...")
        
        # Test with synthetic data
        test_tensor = torch.randn(1024, 1024) * 0.1
        result = detector.compress_layer_advanced(test_tensor, "test_layer")
        
        print(f"🧪 Synthetic test: {result['compression_ratio']:.1f}× compression")

if __name__ == "__main__":
    main()
