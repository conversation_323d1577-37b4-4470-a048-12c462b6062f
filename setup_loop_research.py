#!/usr/bin/env python3
"""
SETUP LOOP RESEARCH SYSTEM
==========================

Setup script for Loop research API system
Installs dependencies and configures API access
"""

import subprocess
import sys
import os
import json

def install_dependencies():
    """Install required dependencies"""
    
    print("📦 Installing Loop Research dependencies...")
    
    dependencies = [
        "google-generativeai",
        "requests",
        "torch",
        "transformers",
        "safetensors"
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
    
    print("✅ All dependencies installed")

def setup_gemini_api():
    """Setup Gemini API configuration"""
    
    print("\n🔧 Setting up Gemini API...")
    
    # Check if config file exists
    if os.path.exists('gemini_config.json'):
        with open('gemini_config.json', 'r') as f:
            config = json.load(f)
        
        if config.get('api_key') == "YOUR_GEMINI_API_KEY_HERE":
            print("⚠️ Please update gemini_config.json with your actual API key")
            print("🔗 Get your API key from: https://makersuite.google.com/app/apikey")
            return False
        else:
            print("✅ Gemini API configuration found")
            return True
    else:
        print("❌ gemini_config.json not found")
        return False

def test_system():
    """Test the Loop research system"""
    
    print("\n🧪 Testing Loop research system...")
    
    try:
        # Import the research system
        from loop_research_api_system import LoopResearchSystem
        
        # Initialize system
        system = LoopResearchSystem()
        
        print("✅ Loop research system initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    """Main setup function"""
    
    print("🚀 LOOP RESEARCH SYSTEM SETUP")
    print("=" * 40)
    
    # Install dependencies
    install_dependencies()
    
    # Setup API
    api_ready = setup_gemini_api()
    
    # Test system
    system_ready = test_system()
    
    print(f"\n📊 SETUP SUMMARY:")
    print(f"   Dependencies: ✅ Installed")
    print(f"   Gemini API: {'✅ Ready' if api_ready else '⚠️ Needs configuration'}")
    print(f"   System: {'✅ Ready' if system_ready else '❌ Needs fixing'}")
    
    if api_ready and system_ready:
        print(f"\n🎉 LOOP RESEARCH SYSTEM READY!")
        print(f"🚀 Run: python loop_research_api_system.py")
    else:
        print(f"\n⚠️ Setup incomplete - please fix issues above")
    
    return api_ready and system_ready

if __name__ == "__main__":
    main()
