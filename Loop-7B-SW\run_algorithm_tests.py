#!/usr/bin/env python3
"""
RUN ALGORITHM TESTS: Real Testing of Q-GEMS, NeuroGenesis, MDM
==============================================================

Execute comprehensive testing of all three breakthrough algorithms
on actual Mistral 7B model with documented evidence.
"""

import os
import sys
import json
from datetime import datetime
from test_all_algorithms import RealAlgorithmTester

def find_mistral_model():
    """Find Mistral 7B model path"""
    
    # Common model locations
    possible_paths = [
        "downloaded_models/mistral-7b-v0.1",
        "../downloaded_models/mistral-7b-v0.1",
        "D:/Loop/downloaded_models/mistral-7b-v0.1",
        "mistralai/Mistral-7B-v0.1",
        "../mistralai/Mistral-7B-v0.1",
        "../../mistralai/Mistral-7B-v0.1",
        "models/mistralai/Mistral-7B-v0.1",
        "../models/mistralai/Mistral-7B-v0.1"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            index_file = os.path.join(path, "model.safetensors.index.json")
            if os.path.exists(index_file):
                print(f"✅ Found Mistral 7B model at: {path}")
                return path
    
    print("❌ Mistral 7B model not found in common locations")
    print("Please ensure the model is downloaded and accessible")
    return None

def install_dependencies():
    """Install required dependencies"""
    
    print("📦 Checking dependencies...")
    
    try:
        import torch
        import transformers
        import safetensors
        import psutil
        print("✅ All dependencies available")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Installing required packages...")
        
        import subprocess
        packages = [
            "torch",
            "transformers", 
            "safetensors",
            "psutil"
        ]
        
        for package in packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
        
        return True

def main():
    """Main execution function"""
    
    print("🔬🔬🔬 REAL ALGORITHM TESTING EXECUTION 🔬🔬🔬")
    print("=" * 70)
    print("Testing Q-GEMS, NeuroGenesis, and MDM algorithms")
    print("NO SIMULATION - Real measurements and documented evidence")
    print()
    
    # Check dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return False
    
    # Find model
    model_path = find_mistral_model()
    if not model_path:
        print("❌ Cannot proceed without Mistral 7B model")
        return False
    
    print(f"🎯 Target: <300MB RAM compression")
    print(f"📁 Model: {model_path}")
    print()
    
    try:
        # Initialize tester
        print("🔧 Initializing algorithm tester...")
        tester = RealAlgorithmTester(model_path)
        
        # Run comprehensive tests
        print("🚀 Starting comprehensive algorithm testing...")
        results = tester.run_comprehensive_test()
        
        # Display summary
        print("\n" + "="*70)
        print("🎉 ALGORITHM TESTING COMPLETE!")
        print("="*70)
        
        # Summary of results
        baseline = results['baseline_measurements']
        algorithms = results['algorithm_results']
        
        print(f"\n📊 BASELINE MEASUREMENTS:")
        print(f"   Total parameters: {baseline['total_parameters']:,}")
        print(f"   Original size: {baseline['total_size_gb']:.2f} GB")
        print(f"   Bits per parameter: {baseline['bits_per_parameter']:.2f}")
        
        print(f"\n🏆 ALGORITHM RESULTS:")
        
        for alg_name, alg_result in algorithms.items():
            if alg_result['success']:
                final_memory = alg_result['final_memory_mb']
                target_memory = alg_result['target_memory_mb']
                target_achieved = alg_result['target_achieved']
                
                print(f"\n   {alg_name.upper()}:")
                print(f"      Algorithm: {alg_result['algorithm_name']}")
                print(f"      Target: {target_memory} MB")
                print(f"      Achieved: {final_memory:.1f} MB")
                print(f"      Target met: {'✅ YES' if target_achieved else '❌ NO'}")
                print(f"      Processing time: {alg_result['total_time_seconds']:.2f}s")
                
                if 'quality_test' in alg_result:
                    quality = alg_result['quality_test']['average_quality']
                    print(f"      Quality retention: {quality:.2f} ({quality*100:.1f}%)")
            else:
                print(f"\n   {alg_name.upper()}: ❌ FAILED")
                print(f"      Error: {alg_result.get('error', 'Unknown error')}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        
        successful_algorithms = [name for name, result in algorithms.items() if result['success']]
        target_achieved_algorithms = [name for name, result in algorithms.items() 
                                    if result['success'] and result['target_achieved']]
        
        print(f"   Successful algorithms: {len(successful_algorithms)}/3")
        print(f"   Target achieved: {len(target_achieved_algorithms)}/3")
        
        if target_achieved_algorithms:
            print(f"   ✅ SUCCESS: {len(target_achieved_algorithms)} algorithm(s) achieved <300MB target!")
            
            # Find best performing algorithm
            best_memory = min([algorithms[name]['final_memory_mb'] 
                             for name in target_achieved_algorithms])
            best_algorithm = [name for name in target_achieved_algorithms 
                            if algorithms[name]['final_memory_mb'] == best_memory][0]
            
            print(f"   🏆 Best performer: {best_algorithm.upper()} at {best_memory:.1f} MB")
        else:
            print(f"   ⚠️ No algorithms achieved the <300MB target")
        
        # Save detailed results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        summary_file = f"algorithm_test_summary_{timestamp}.json"
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'test_summary': {
                'total_algorithms_tested': len(algorithms),
                'successful_algorithms': len(successful_algorithms),
                'target_achieved_count': len(target_achieved_algorithms),
                'target_achieved_algorithms': target_achieved_algorithms,
                'best_memory_mb': min([algorithms[name]['final_memory_mb'] 
                                     for name in target_achieved_algorithms]) if target_achieved_algorithms else None
            },
            'baseline_measurements': baseline,
            'algorithm_results': algorithms
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n💾 Test summary saved to: {summary_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Algorithm testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 Algorithm testing completed successfully!")
        print("📋 Check the generated JSON files for detailed results")
    else:
        print("\n❌ Algorithm testing failed")
        print("📋 Check error messages above for troubleshooting")
    
    exit(0 if success else 1)
