"""
Direct test script for DataCollectionAgent
"""
import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

from agents.data_agent import DataCollectionAgent, MarketDataRequest

async def test_data_collection():
    print("Testing DataCollectionAgent...")
    
    # Initialize the agent
    agent = DataCollectionAgent()
    
    try:
        # Start the agent
        await agent.start()
        
        # Test 1: Basic data fetch
        print("\nTest 1: Basic OHLCV data for AAPL (1 day interval, 1 month period)")
        ohlcv = await agent.fetch_ohlcv(
            symbol="AAPL",
            interval="1d",
            period="1mo"
        )
        
        if ohlcv:
            print(f"Successfully fetched {len(ohlcv.timestamp)} data points for {ohlcv.symbol}")
            print(f"First 5 timestamps: {ohlcv.timestamp[:5]}")
            print(f"First 5 close prices: {ohlcv.close[:5]}")
        
        # Test 2: Date range
        print("\nTest 2: Date range for MSFT (1 hour interval, specific dates)")
        ohlcv = await agent.fetch_ohlcv(
            symbol="MSFT",
            interval="1h",
            start="2023-01-01",
            end="2023-01-07"
        )
        
        if ohlcv:
            print(f"Successfully fetched {len(ohlcv.timestamp)} hourly data points for {ohlcv.symbol}")
        
        # Test 3: Process request
        print("\nTest 3: Process request for multiple symbols")
        response = await agent.process({
            'request': {
                'symbols': ['GOOGL', 'AMZN'],
                'interval': '1d',
                'period': '1wk'
            }
        })
        
        if response.success:
            print(f"Successfully processed request with {len(response.data['results'])} symbols")
            for symbol, data in response.data['results'].items():
                print(f"  {symbol}: {len(data) if data else 0} data points")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        raise
    finally:
        # Always stop the agent
        await agent.stop()

if __name__ == "__main__":
    asyncio.run(test_data_collection())
