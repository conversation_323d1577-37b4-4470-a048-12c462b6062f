# Financial Agent System

A comprehensive, modular trading system with multiple specialized agents working together to analyze markets, generate trading signals, and execute trades while managing risk.

## 🚀 Features

- **Multi-Agent Architecture**: Specialized agents for data collection, analysis, strategy, execution, and risk management
- **Real-time Market Data**: Integrated with yfinance for real-time and historical market data
- **Advanced Analysis**: Technical indicators, pattern recognition, and statistical analysis
- **Risk Management**: Comprehensive risk controls including position sizing, drawdown limits, and sector exposure
- **Web Interface**: Real-time monitoring and control via FastAPI-based web interface
- **LLM Integration**: Optional Mistral LLM integration for advanced analysis and decision making
- **Paper Trading**: Test strategies risk-free with paper trading mode
- **Modular Design**: Easily extensible architecture for adding new strategies and data sources

## 📦 Project Structure

```
financial_agent/
├── agents/                  # Agent implementations
│   ├── __init__.py
│   ├── base_agent.py        # Base agent class with common functionality
│   ├── data_agent.py        # Data collection and processing
│   ├── analysis_agent.py    # Market analysis and technical indicators
│   ├── strategy_agent.py    # Trading strategies and signal generation
│   ├── execution_agent.py   # Order execution and position management
│   └── risk_agent.py        # Risk management and controls
├── web/                     # Web interface
│   ├── app.py               # FastAPI application
│   ├── static/              # Static files (JS, CSS)
│   └── templates/           # HTML templates
├── llm/                     # LLM integration (optional)
│   └── mistral_wrapper.py   # Mistral model wrapper
├── tests/                   # Test suite
│   ├── test_data_agent.py
│   ├── test_analysis_agent.py
│   ├── test_strategy_agent.py
│   ├── test_execution_agent.py
│   ├── test_risk_agent.py
│   └── test_integration.py
├── config.py                # Configuration settings
├── run_system.py            # Main entry point
├── run_web.py               # Web server entry point
├── requirements.txt         # Core dependencies
└── requirements-web.txt     # Web interface dependencies
```

## 🚀 Getting Started

### Prerequisites

- Python 3.9+
- pip (Python package manager)
- (Optional) CUDA-compatible GPU for LLM acceleration

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/financial-agent.git
   cd financial-agent
   ```

2. **Create and activate a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   # Core dependencies
   pip install -r requirements.txt
   
   # Web interface (optional)
   pip install -r requirements-web.txt
   ```

4. **Configure the system**
   Copy `.env.example` to `.env` and update the settings as needed:
   ```bash
   cp .env.example .env
   # Edit .env with your preferred settings
   ```

### Running the System

1. **Start the system**
   ```bash
   python run_system.py
   ```
   This will start all agents and the web interface (if enabled).

2. **Access the web interface**
   Open your browser and navigate to `http://localhost:8000`

3. **Run tests**
   ```bash
   pytest tests/
   ```

## 🛠 Configuration

Configuration is handled through environment variables or a `.env` file. Key settings include:

```ini
# Trading settings
WATCHLIST=SPY,QQQ,IWM,DIA,VTI,VOO
PAPER_TRADING=true
INITIAL_BALANCE=100000.0

# Risk management
MAX_DRAWDOWN=0.10
MAX_POSITION_RISK=0.02
MAX_SECTOR_EXPOSURE=0.30

# Web interface
WEB_ENABLED=true
WEB_HOST=0.0.0.0
WEB_PORT=8000

# LLM (optional)
LLM_ENABLED=false
LLM_MODEL_NAME=mistral-7b
```

## 🤖 Agents

### Data Collection Agent
- Fetches market data from various sources
- Handles data normalization and caching
- Supports multiple timeframes and intervals

### Analysis Agent
- Calculates technical indicators
- Identifies chart patterns
- Generates market sentiment analysis

### Strategy Agent
- Implements various trading strategies
- Generates trade signals
- Manages position sizing and portfolio allocation

### Execution Agent
- Executes trades through broker APIs
- Manages open positions
- Handles order types (market, limit, stop-loss)

### Risk Management Agent
- Monitors portfolio risk
- Enforces position limits
- Implements circuit breakers

## 🌐 Web Interface

The web interface provides:
- Real-time portfolio overview
- Position tracking
- Performance metrics
- Trade history
- System status

## 📈 Strategies

### ETF Rotation Strategy
- Rotates between sector ETFs based on momentum
- Implements sector diversification
- Uses technical indicators for entry/exit signals

### Mean Reversion
- Identifies overbought/oversold conditions
- Trades against the prevailing trend
- Uses RSI and Bollinger Bands

### Trend Following
- Rides strong market trends
- Uses moving averages for trend identification
- Implements trailing stops

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please read our [contributing guidelines](CONTRIBUTING.md) for details.

## 📧 Contact

For questions or support, please open an issue on GitHub.


2. **Installation**
   ```bash
   # Clone the repository
   git clone [repository-url]
   cd financial_agent

   # Install dependencies
   pip install -r requirements.txt
   
   # Install Mistral dependencies
   pip install torch transformers accelerate bitsandbytes
   ```

3. **Configuration**
   Copy and update the example configuration:
   ```bash
   cp config/example_settings.py config/settings.py
   ```

4. **Running the Agent**
   ```bash
   python main.py
   ```

## Usage

```python
from agents.orchestrator import OrchestratorAgent

# Initialize the orchestrator
orchestrator = OrchestratorAgent()

# Start the agent system
orchestrator.run()
```

## Configuration

Edit `config/settings.py` to configure:
- API keys
- Model parameters
- Trading parameters
- Risk parameters

## License

MIT License - See LICENSE for more information.
