#!/usr/bin/env python3
"""
RUN AUTONOMOUS ARCHITECTURE SEARCH
==================================

Main execution script for the autonomous transformer architecture search system.
Integrates evolutionary optimization with AI scientist capabilities for continuous discovery.

Usage:
    python run_autonomous_architecture_search.py --config config.json
    python run_autonomous_architecture_search.py --quick-test
    python run_autonomous_architecture_search.py --resume checkpoint.json
"""

import asyncio
import argparse
import json
import logging
import time
import signal
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Import our autonomous search system
from ai_scientist_architecture_search import AIScientistArchitectureSearch
from ai_scientist_components import ResearchDatabase, HypothesisGenerator, ExperimentPlanner, ResultsAnalyzer
from paper_generator import PaperGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('autonomous_search.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutonomousSearchRunner:
    """Main runner for autonomous architecture search"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.search_system = None
        self.is_running = False
        self.start_time = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        if self.search_system:
            self.search_system.stop_search()
        self.is_running = False
    
    async def run_search(self) -> Dict[str, Any]:
        """Run the autonomous architecture search"""
        
        logger.info("🚀 Starting Autonomous Transformer Architecture Search")
        logger.info("=" * 70)
        
        self.start_time = time.time()
        self.is_running = True
        
        try:
            # Initialize search system
            logger.info("🔧 Initializing AI Scientist Architecture Search System...")
            self.search_system = AIScientistArchitectureSearch(self.config)
            
            # Print configuration
            self._print_configuration()
            
            # Run autonomous search
            logger.info("🧬 Beginning autonomous evolution...")
            results = await self.search_system.run_autonomous_search()
            
            # Generate final summary
            self._print_final_summary(results)
            
            return results
            
        except KeyboardInterrupt:
            logger.info("🛑 Search interrupted by user")
            return self._handle_interruption()
            
        except Exception as e:
            logger.error(f"❌ Search failed with error: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}
            
        finally:
            self.is_running = False
            if self.start_time:
                total_time = time.time() - self.start_time
                logger.info(f"⏱️ Total runtime: {total_time/3600:.2f} hours")
    
    def _print_configuration(self):
        """Print search configuration"""
        
        logger.info("📋 Search Configuration:")
        logger.info(f"   Population Size: {self.config.get('population_size', 50)}")
        logger.info(f"   Target Generations: {self.config.get('target_generations', 100)}")
        logger.info(f"   Target Memory: {self.config.get('target_memory_gb', 8.0)}GB")
        logger.info(f"   Target Speed: {self.config.get('target_speed_tokens_per_sec', 100.0)} tokens/sec")
        logger.info(f"   Hardware: {self.config.get('compute_capability', 7.5)} compute capability")
        logger.info(f"   Save Interval: {self.config.get('save_interval', 10)} generations")
        logger.info("")
    
    def _print_final_summary(self, results: Dict[str, Any]):
        """Print final search summary"""
        
        logger.info("🎉 AUTONOMOUS SEARCH COMPLETED!")
        logger.info("=" * 50)
        
        if 'error' in results:
            logger.error(f"❌ Search failed: {results['error']}")
            return
        
        # Extract key metrics
        total_generations = results.get('total_generations', 0)
        best_architectures = results.get('best_architectures', [])
        insights = results.get('research_insights', [])
        fitness_progression = results.get('fitness_progression', [])
        
        logger.info(f"📊 Search Statistics:")
        logger.info(f"   Generations Completed: {total_generations}")
        logger.info(f"   High-Performing Architectures: {len(best_architectures)}")
        logger.info(f"   Research Insights Generated: {len(insights)}")
        
        if fitness_progression:
            initial_fitness = fitness_progression[0]
            final_fitness = fitness_progression[-1]
            improvement = final_fitness - initial_fitness
            logger.info(f"   Fitness Improvement: {improvement:.4f} ({improvement/initial_fitness*100:.1f}%)")
        
        if best_architectures:
            best_arch = best_architectures[0]
            logger.info(f"🏆 Best Architecture:")
            logger.info(f"   Fitness Score: {best_arch.get('fitness', 0):.4f}")
            
            genome = best_arch.get('genome', {})
            logger.info(f"   Layers: {genome.get('num_layers', 'N/A')}")
            logger.info(f"   Hidden Size: {genome.get('hidden_size', 'N/A')}")
            logger.info(f"   Compression: {1.0/genome.get('compression_ratio', 0.1):.1f}×")
        
        # Print file locations
        paper_path = results.get('final_paper_path', 'N/A')
        logger.info(f"📄 Final Paper: {paper_path}")
        logger.info(f"💾 Results Saved: results/final_search_results.json")
        
        logger.info("🎯 Search completed successfully!")
    
    def _handle_interruption(self) -> Dict[str, Any]:
        """Handle search interruption"""
        
        logger.info("🔄 Handling search interruption...")
        
        if self.search_system:
            # Try to save current progress
            try:
                self.search_system._save_progress()
                logger.info("💾 Progress saved successfully")
            except Exception as e:
                logger.warning(f"Failed to save progress: {e}")
        
        return {
            'status': 'interrupted',
            'message': 'Search was interrupted but progress has been saved',
            'checkpoint': 'checkpoints/search_progress.json'
        }

def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """Load configuration from file or create default"""
    
    if config_path and Path(config_path).exists():
        logger.info(f"📁 Loading configuration from {config_path}")
        with open(config_path, 'r') as f:
            return json.load(f)
    
    # Default configuration
    logger.info("📋 Using default configuration")
    return {
        # Search parameters
        'population_size': 50,
        'target_generations': 100,
        'mutation_rate': 0.1,
        'crossover_rate': 0.7,
        'elite_ratio': 0.1,
        
        # Hardware constraints
        'target_memory_gb': 8.0,
        'target_speed_tokens_per_sec': 100.0,
        'max_memory_gb': 8.0,
        'gpu_memory_gb': 8.0,
        'cpu_cores': 8,
        'memory_bandwidth_gbps': 100.0,
        'compute_capability': 7.5,
        
        # Operational parameters
        'save_interval': 10,
        'report_interval': 20,
        'checkpoint_interval': 25,
        
        # AI scientist parameters
        'hypothesis_generation_enabled': True,
        'experiment_planning_enabled': True,
        'autonomous_adaptation': True,
        'paper_generation_enabled': True
    }

def create_quick_test_config() -> Dict[str, Any]:
    """Create configuration for quick testing"""
    
    logger.info("⚡ Creating quick test configuration")
    return {
        'population_size': 10,
        'target_generations': 5,
        'mutation_rate': 0.2,
        'crossover_rate': 0.8,
        'elite_ratio': 0.2,
        'target_memory_gb': 4.0,
        'target_speed_tokens_per_sec': 50.0,
        'max_memory_gb': 4.0,
        'save_interval': 2,
        'report_interval': 3,
        'hypothesis_generation_enabled': True,
        'experiment_planning_enabled': True,
        'autonomous_adaptation': True,
        'paper_generation_enabled': True
    }

def resume_from_checkpoint(checkpoint_path: str) -> Dict[str, Any]:
    """Resume search from checkpoint"""
    
    logger.info(f"🔄 Resuming from checkpoint: {checkpoint_path}")
    
    if not Path(checkpoint_path).exists():
        raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
    
    with open(checkpoint_path, 'r') as f:
        checkpoint_data = json.load(f)
    
    # Extract configuration from checkpoint
    config = checkpoint_data.get('config', load_config())
    
    # Adjust for resumption
    completed_generations = checkpoint_data.get('current_generation', 0)
    remaining_generations = config.get('target_generations', 100) - completed_generations
    
    config['target_generations'] = remaining_generations
    config['resume_from_checkpoint'] = checkpoint_path
    
    logger.info(f"📊 Resuming with {remaining_generations} generations remaining")
    
    return config

async def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(
        description="Autonomous Transformer Architecture Search with AI Scientist"
    )
    parser.add_argument(
        '--config', 
        type=str, 
        help='Path to configuration JSON file'
    )
    parser.add_argument(
        '--quick-test', 
        action='store_true', 
        help='Run quick test with small population and few generations'
    )
    parser.add_argument(
        '--resume', 
        type=str, 
        help='Resume from checkpoint file'
    )
    parser.add_argument(
        '--output-dir', 
        type=str, 
        default='autonomous_search_results',
        help='Output directory for results'
    )
    
    args = parser.parse_args()
    
    try:
        # Create output directory
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Load configuration
        if args.resume:
            config = resume_from_checkpoint(args.resume)
        elif args.quick_test:
            config = create_quick_test_config()
        else:
            config = load_config(args.config)
        
        # Add output directory to config
        config['output_dir'] = str(output_dir)
        
        # Create and run search
        runner = AutonomousSearchRunner(config)
        results = await runner.run_search()
        
        # Save final results
        results_path = output_dir / 'final_results.json'
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"💾 Final results saved to {results_path}")
        
        # Exit with appropriate code
        if 'error' in results:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
