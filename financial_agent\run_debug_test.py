"""
Debug script for DataCollectionAgent with detailed logging.
"""
import asyncio
import logging
import sys
import os
import time
from datetime import datetime

# Set up logging to both console and file
log_filename = f"debug_test_{int(time.time())}.log"
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_filename, mode='w', encoding='utf-8')
    ]
)

# Get the root logger and set level to DEBUG
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)

# Add the parent directory to the path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from financial_agent.agents.data_agent import DataCollectionAgent
from financial_agent.llm.mock_mistral_wrapper import MistralWrapper

async def test_single_fetch(symbol, interval, period, agent):
    """Test fetching data for a single symbol with detailed logging."""
    logger.info("\n" + "="*80)
    logger.info(f"TESTING {symbol} - Interval: {interval}, Period: {period}")
    logger.info("="*80)
    
    try:
        start_time = time.time()
        logger.info(f"Fetching data for {symbol}...")
        
        ohlcv = await agent.fetch_ohlcv(
            symbol=symbol,
            interval=interval,
            period=period,
            timeout=45,
            max_retries=2
        )
        
        elapsed = time.time() - start_time
        
        if ohlcv is not None:
            logger.info("\nFETCH SUCCESSFUL")
            logger.info("-" * 40)
            logger.info(f"Symbol: {symbol}")
            logger.info(f"Data points: {len(ohlcv.timestamp)}")
            logger.info(f"Date range: {ohlcv.timestamp[0]} to {ohlcv.timestamp[-1]}")
            logger.info(f"Latest close: ${ohlcv.close[-1]:.2f}")
            logger.info(f"Volume: {ohlcv.volume[-1]:,}")
            logger.info(f"Time taken: {elapsed:.2f} seconds")
            return True
        else:
            logger.error("\nFETCH FAILED: No data returned")
            return False
            
    except Exception as e:
        logger.error(f"\nFETCH FAILED: {str(e)}", exc_info=True)
        return False

async def main():
    """Run the debug test."""
    logger.info("Starting debug test...")
    logger.info(f"Log file: {os.path.abspath(log_filename)}")
    
    # Initialize the agent with mock LLM
    mock_llm = MistralWrapper()
    agent = DataCollectionAgent(llm_wrapper=mock_llm)
    
    try:
        # Start the agent
        logger.info("Starting agent...")
        await agent.start()
        
        # Test cases
        test_cases = [
            ("AAPL", "1d", "1mo"),  # Daily data for 1 month
            ("MSFT", "1h", "5d"),   # Hourly data for 5 days
            ("GOOGL", "1d", "1y"),   # Daily data for 1 year
        ]
        
        # Run tests
        results = []
        for symbol, interval, period in test_cases:
            success = await test_single_fetch(symbol, interval, period, agent)
            results.append((symbol, interval, period, success))
            await asyncio.sleep(1)  # Small delay between tests
        
        # Print summary
        logger.info("\n" + "="*80)
        logger.info("TEST SUMMARY")
        logger.info("="*80)
        
        for symbol, interval, period, success in results:
            status = "PASSED" if success else "FAILED"
            logger.info(f"{symbol} ({interval}, {period}): {status}")
        
        logger.info("\nDebug test completed.")
        logger.info(f"Log file: {os.path.abspath(log_filename)}")
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}", exc_info=True)
    finally:
        # Stop the agent
        if agent.is_running:
            logger.info("Stopping agent...")
            await agent.stop()
            logger.info("Agent stopped.")

if __name__ == "__main__":
    asyncio.run(main())
