#!/usr/bin/env python3
"""Test web interface imports and basic functionality."""

import sys
import os
import asyncio

# Add the project root to the Python path
project_root = os.path.abspath('.')
sys.path.insert(0, project_root)
logger.info(f"Added project root to path: {project_root}")

async def test_imports():
    """Test importing the web application."""
    try:
        logger.info("Testing web app imports...")
        
        # Test importing web app
        try:
            from financial_agent.web.app import app
            logger.info("✅ Successfully imported web app")
        except ImportError as e:
            logger.error(f"❌ Failed to import web app: {e}")
            raise
            
        # Test importing TradingSystem
        try:
            from financial_agent.web.integration import TradingSystem
            logger.info("✅ Successfully imported TradingSystem")
        except ImportError as e:
            logger.error(f"❌ Failed to import TradingSystem: {e}")
            raise
        
        # Test creating a TradingSystem instance
        config = {
            'initial_portfolio': 100000.0,
            'watchlist': ['SPY', 'QQQ'],
            'timeframe': '1d',
            'data_agent': {'api_key': 'test', 'cache_ttl': 300},
            'analysis_agent': {'indicators': ['sma', 'rsi']},
            'strategy_agent': {'strategy': 'etf_rotation'},
            'execution_agent': {'paper_trading': True},
            'risk_agent': {'max_drawdown': 0.1}
        }
        
        try:
            trading_system = TradingSystem(config)
            logger.info("✅ Successfully created TradingSystem instance")
            
            # Test getting portfolio
            portfolio = await trading_system.get_portfolio()
            logger.info(f"✅ Portfolio: {portfolio}")
            
            # Test getting risk metrics
            risk_metrics = await trading_system.get_risk_metrics()
            logger.info(f"✅ Risk Metrics: {risk_metrics}")
            
            # Test getting trades
            trades = await trading_system.get_trades()
            logger.info(f"✅ Trades: {trades}")
            
            # Test getting performance
            performance = await trading_system.get_performance()
            logger.info(f"✅ Performance: {performance}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing TradingSystem: {e}")
            raise
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_imports())
