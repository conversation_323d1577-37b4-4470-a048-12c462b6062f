#!/usr/bin/env python3
"""
LOOP AGI - Meta-Cognitive Engine
Advanced thought logging and self-reflection system
"""

import json
import time
import datetime
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class MetaCognitiveEngine:
    """Advanced meta-cognitive processing and thought analysis"""
    
    def __init__(self):
        self.thought_categories = {
            'REASONING': 'Logical analysis and problem-solving thoughts',
            'SELF_REFLECTION': 'Self-assessment and introspection',
            'GOAL_PLANNING': 'Goal setting and strategic planning',
            'PERFORMANCE_ANALYSIS': 'Performance evaluation and optimization',
            'SAFETY_MONITORING': 'Safety compliance and risk assessment',
            'LEARNING': 'Knowledge acquisition and skill development',
            'CREATIVITY': 'Novel idea generation and innovation',
            'ERROR_ANALYSIS': 'Mistake identification and correction',
            'SYSTEM_OPTIMIZATION': 'System improvement and efficiency',
            'METACOGNITION': 'Thinking about thinking processes'
        }
        
        self.thought_history = []
        self.reflection_depth = 0
        self.cognitive_load = 0.0
        
    def log_advanced_thought(self, content: str, category: str, 
                           confidence: float = 0.8, 
                           reasoning_chain: List[str] = None,
                           metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Log advanced thought with meta-cognitive analysis"""
        
        thought_entry = {
            'timestamp': datetime.datetime.now().isoformat(),
            'content': content,
            'category': category,
            'confidence': confidence,
            'reasoning_chain': reasoning_chain or [],
            'metadata': metadata or {},
            'cognitive_metrics': self._analyze_cognitive_metrics(content, category),
            'thought_id': self._generate_thought_id(),
            'reflection_level': self.reflection_depth
        }
        
        # Analyze thought quality
        thought_entry['quality_score'] = self._assess_thought_quality(thought_entry)
        
        # Update cognitive load
        self.cognitive_load = self._calculate_cognitive_load(thought_entry)
        thought_entry['cognitive_load'] = self.cognitive_load
        
        # Store thought
        self.thought_history.append(thought_entry)
        
        # Write to enhanced thoughts log
        self._write_enhanced_thought_log(thought_entry)
        
        return thought_entry
    
    def _generate_thought_id(self) -> str:
        """Generate unique thought identifier"""
        timestamp = int(time.time() * 1000000)
        return f"thought_{timestamp}"
    
    def _analyze_cognitive_metrics(self, content: str, category: str) -> Dict[str, float]:
        """Analyze cognitive complexity and characteristics"""
        
        # Basic text analysis
        word_count = len(content.split())
        sentence_count = content.count('.') + content.count('!') + content.count('?')
        avg_word_length = sum(len(word) for word in content.split()) / max(1, word_count)
        
        # Complexity indicators
        complexity_keywords = ['because', 'therefore', 'however', 'although', 'consequently']
        complexity_score = sum(1 for keyword in complexity_keywords if keyword in content.lower()) / len(complexity_keywords)
        
        # Category-specific analysis
        category_weights = {
            'REASONING': 1.2,
            'METACOGNITION': 1.5,
            'CREATIVITY': 1.1,
            'PERFORMANCE_ANALYSIS': 1.0,
            'SAFETY_MONITORING': 0.9
        }
        
        category_multiplier = category_weights.get(category, 1.0)
        
        return {
            'word_count': word_count,
            'sentence_count': sentence_count,
            'avg_word_length': avg_word_length,
            'complexity_score': complexity_score * category_multiplier,
            'cognitive_density': (word_count * complexity_score) / max(1, sentence_count)
        }
    
    def _assess_thought_quality(self, thought_entry: Dict[str, Any]) -> float:
        """Assess the quality of a thought entry"""
        
        quality_factors = {
            'confidence': thought_entry['confidence'] * 0.3,
            'complexity': min(1.0, thought_entry['cognitive_metrics']['complexity_score']) * 0.25,
            'reasoning_depth': min(1.0, len(thought_entry['reasoning_chain']) * 0.1) * 0.2,
            'cognitive_density': min(1.0, thought_entry['cognitive_metrics']['cognitive_density'] * 0.1) * 0.15,
            'category_relevance': self._assess_category_relevance(thought_entry) * 0.1
        }
        
        return sum(quality_factors.values())
    
    def _assess_category_relevance(self, thought_entry: Dict[str, Any]) -> float:
        """Assess how well the thought fits its category"""
        category = thought_entry['category']
        content = thought_entry['content'].lower()
        
        category_keywords = {
            'REASONING': ['analyze', 'conclude', 'deduce', 'infer', 'logic'],
            'SELF_REFLECTION': ['assess', 'evaluate', 'reflect', 'consider', 'introspect'],
            'GOAL_PLANNING': ['plan', 'goal', 'objective', 'strategy', 'target'],
            'PERFORMANCE_ANALYSIS': ['performance', 'efficiency', 'optimize', 'improve', 'measure'],
            'SAFETY_MONITORING': ['safe', 'risk', 'secure', 'validate', 'compliance'],
            'LEARNING': ['learn', 'understand', 'knowledge', 'skill', 'acquire'],
            'CREATIVITY': ['create', 'innovate', 'novel', 'original', 'generate'],
            'ERROR_ANALYSIS': ['error', 'mistake', 'fix', 'correct', 'debug'],
            'SYSTEM_OPTIMIZATION': ['system', 'optimize', 'enhance', 'upgrade', 'improve'],
            'METACOGNITION': ['think', 'cognition', 'mental', 'awareness', 'consciousness']
        }
        
        keywords = category_keywords.get(category, [])
        matches = sum(1 for keyword in keywords if keyword in content)
        
        return min(1.0, matches / max(1, len(keywords)))
    
    def _calculate_cognitive_load(self, thought_entry: Dict[str, Any]) -> float:
        """Calculate current cognitive load based on thought complexity"""
        
        recent_thoughts = self.thought_history[-10:]  # Last 10 thoughts
        if not recent_thoughts:
            return 0.1
        
        # Calculate average complexity of recent thoughts
        avg_complexity = statistics.mean(
            t['cognitive_metrics']['complexity_score'] for t in recent_thoughts
        )
        
        # Factor in thought frequency
        time_span = 300  # 5 minutes in seconds
        recent_time = time.time() - time_span
        recent_count = sum(1 for t in recent_thoughts 
                          if datetime.datetime.fromisoformat(t['timestamp']).timestamp() > recent_time)
        
        frequency_factor = min(1.0, recent_count / 20)  # Normalize to 20 thoughts per 5 min
        
        return min(1.0, (avg_complexity * 0.7) + (frequency_factor * 0.3))
    
    def _write_enhanced_thought_log(self, thought_entry: Dict[str, Any]):
        """Write enhanced thought to log file"""
        
        # Enhanced log format
        log_line = (
            f"[{thought_entry['timestamp']}] "
            f"[{thought_entry['category']}] "
            f"[ID:{thought_entry['thought_id']}] "
            f"[Q:{thought_entry['quality_score']:.2f}] "
            f"[C:{thought_entry['confidence']:.2f}] "
            f"[L:{thought_entry['cognitive_load']:.2f}] "
            f"{thought_entry['content']}"
        )
        
        # Add reasoning chain if present
        if thought_entry['reasoning_chain']:
            log_line += f" | REASONING: {' -> '.join(thought_entry['reasoning_chain'])}"
        
        log_line += "\n"
        
        # Write to thoughts log
        with open('logs/thoughts.log', 'a', encoding='utf-8') as f:
            f.write(log_line)
        
        # Also write detailed JSON to separate file
        with open('logs/detailed_thoughts.json', 'a', encoding='utf-8') as f:
            f.write(json.dumps(thought_entry, default=str) + '\n')
    
    def perform_self_reflection(self, focus_area: str = None) -> Dict[str, Any]:
        """Perform deep self-reflection analysis"""
        
        self.reflection_depth += 1
        
        reflection_result = {
            'timestamp': datetime.datetime.now().isoformat(),
            'reflection_depth': self.reflection_depth,
            'focus_area': focus_area,
            'analysis': {},
            'insights': [],
            'improvement_suggestions': []
        }
        
        # Analyze recent thought patterns
        recent_thoughts = self.thought_history[-50:]  # Last 50 thoughts
        
        if recent_thoughts:
            # Category distribution analysis
            category_counts = {}
            for thought in recent_thoughts:
                cat = thought['category']
                category_counts[cat] = category_counts.get(cat, 0) + 1
            
            reflection_result['analysis']['category_distribution'] = category_counts
            
            # Quality trend analysis
            quality_scores = [t['quality_score'] for t in recent_thoughts]
            reflection_result['analysis']['quality_trend'] = {
                'average': statistics.mean(quality_scores),
                'median': statistics.median(quality_scores),
                'improvement': quality_scores[-10:] if len(quality_scores) >= 10 else quality_scores
            }
            
            # Cognitive load analysis
            cognitive_loads = [t['cognitive_load'] for t in recent_thoughts]
            reflection_result['analysis']['cognitive_load_trend'] = {
                'average': statistics.mean(cognitive_loads),
                'current': self.cognitive_load,
                'peak': max(cognitive_loads)
            }
            
            # Generate insights
            reflection_result['insights'] = self._generate_insights(reflection_result['analysis'])
            
            # Generate improvement suggestions
            reflection_result['improvement_suggestions'] = self._generate_improvement_suggestions(
                reflection_result['analysis']
            )
        
        # Log the reflection
        self.log_advanced_thought(
            f"Performed self-reflection analysis. Focus: {focus_area or 'general'}. "
            f"Insights generated: {len(reflection_result['insights'])}",
            'SELF_REFLECTION',
            confidence=0.9,
            reasoning_chain=[
                "Analyzed recent thought patterns",
                "Evaluated quality trends",
                "Assessed cognitive load",
                "Generated insights and suggestions"
            ],
            metadata={'reflection_result': reflection_result}
        )
        
        # Save reflection to file
        with open('logs/self_reflections.json', 'a', encoding='utf-8') as f:
            f.write(json.dumps(reflection_result, default=str) + '\n')
        
        self.reflection_depth = max(0, self.reflection_depth - 1)
        
        return reflection_result
    
    def _generate_insights(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate insights from analysis"""
        insights = []
        
        # Category distribution insights
        if 'category_distribution' in analysis:
            dist = analysis['category_distribution']
            total_thoughts = sum(dist.values())
            
            # Find dominant categories
            dominant_cat = max(dist, key=dist.get)
            dominant_pct = (dist[dominant_cat] / total_thoughts) * 100
            
            if dominant_pct > 40:
                insights.append(f"Thinking heavily focused on {dominant_cat} ({dominant_pct:.1f}% of thoughts)")
            
            # Find underrepresented categories
            important_cats = ['REASONING', 'SELF_REFLECTION', 'SAFETY_MONITORING']
            for cat in important_cats:
                if cat not in dist or (dist[cat] / total_thoughts) < 0.1:
                    insights.append(f"Insufficient focus on {cat} - may need more attention")
        
        # Quality trend insights
        if 'quality_trend' in analysis:
            quality = analysis['quality_trend']
            if quality['average'] < 0.6:
                insights.append("Thought quality below optimal threshold - need improvement")
            elif quality['average'] > 0.8:
                insights.append("Maintaining high thought quality - good cognitive performance")
        
        # Cognitive load insights
        if 'cognitive_load_trend' in analysis:
            load = analysis['cognitive_load_trend']
            if load['average'] > 0.8:
                insights.append("High cognitive load detected - may need processing optimization")
            elif load['average'] < 0.3:
                insights.append("Low cognitive engagement - could increase thinking complexity")
        
        return insights
    
    def _generate_improvement_suggestions(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate improvement suggestions"""
        suggestions = []
        
        # Quality improvement suggestions
        if 'quality_trend' in analysis:
            quality = analysis['quality_trend']
            if quality['average'] < 0.7:
                suggestions.append("Increase reasoning chain depth for better thought quality")
                suggestions.append("Focus on higher confidence assessments")
                suggestions.append("Enhance complexity of cognitive analysis")
        
        # Category balance suggestions
        if 'category_distribution' in analysis:
            dist = analysis['category_distribution']
            total = sum(dist.values())
            
            # Suggest more self-reflection if low
            if dist.get('SELF_REFLECTION', 0) / total < 0.15:
                suggestions.append("Increase self-reflection frequency for better self-awareness")
            
            # Suggest more creativity if low
            if dist.get('CREATIVITY', 0) / total < 0.1:
                suggestions.append("Engage in more creative thinking for innovation")
        
        # Cognitive load optimization
        if 'cognitive_load_trend' in analysis:
            load = analysis['cognitive_load_trend']
            if load['peak'] > 0.9:
                suggestions.append("Implement cognitive load balancing to prevent overload")
        
        return suggestions
    
    def get_cognitive_metrics(self) -> Dict[str, Any]:
        """Get comprehensive cognitive performance metrics"""
        
        if not self.thought_history:
            return {'status': 'no_data', 'metrics': {}}
        
        recent_thoughts = self.thought_history[-100:]  # Last 100 thoughts
        
        # Calculate metrics
        avg_quality = statistics.mean(t['quality_score'] for t in recent_thoughts)
        avg_confidence = statistics.mean(t['confidence'] for t in recent_thoughts)
        avg_complexity = statistics.mean(t['cognitive_metrics']['complexity_score'] for t in recent_thoughts)
        
        # Category distribution
        categories = {}
        for thought in recent_thoughts:
            cat = thought['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        return {
            'status': 'active',
            'metrics': {
                'total_thoughts': len(self.thought_history),
                'recent_thoughts': len(recent_thoughts),
                'average_quality': avg_quality,
                'average_confidence': avg_confidence,
                'average_complexity': avg_complexity,
                'current_cognitive_load': self.cognitive_load,
                'reflection_depth': self.reflection_depth,
                'category_distribution': categories
            },
            'timestamp': datetime.datetime.now().isoformat()
        }

# Module interface
def create_meta_cognitive_engine():
    """Factory function to create MetaCognitiveEngine instance"""
    return MetaCognitiveEngine()
