"""
Tests for the Risk Management Agent.
"""
import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from financial_agent.agents.risk_agent import (
    RiskManagementAgent,
    RiskLevel,
    RiskParameters
)
from financial_agent.agents.strategy_agent import TradeSignal, SignalType, StrategyType

@pytest.fixture
def risk_agent():
    """Create a RiskManagementAgent instance for testing."""
    config = {
        'initial_portfolio': 100000.0,
        'risk_parameters': {
            'max_drawdown': 0.10,
            'max_position_risk': 0.02,
            'max_sector_exposure': 0.30,
            'max_leverage': 2.0,
            'daily_loss_limit': 0.05,
            'position_concentration': 0.20,
            'volatility_threshold': 0.30,
            'min_volume': 100000,
            'min_price': 5.0
        }
    }
    return RiskManagementAgent(config=config)

@pytest.fixture
def sample_trade_signal():
    """Create a sample trade signal for testing."""
    return TradeSignal(
        symbol='AAPL',
        signal_type=SignalType.BUY,
        strategy_type=StrategyType.ETF_STRATEGY,
        entry_price=150.0,
        stop_loss=135.0,
        take_profit=165.0,
        size_percent=10.0,  # 10% of portfolio
        confidence=0.75,
        timestamp=int(datetime.now().timestamp()),
        metadata={
            'sector': 'Technology',
            'atr': 2.5,
            'volume': 5000000
        }
    )

@pytest.mark.asyncio
async def test_risk_assessment_acceptable(risk_agent, sample_trade_signal):
    """Test risk assessment for an acceptable trade."""
    # Test with a trade that should be within risk limits
    response = await risk_agent.process({'signal': sample_trade_signal})
    
    assert response.success is True
    assert response.data['is_acceptable'] is True
    assert RiskLevel(response.data['risk_level']) in [RiskLevel.LOW, RiskLevel.MEDIUM]
    assert isinstance(response.data['reasons'], list)
    assert isinstance(response.data['metrics'], dict)

@pytest.mark.asyncio
async def test_position_size_risk(risk_agent, sample_trade_signal):
    """Test position size risk check."""
    # Set position size to exceed concentration limit (20%)
    sample_trade_signal.size_percent = 25.0
    
    response = await risk_agent.process({'signal': sample_trade_signal})
    
    assert response.success is True
    assert response.data['is_acceptable'] is False
    assert any('Position size exceeds' in reason for reason in response.data['reasons'])
    assert RiskLevel(response.data['risk_level']) in [RiskLevel.HIGH, RiskLevel.CRITICAL]

@pytest.mark.asyncio
async def test_drawdown_risk(risk_agent, sample_trade_signal):
    """Test portfolio drawdown risk check."""
    # Simulate a large drawdown
    risk_agent.portfolio_state['current_value'] = 80000.0  # 20% drawdown from 100k
    risk_agent.portfolio_state['peak_value'] = 100000.0
    
    response = await risk_agent.process({'signal': sample_trade_signal})
    
    assert response.success is True
    assert response.data['is_acceptable'] is False
    assert any('drawdown' in reason.lower() for reason in response.data['reasons'])

@pytest.mark.asyncio
async def test_sector_exposure_risk(risk_agent, sample_trade_signal):
    """Test sector exposure risk check."""
    # Set up existing sector exposure
    risk_agent.portfolio_state['sector_exposure'] = {
        'Technology': 0.25  # 25% exposure already
    }
    
    # Add another 10% to Technology sector
    sample_trade_signal.size_percent = 10.0
    
    response = await risk_agent.process({'signal': sample_trade_signal})
    
    assert response.success is True
    assert response.data['is_acceptable'] is False
    assert any('sector' in reason.lower() for reason in response.data['reasons'])

@pytest.mark.asyncio
async def test_daily_loss_risk(risk_agent, sample_trade_signal):
    """Test daily loss limit risk check."""
    # Simulate a large daily loss
    today = datetime.now().date().isoformat()
    risk_agent.portfolio_state['daily_pnl'] = {
        today: -6000.0  # 6% loss today (limit is 5%)
    }
    
    response = await risk_agent.process({'signal': sample_trade_signal})
    
    assert response.success is True
    assert response.data['is_acceptable'] is False
    assert any('daily loss' in reason.lower() for reason in response.data['reasons'])

@pytest.mark.asyncio
async def test_liquidity_risk(risk_agent, sample_trade_signal):
    """Test liquidity and price risk check."""
    # Modify the signal to have low volume and price
    sample_trade_signal.metadata['volume'] = 50000  # Below min_volume of 100k
    sample_trade_signal.entry_price = 4.0  # Below min_price of 5.0
    
    response = await risk_agent.process({'signal': sample_trade_signal})
    
    assert response.success is True
    assert response.data['is_acceptable'] is False
    assert any('liquidity' in reason.lower() or 'price' in reason.lower() 
               for reason in response.data['reasons'])

def test_get_risk_metrics(risk_agent):
    """Test getting risk metrics."""
    metrics = risk_agent.get_risk_metrics()
    
    assert isinstance(metrics, dict)
    assert 'portfolio_value' in metrics
    assert 'peak_value' in metrics
    assert 'drawdown' in metrics
    assert 'positions' in metrics
    assert 'last_updated' in metrics

@pytest.mark.asyncio
async def test_invalid_input(risk_agent):
    """Test handling of invalid input."""
    # Missing signal
    response = await risk_agent.process({})
    assert response.success is False
    assert 'signal' in response.error
    
    # Invalid signal type
    response = await risk_agent.process({'signal': 'not a signal'})
    assert response.success is False
    assert 'TradeSignal' in response.error

if __name__ == "__main__":
    pytest.main(["-v", "test_risk_agent.py"])
