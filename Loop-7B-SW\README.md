# 🧬 Loop 7B SW - Streaming Weights Compression

**Ultra-efficient 7B parameter model inference using streaming weight compression**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Memory Usage](https://img.shields.io/badge/RAM-1.9GB-green.svg)](docs/BENCHMARKS.md)
[![Compression](https://img.shields.io/badge/Compression-27x-red.svg)](docs/TECHNICAL_DETAILS.md)

## 🚀 **Key Achievements**

- **1.9 GB RAM usage** (vs 6.87 GB for GGUF Q4_K_M)
- **500 MB active storage** (vs 4.37 GB for standard compression)
- **7.96 tokens/sec** on CPU-only inference
- **Runs on 4GB devices** where other methods fail
- **Proven on real Mistral 7B model**

## 📊 **Performance Comparison**

| Method | RAM Usage | Storage | Speed | Quality | 8GB Compatible |
|--------|-----------|---------|-------|---------|----------------|
| **Loop 7B SW** | **1.9 GB** | **0.5 GB** | 7.96 tok/s | Fair | ✅ **YES** |
| GGUF Q4_K_M | 6.87 GB | 4.37 GB | 9-47 tok/s | Good | ✅ YES |
| GGUF Q2_K | 5.58 GB | 3.08 GB | ~7-35 tok/s | Poor | ✅ YES |
| Vanilla CPU | 9.5 GB | 13.5 GB | 0.1 tok/s | Perfect | ❌ NO |

## 🎯 **Use Cases**

### ✅ **Perfect For:**
- **Edge devices** with limited memory (4-8GB RAM)
- **Budget laptops** without dedicated GPU
- **IoT deployments** requiring local AI
- **Research** into streaming architectures
- **Proof-of-concept** for larger model scaling

### ⚠️ **Not Ideal For:**
- Production apps requiring maximum speed
- Applications needing perfect quality
- Batch processing workloads
- Real-time conversational AI

## 🛠️ **Quick Start**

### Prerequisites
```bash
pip install torch transformers safetensors psutil numpy
```

### Basic Usage
```python
from loop_7b_sw import StreamingInference

# Initialize the system
model = StreamingInference("path/to/mistral-7b")

# Run inference
result = model.generate("The future of AI is", max_tokens=10)
print(result['generated_text'])
print(f"Memory used: {result['peak_memory_mb']:.1f}MB")
```

### Command Line
```bash
python run_inference.py --prompt "Hello world" --max_tokens 20
```

## 📁 **Project Structure**

```
Loop-7B-SW/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── setup.py                 # Package installation
├── LICENSE                  # MIT license
├── src/
│   ├── loop_7b_sw/          # Main package
│   │   ├── __init__.py
│   │   ├── streaming_inference.py    # Core inference engine
│   │   ├── compression.py            # Weight compression
│   │   └── memory_manager.py         # Memory optimization
│   └── examples/
│       ├── basic_inference.py       # Simple example
│       ├── benchmark.py             # Performance testing
│       └── compare_methods.py       # Comparison with GGUF
├── tests/
│   ├── test_compression.py         # Unit tests
│   ├── test_inference.py           # Integration tests
│   └── test_memory.py              # Memory tests
├── docs/
│   ├── BENCHMARKS.md               # Performance data
│   ├── TECHNICAL_DETAILS.md        # Implementation details
│   ├── COMPARISON.md               # vs other methods
│   └── API_REFERENCE.md            # Code documentation
├── scripts/
│   ├── download_model.py           # Model download utility
│   ├── run_inference.py            # CLI interface
│   └── benchmark_suite.py          # Complete benchmarks
└── data/
    └── sample_outputs/             # Example generations
```

## 🔬 **Technical Innovation**

### Streaming Weight Architecture
- **On-demand loading**: Only active layers in memory
- **Compressed storage**: Float16 + SVD compression
- **Intelligent caching**: LRU with predictive prefetching
- **Memory pooling**: Efficient tensor management

### Key Components
1. **CompressedWeightStorage**: Disk-based weight management
2. **StreamingLinear**: On-demand layer computation
3. **MemoryManager**: RAM optimization and monitoring
4. **InferenceEngine**: Coordinated text generation

## 📈 **Benchmarks**

### Real Hardware Tests
- **System**: 12-core CPU, 16GB RAM, Windows
- **Model**: Mistral 7B (7.24B parameters)
- **Method**: Direct measurement with psutil

### Memory Usage
```
Peak RAM: 1,904.6 MB (measured)
Active weights: ~500 MB
Compression ratio: 27× storage reduction
Memory efficiency: 72% better than Q4_K_M
```

### Performance
```
Inference speed: 7.96 tokens/sec (averaged)
Load time: 1.74 seconds
Quality: Fair (degradation after 3-4 tokens)
8GB compatibility: ✅ 76% headroom
```

## 🤝 **Contributing**

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md).

### Development Setup
```bash
git clone https://github.com/yourusername/Loop-7B-SW.git
cd Loop-7B-SW
pip install -e .
pip install -r requirements-dev.txt
```

### Running Tests
```bash
pytest tests/
python scripts/benchmark_suite.py
```

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- **Mistral AI** for the base model
- **HuggingFace** for the transformers library
- **TheBloke** for GGUF quantization references
- **Community** feedback and testing

## 📞 **Support**

- **Issues**: [GitHub Issues](https://github.com/yourusername/Loop-7B-SW/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/Loop-7B-SW/discussions)
- **Documentation**: [docs/](docs/)

---

**Loop 7B SW - Making 7B models accessible on any device** 🚀
