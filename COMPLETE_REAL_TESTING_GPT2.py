#!/usr/bin/env python3
"""
🔥 COMPLETE REAL COMPRESSION + INFERENCE TESTING
===============================================

REAL testing methodology using GPT-2 Large (774M parameters):
1. ✅ Load actual model weights
2. ✅ Apply actual compression algorithms  
3. ✅ Save compressed model to disk
4. ✅ Load compressed model back
5. ✅ Run inference on compressed model
6. ✅ Compare outputs before/after
7. ✅ Measure actual accuracy

This is COMPLETE REAL TESTING, not theoretical!
"""

import torch
import torch.nn as nn
import time
import gc
import psutil
import os
from transformers import GPT2LMHeadModel, GPT2Tokenizer
import numpy as np

def monitor_ram():
    """Monitor RAM usage"""
    process = psutil.Process()
    memory_info = process.memory_info()
    ram_mb = memory_info.rss / (1024 * 1024)
    return ram_mb

def calculate_model_size(model):
    """Calculate actual model size"""
    total_size = 0
    for param in model.parameters():
        total_size += param.numel() * param.element_size()
    for buffer in model.buffers():
        total_size += buffer.numel() * buffer.element_size()
    return total_size / (1024 * 1024)  # MB

def apply_real_quantization(model):
    """Apply REAL quantization to model"""
    print("🔧 Applying REAL 8-bit quantization...")
    
    # Prepare for quantization
    model.eval()
    
    # Apply dynamic quantization
    quantized_model = torch.quantization.quantize_dynamic(
        model,
        {nn.Linear},
        dtype=torch.qint8
    )
    
    return quantized_model

def test_model_inference(model, tokenizer, test_prompts):
    """Test model inference with multiple prompts"""
    
    results = []
    
    for i, prompt in enumerate(test_prompts):
        print(f"  Test {i+1}: '{prompt[:30]}...'")
        
        try:
            # Tokenize
            inputs = tokenizer.encode(prompt, return_tensors='pt')
            
            # Generate
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 30,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            inference_time = time.time() - start_time
            
            # Decode
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated_only = generated_text[len(prompt):].strip()
            
            # Quality check
            quality_score = 0
            if len(generated_only) > 5:
                quality_score += 1
            if any(word in generated_only.lower() for word in ['def', 'function', 'code', 'algorithm', 'python']):
                quality_score += 1
            if not any(error in generated_only.lower() for error in ['error', 'failed', 'cannot']):
                quality_score += 1
            
            quality_percentage = (quality_score / 3) * 100
            
            results.append({
                'prompt': prompt,
                'generated': generated_only,
                'inference_time': inference_time,
                'quality_score': quality_percentage,
                'success': True
            })
            
            print(f"    ✅ Success ({inference_time:.2f}s, {quality_percentage:.0f}%): '{generated_only[:40]}...'")
            
        except Exception as e:
            print(f"    ❌ Failed: {e}")
            results.append({
                'prompt': prompt,
                'error': str(e),
                'success': False
            })
    
    return results

def complete_real_testing():
    """Complete real compression + inference testing"""
    
    print("🔥 COMPLETE REAL COMPRESSION + INFERENCE TESTING")
    print("=" * 60)
    print("Using GPT-2 Large (774M parameters) for complete real testing")
    
    baseline_ram = monitor_ram()
    print(f"📊 Baseline RAM: {baseline_ram:.1f}MB")
    
    # Test prompts
    test_prompts = [
        "Write a Python function to reverse a linked list:",
        "Fix this bug: def add(a, b): return a - b",
        "Create a unit test for factorial:",
        "Explain binary search algorithm:",
        "Write code to find maximum in array:"
    ]
    
    try:
        # STEP 1: Load original model
        print(f"\n📥 STEP 1: LOADING ORIGINAL GPT-2 LARGE")
        print("=" * 45)
        
        print("🔄 Loading GPT-2 Large model and tokenizer...")
        
        tokenizer = GPT2Tokenizer.from_pretrained('gpt2-large')
        tokenizer.pad_token = tokenizer.eos_token
        
        model = GPT2LMHeadModel.from_pretrained('gpt2-large')
        
        original_ram = monitor_ram()
        original_size_mb = calculate_model_size(model)
        
        print(f"✅ Original model loaded!")
        print(f"   Model size: {original_size_mb:.1f}MB")
        print(f"   RAM usage: {original_ram:.1f}MB")
        print(f"   Parameters: {model.num_parameters():,}")
        
        # STEP 2: Test original model
        print(f"\n🔄 STEP 2: TESTING ORIGINAL MODEL INFERENCE")
        print("=" * 50)
        
        print("Testing original model...")
        original_results = test_model_inference(model, tokenizer, test_prompts)
        
        original_successful = [r for r in original_results if r.get('success', False)]
        original_success_rate = len(original_successful) / len(test_prompts) * 100
        original_avg_time = np.mean([r['inference_time'] for r in original_successful]) if original_successful else 0
        original_avg_quality = np.mean([r['quality_score'] for r in original_successful]) if original_successful else 0
        
        print(f"✅ Original model results:")
        print(f"   Success rate: {original_success_rate:.1f}%")
        print(f"   Average time: {original_avg_time:.2f}s")
        print(f"   Average quality: {original_avg_quality:.1f}%")
        
        # STEP 3: Apply REAL compression
        print(f"\n🔧 STEP 3: APPLYING REAL COMPRESSION")
        print("=" * 40)
        
        compression_start = time.time()
        
        # Apply real quantization
        quantized_model = apply_real_quantization(model)
        
        compression_time = time.time() - compression_start
        compressed_ram = monitor_ram()
        compressed_size_mb = calculate_model_size(quantized_model)
        
        # Calculate real compression ratio
        compression_ratio = original_size_mb / compressed_size_mb
        
        print(f"✅ REAL compression applied!")
        print(f"   Compression time: {compression_time:.1f}s")
        print(f"   Original size: {original_size_mb:.1f}MB")
        print(f"   Compressed size: {compressed_size_mb:.1f}MB")
        print(f"   REAL compression ratio: {compression_ratio:.1f}×")
        print(f"   RAM usage: {compressed_ram:.1f}MB")
        
        # STEP 4: Save compressed model (REAL save/load test)
        print(f"\n💾 STEP 4: SAVING COMPRESSED MODEL")
        print("=" * 40)
        
        compressed_model_path = "D:/Loop/compressed_gpt2_large"
        os.makedirs(compressed_model_path, exist_ok=True)
        
        print("🔄 Saving compressed model to disk...")
        
        # Save quantized model
        torch.save(quantized_model.state_dict(), f"{compressed_model_path}/quantized_model.pt")
        tokenizer.save_pretrained(compressed_model_path)
        
        # Clear from memory
        del model
        del quantized_model
        gc.collect()
        
        cleared_ram = monitor_ram()
        print(f"✅ Compressed model saved!")
        print(f"   Path: {compressed_model_path}")
        print(f"   RAM after clearing: {cleared_ram:.1f}MB")
        
        # STEP 5: Load compressed model (REAL load test)
        print(f"\n📥 STEP 5: LOADING COMPRESSED MODEL")
        print("=" * 40)
        
        print("🔄 Loading compressed model from disk...")
        
        # Load compressed model
        loaded_tokenizer = GPT2Tokenizer.from_pretrained(compressed_model_path)
        loaded_tokenizer.pad_token = loaded_tokenizer.eos_token
        
        # Create model structure and load quantized weights
        loaded_model = GPT2LMHeadModel.from_pretrained('gpt2-large')
        loaded_quantized_model = apply_real_quantization(loaded_model)
        
        # Load saved state
        loaded_quantized_model.load_state_dict(torch.load(f"{compressed_model_path}/quantized_model.pt"))
        
        loaded_ram = monitor_ram()
        loaded_size_mb = calculate_model_size(loaded_quantized_model)
        
        print(f"✅ Compressed model loaded from disk!")
        print(f"   Loaded size: {loaded_size_mb:.1f}MB")
        print(f"   RAM usage: {loaded_ram:.1f}MB")
        
        # STEP 6: Test compressed model
        print(f"\n🔄 STEP 6: TESTING COMPRESSED MODEL INFERENCE")
        print("=" * 50)
        
        print("Testing compressed model...")
        compressed_results = test_model_inference(loaded_quantized_model, loaded_tokenizer, test_prompts)
        
        compressed_successful = [r for r in compressed_results if r.get('success', False)]
        compressed_success_rate = len(compressed_successful) / len(test_prompts) * 100
        compressed_avg_time = np.mean([r['inference_time'] for r in compressed_successful]) if compressed_successful else 0
        compressed_avg_quality = np.mean([r['quality_score'] for r in compressed_successful]) if compressed_successful else 0
        
        print(f"✅ Compressed model results:")
        print(f"   Success rate: {compressed_success_rate:.1f}%")
        print(f"   Average time: {compressed_avg_time:.2f}s")
        print(f"   Average quality: {compressed_avg_quality:.1f}%")
        
        # STEP 7: Compare results (REAL accuracy measurement)
        print(f"\n📊 STEP 7: REAL ACCURACY COMPARISON")
        print("=" * 40)
        
        print("Comparing original vs compressed outputs:")
        
        accuracy_preserved = 0
        
        for i, (orig, comp) in enumerate(zip(original_results, compressed_results)):
            if orig.get('success') and comp.get('success'):
                orig_text = orig['generated']
                comp_text = comp['generated']
                
                # Simple similarity check
                orig_words = set(orig_text.lower().split())
                comp_words = set(comp_text.lower().split())
                
                if orig_words and comp_words:
                    similarity = len(orig_words & comp_words) / len(orig_words | comp_words)
                    if similarity > 0.3:  # 30% word overlap
                        accuracy_preserved += 1
                
                print(f"  Test {i+1}:")
                print(f"    Original:   '{orig_text[:50]}...'")
                print(f"    Compressed: '{comp_text[:50]}...'")
                print(f"    Similar: {'✅' if similarity > 0.3 else '❌'}")
        
        accuracy_percentage = (accuracy_preserved / len(test_prompts)) * 100
        
        # FINAL ASSESSMENT
        print(f"\n🎯 FINAL REAL TESTING RESULTS")
        print("=" * 40)
        
        print(f"✅ COMPRESSION RESULTS:")
        print(f"   Original size: {original_size_mb:.1f}MB")
        print(f"   Compressed size: {compressed_size_mb:.1f}MB")
        print(f"   REAL compression ratio: {compression_ratio:.1f}×")
        
        print(f"\n✅ PERFORMANCE RESULTS:")
        print(f"   Original success rate: {original_success_rate:.1f}%")
        print(f"   Compressed success rate: {compressed_success_rate:.1f}%")
        print(f"   Performance retention: {(compressed_success_rate/original_success_rate*100):.1f}%")
        
        print(f"\n✅ QUALITY RESULTS:")
        print(f"   Original quality: {original_avg_quality:.1f}%")
        print(f"   Compressed quality: {compressed_avg_quality:.1f}%")
        print(f"   Quality retention: {(compressed_avg_quality/original_avg_quality*100):.1f}%")
        
        print(f"\n✅ ACCURACY RESULTS:")
        print(f"   Output similarity: {accuracy_percentage:.1f}%")
        
        print(f"\n✅ MEMORY RESULTS:")
        print(f"   Peak RAM usage: {max(original_ram, compressed_ram, loaded_ram):.1f}MB")
        print(f"   Fits in 8GB: {'✅ YES' if max(original_ram, compressed_ram, loaded_ram) < 8000 else '❌ NO'}")
        
        # Success criteria
        good_compression = compression_ratio >= 2.0
        good_performance = compressed_success_rate >= 80
        good_quality = compressed_avg_quality >= 60
        good_accuracy = accuracy_percentage >= 50
        fits_memory = max(original_ram, compressed_ram, loaded_ram) < 8000
        
        if good_compression and good_performance and good_quality and good_accuracy and fits_memory:
            print(f"\n🎉 COMPLETE REAL TESTING SUCCESSFUL!")
            print(f"   ✅ Good compression: {compression_ratio:.1f}×")
            print(f"   ✅ Good performance: {compressed_success_rate:.1f}%")
            print(f"   ✅ Good quality: {compressed_avg_quality:.1f}%")
            print(f"   ✅ Good accuracy: {accuracy_percentage:.1f}%")
            print(f"   ✅ Fits in memory: {max(original_ram, compressed_ram, loaded_ram):.1f}MB")
            print(f"\n🚀 READY TO SCALE TO LARGER MODELS!")
        else:
            print(f"\n❌ Some issues found:")
            if not good_compression: print(f"   ❌ Low compression: {compression_ratio:.1f}×")
            if not good_performance: print(f"   ❌ Low performance: {compressed_success_rate:.1f}%")
            if not good_quality: print(f"   ❌ Low quality: {compressed_avg_quality:.1f}%")
            if not good_accuracy: print(f"   ❌ Low accuracy: {accuracy_percentage:.1f}%")
            if not fits_memory: print(f"   ❌ Too much memory: {max(original_ram, compressed_ram, loaded_ram):.1f}MB")
        
        return {
            'compression_ratio': compression_ratio,
            'success_rate': compressed_success_rate,
            'quality_score': compressed_avg_quality,
            'accuracy_percentage': accuracy_percentage,
            'ram_usage': max(original_ram, compressed_ram, loaded_ram)
        }
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function"""
    
    print("🔥🔥🔥 COMPLETE REAL COMPRESSION + INFERENCE TESTING 🔥🔥🔥")
    print("=" * 80)
    
    results = complete_real_testing()
    
    if results:
        print(f"\n🎉 REAL TESTING COMPLETE!")
        print(f"This demonstrates the complete methodology for real compression testing.")
        print(f"The same approach can be applied to larger models like 7B, 65B, 675B.")
    else:
        print(f"\n❌ Testing failed - need to debug")

if __name__ == "__main__":
    main()
