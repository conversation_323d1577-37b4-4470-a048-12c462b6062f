#!/usr/bin/env python3
"""
🔥 ROBUST MISTRAL 7B DOWNLOAD WITH RETRY
========================================

Download Mistral 7B with automatic retry, resume, and error handling.
Handles network timeouts and connection issues.
"""

import os
import time
import requests
from huggingface_hub import login, hf_hub_download, HfApi
import psutil
from pathlib import Path

def robust_download_mistral():
    """Download Mistral 7B with robust error handling"""
    
    print("🔥 ROBUST MISTRAL 7B DOWNLOAD")
    print("=" * 35)
    
    # Your token
    token = "*************************************"
    
    try:
        # Login
        print("🔐 Logging in...")
        login(token=token)
        print("✅ Login successful!")
        
    except Exception as e:
        print(f"❌ Login failed: {e}")
        return None
    
    # Download directory
    download_dir = "D:/Loop/downloaded_models/mistral-7b-v0.1"
    model_name = "mistralai/Mistral-7B-v0.1"
    
    os.makedirs(download_dir, exist_ok=True)
    
    # Files to download (essential ones only)
    files_to_download = [
        "config.json",
        "generation_config.json", 
        "tokenizer.json",
        "tokenizer.model",
        "tokenizer_config.json",
        "special_tokens_map.json",
        # Only download safetensors (more reliable than .bin)
        "model-00001-of-00002.safetensors",
        "model-00002-of-00002.safetensors",
        "model.safetensors.index.json"
    ]
    
    print(f"📁 Download directory: {download_dir}")
    print(f"📥 Files to download: {len(files_to_download)}")
    
    # Download each file with retry
    success_count = 0
    
    for i, filename in enumerate(files_to_download):
        print(f"\n📥 [{i+1}/{len(files_to_download)}] Downloading {filename}...")
        
        file_path = os.path.join(download_dir, filename)
        
        # Check if already exists
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            print(f"✅ Already exists: {filename} ({file_size:.1f}MB)")
            success_count += 1
            continue
        
        # Download with retry
        max_retries = 5
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                print(f"   Attempt {retry_count + 1}/{max_retries}...")
                
                start_time = time.time()
                
                # Download file
                downloaded_path = hf_hub_download(
                    repo_id=model_name,
                    filename=filename,
                    local_dir=download_dir,
                    token=token,
                    resume_download=True
                )
                
                download_time = time.time() - start_time
                file_size = os.path.getsize(downloaded_path) / (1024 * 1024)
                
                print(f"✅ Downloaded: {filename} ({file_size:.1f}MB in {download_time:.1f}s)")
                success_count += 1
                break
                
            except Exception as e:
                retry_count += 1
                print(f"❌ Attempt {retry_count} failed: {str(e)[:100]}...")
                
                if retry_count < max_retries:
                    wait_time = retry_count * 10  # Exponential backoff
                    print(f"⏳ Waiting {wait_time}s before retry...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ Failed to download {filename} after {max_retries} attempts")
    
    print(f"\n📊 DOWNLOAD SUMMARY:")
    print(f"✅ Successfully downloaded: {success_count}/{len(files_to_download)} files")
    
    if success_count >= 7:  # At least config + tokenizer + 1 model file
        print(f"✅ Minimum files downloaded - model should work!")
        return download_dir
    else:
        print(f"❌ Insufficient files downloaded")
        return None

def verify_partial_download(download_dir):
    """Verify what we have downloaded"""
    
    print(f"\n🔍 VERIFYING PARTIAL DOWNLOAD")
    print("=" * 35)
    
    if not os.path.exists(download_dir):
        print("❌ Download directory not found")
        return False
    
    files = os.listdir(download_dir)
    total_size_gb = 0
    
    essential_files = ["config.json", "tokenizer.json"]
    model_files = []
    
    print(f"📁 Files in {download_dir}:")
    
    for file in files:
        file_path = os.path.join(download_dir, file)
        if os.path.isfile(file_path):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            total_size_gb += size_mb / 1024
            
            if file.endswith('.safetensors'):
                model_files.append((file, size_mb))
            
            print(f"   {file}: {size_mb:.1f}MB")
    
    print(f"\n📊 Total downloaded: {total_size_gb:.1f}GB")
    
    # Check if we have minimum required files
    has_config = any(f in files for f in essential_files)
    has_model = len(model_files) > 0
    
    if has_config and has_model:
        print(f"✅ Minimum requirements met:")
        print(f"   Config files: ✅")
        print(f"   Model files: {len(model_files)} files")
        return True
    else:
        print(f"❌ Missing essential files")
        return False

def test_partial_model(download_dir):
    """Test if partial model can be loaded"""
    
    print(f"\n🔄 TESTING PARTIAL MODEL")
    print("=" * 30)
    
    try:
        from transformers import AutoTokenizer, AutoConfig
        
        # Test config
        config = AutoConfig.from_pretrained(download_dir)
        print(f"✅ Config loaded: {config.model_type}")
        
        # Test tokenizer
        tokenizer = AutoTokenizer.from_pretrained(download_dir)
        print(f"✅ Tokenizer loaded: {len(tokenizer)} tokens")
        
        # Test tokenization
        test_text = "Hello world"
        tokens = tokenizer.encode(test_text)
        decoded = tokenizer.decode(tokens)
        print(f"✅ Tokenization works: '{test_text}' → '{decoded}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def main():
    """Main robust download function"""
    
    print("🔥🔥🔥 ROBUST MISTRAL 7B DOWNLOAD 🔥🔥🔥")
    print("=" * 55)
    
    # Check disk space
    try:
        disk_usage = psutil.disk_usage('D:/')
        free_gb = disk_usage.free / (1024**3)
        print(f"💾 Available space: {free_gb:.1f}GB")
        
        if free_gb < 20:
            print(f"❌ Need at least 20GB, have {free_gb:.1f}GB")
            return
            
    except Exception as e:
        print(f"⚠️ Could not check disk space: {e}")
    
    # Download with retry
    download_dir = robust_download_mistral()
    
    if download_dir:
        # Verify what we got
        if verify_partial_download(download_dir):
            # Test if it works
            if test_partial_model(download_dir):
                print(f"\n🎯 PARTIAL SUCCESS!")
                print(f"✅ Location: {download_dir}")
                print(f"✅ Model partially downloaded and working")
                print(f"✅ Can proceed with streaming weights test")
                
                print(f"\n💡 NOTE: We have enough to test streaming weights!")
                print(f"   Even partial model files can test compression algorithms")
                
            else:
                print(f"\n⚠️ PARTIAL DOWNLOAD - MODEL TEST FAILED")
                print(f"Files downloaded but model won't load")
        else:
            print(f"\n❌ INSUFFICIENT FILES DOWNLOADED")
    else:
        print(f"\n❌ DOWNLOAD COMPLETELY FAILED")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"1. Try download again with better internet")
    print(f"2. Or use existing GPT-2 Large for streaming weights test")
    print(f"3. Streaming weights algorithm works on any model size")

if __name__ == "__main__":
    main()
