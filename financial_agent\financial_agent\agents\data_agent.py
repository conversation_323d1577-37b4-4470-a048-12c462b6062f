"""
Data Collection Agent

Responsible for:
- Fetching market data using yfinance
- Data cleaning and normalization
- Storing historical data
- Managing data quality
"""
import asyncio
import json
import logging
import time
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from dataclasses import dataclass
import pytz
import traceback

from .base_agent import BaseAgent, AgentResponse

logger = logging.getLogger(__name__)

@dataclass
class MarketDataRequest:
    """Request for market data"""
    symbols: List[str]
    interval: str = '1h'  # 1m, 5m, 15m, 30m, 1h, 1d, 1wk, 1mo
    period: str = '1y'    # 1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max
    start: Optional[str] = None  # Overrides period if specified
    end: Optional[str] = None    # Defaults to now

@dataclass
class OHLCVData:
    """Standardized OHLCV data structure"""
    symbol: str
    interval: str
    timestamp: List[float]
    open: List[float]
    high: List[float]
    low: List[float]
    close: List[float]
    volume: List[float]
    
    def to_dataframe(self) -> pd.DataFrame:
        """Convert to pandas DataFrame"""
        return pd.DataFrame({
            'timestamp': pd.to_datetime(self.timestamp, unit='s', utc=True),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        }).set_index('timestamp')

class DataCollectionAgent(BaseAgent):
    """
    Data Collection Agent using yfinance
    
    Handles fetching, processing, and storing market data from Yahoo Finance.
    """
    
    def __init__(
        self,
        name: str = 'data',
        llm_wrapper=None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the Data Collection Agent
        
        Args:
            name: Agent name
            llm_wrapper: Optional LLM wrapper for data processing
            config: Configuration dictionary
        """
        super().__init__(name, llm_wrapper)
        self.config = config or {}
        self.cache = {}
        self.cache_ttl = self.config.get('cache_ttl', 300)  # 5 minutes default
        
        # yfinance specific settings
        self.valid_intervals = ['1m', '5m', '15m', '30m', '1h', '1d', '1wk', '1mo']
        self.valid_periods = ['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max']
        
        logger.info("Initialized DataCollectionAgent with yfinance")
    
    async def start(self) -> AgentResponse:
        """Start the data collection agent"""
        if self.is_running:
            return AgentResponse(
                success=False,
                error=f"{self.name} is already running"
            )
        
        try:
            # Initialize any async resources here if needed
            self.is_running = True
            logger.info(f"Started {self.name} agent")
            return AgentResponse(success=True, data={"status": "started"})
            
        except Exception as e:
            error_msg = f"Failed to start DataCollectionAgent: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return AgentResponse(success=False, error=error_msg)
    
    async def stop(self) -> AgentResponse:
        """Stop the data collection agent"""
        if not self.is_running:
            return AgentResponse(
                success=False,
                error=f"{self.name} is not running"
            )
        
        try:
            # Clean up any async resources here if needed
            self.is_running = False
            logger.info(f"Stopped {self.name} agent")
            return AgentResponse(success=True, data={"status": "stopped"})
            
        except Exception as e:
            error_msg = f"Error stopping DataCollectionAgent: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return AgentResponse(success=False, error=error_msg)
    
    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        Process data collection request
        
        Args:
            input_data: Should contain 'request' key with MarketDataRequest
            
        Returns:
            AgentResponse with collected data or error
        """
        if not self.is_running:
            return AgentResponse(
                success=False,
                error="DataCollectionAgent is not running"
            )
        
        try:
            # Parse request
            request_data = input_data.get('request', {})
            request = MarketDataRequest(
                symbols=request_data.get('symbols', []),
                interval=request_data.get('interval', '1h'),
                period=request_data.get('period', '1y'),
                start=request_data.get('start'),
                end=request_data.get('end')
            )
            
            if not request.symbols:
                return AgentResponse(
                    success=False,
                    error="No symbols provided in request"
                )
            
            # Validate interval
            if request.interval not in self.valid_intervals:
                return AgentResponse(
                    success=False,
                    error=f"Invalid interval. Must be one of: {', '.join(self.valid_intervals)}"
                )
            
            # Validate period if provided
            if request.period and request.period not in self.valid_periods:
                return AgentResponse(
                    success=False,
                    error=f"Invalid period. Must be one of: {', '.join(self.valid_periods)}"
                )
            
            # Fetch data for all symbols
            results = {}
            for symbol in request.symbols:
                data = await self.fetch_ohlcv(
                    symbol=symbol,
                    interval=request.interval,
                    period=request.period,
                    start=request.start,
                    end=request.end
                )
                if data:
                    results[symbol] = data.to_dataframe().to_dict(orient='records')
            
            return AgentResponse(
                success=True,
                data={
                    'results': results,
                    'timestamp': datetime.utcnow().isoformat()
                }
            )
            
        except Exception as e:
            error_msg = f"Error in data collection: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return AgentResponse(success=False, error=error_msg)
    
    async def fetch_ohlcv(
        self,
        symbol: str,
        interval: str = '1h',
        period: str = '1y',
        start: Optional[str] = None,
        end: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout: int = 30  # seconds
    ) -> Optional[OHLCVData]:
        logger.debug(f"[DataCollectionAgent] Entering fetch_ohlcv(symbol={symbol}, interval={interval}, period={period}, "
                   f"start={start}, end={end}, max_retries={max_retries}, retry_delay={retry_delay}, timeout={timeout})")
        """
        Fetch OHLCV data using yfinance with retry logic, timeout, and better error handling
        
        Args:
            symbol: Stock/crypto symbol (e.g., 'AAPL', 'BTC-USD')
            interval: Data interval (1m, 5m, 15m, 30m, 1h, 1d, 1wk, 1mo)
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            start: Start date (YYYY-MM-DD or datetime)
            end: End date (YYYY-MM-DD or datetime)
            max_retries: Maximum number of retry attempts
            retry_delay: Base delay between retries in seconds (exponential backoff)
            timeout: Maximum time in seconds to wait for a response
            
        Returns:
            OHLCVData object or None if failed after all retries
        """
        if not self.is_running:
            logger.warning(f"{self.name} is not running")
            logger.debug("[DataCollectionAgent] Exiting fetch_ohlcv() - agent not running")
            return None
            
        # Generate cache key
        cache_key = f"{symbol}_{interval}_{period}_{start}_{end}"
        
        # Check cache first
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            cache_age = datetime.now().timestamp() - cache_entry['timestamp']
            if cache_age < self.cache_ttl:
                logger.debug(f"[DataCollectionAgent] Cache hit for {cache_key} (age: {cache_age:.1f}s < {self.cache_ttl}s)")
                logger.debug("[DataCollectionAgent] Exiting fetch_ohlcv() - returning cached data")
                return cache_entry['data']
            else:
                # Cache expired
                logger.debug(f"[DataCollectionAgent] Cache expired for {cache_key} (age: {cache_age:.1f}s >= {self.cache_ttl}s)")
                del self.cache[cache_key]
        
        # Validate interval
        if interval not in self.valid_intervals:
            logger.error(f"[DataCollectionAgent] Invalid interval: {interval}. Must be one of {self.valid_intervals}")
            logger.debug("[DataCollectionAgent] Exiting fetch_ohlcv() - invalid interval")
            return None
            
        # Validate period if provided (only if start/end not provided)
        if not start and not end and period not in self.valid_periods:
            logger.warning(f"[DataCollectionAgent] Invalid period: {period}. Using default '1y'")
            period = '1y'
            
        logger.debug(f"[DataCollectionAgent] Using period: {period}")
        
        last_error = None
        for attempt in range(max_retries):
            attempt_start_time = time.time()
            try:
                # Add delay between retries (except first attempt)
                if attempt > 0:
                    delay = retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                    logger.info(f"[DataCollectionAgent] Retry attempt {attempt + 1}/{max_retries} for {symbol} in {delay:.1f}s...")
                    await asyncio.sleep(delay)
                else:
                    logger.info(f"[DataCollectionAgent] Fetching data for {symbol} (attempt {attempt + 1}/{max_retries})...")
                
                logger.debug(f"[DataCollectionAgent] Attempt {attempt + 1} starting at {time.strftime('%H:%M:%S')}")
                
                # Create ticker object
                ticker = yf.Ticker(symbol)
                logger.debug(f"[DataCollectionAgent] Created yf.Ticker for {symbol}")
                
                # Download historical data with timeout
                try:
                    # Define the fetch function with proper error handling
                    def fetch_data():
                        try:
                            # Build parameters
                            params = {
                                'interval': interval,
                                'period': period if not start else None,
                                'start': start,
                                'end': end,
                                'actions': False,
                                'auto_adjust': True,
                                'prepost': False
                            }
                            # Remove None values
                            params = {k: v for k, v in params.items() if v is not None}
                            
                            logger.debug(f"[DataCollectionAgent] yfinance.history() params: {params}")
                            
                            # Fetch data with timeout
                            start_time = time.time()
                            logger.debug(f"[DataCollectionAgent] Starting yfinance.history() at {time.strftime('%H:%M:%S')}")
                            result = ticker.history(**params)
                            elapsed = time.time() - start_time
                            logger.debug(f"[DataCollectionAgent] yfinance.history() completed in {elapsed:.2f}s")
                            logger.debug(f"[DataCollectionAgent] Fetched {len(result)} rows for {symbol}")
                            return result
                            
                        except Exception as e:
                            logger.error(f"[DataCollectionAgent] Error in fetch_data for {symbol}: {str(e)}")
                            logger.error(traceback.format_exc())
                            raise
                    
                    # Get the event loop
                    loop = asyncio.get_event_loop()
                    
                    # Run with timeout
                    logger.debug(f"[DataCollectionAgent] Starting asyncio.wait_for with timeout={timeout}s")
                    df = await asyncio.wait_for(
                        loop.run_in_executor(None, fetch_data),
                        timeout=timeout
                    )
                    
                    logger.debug(f"[DataCollectionAgent] Data fetch completed, rows: {len(df) if df is not None else 0}")
                    
                    if df is None or df.empty:
                        logger.warning(f"[DataCollectionAgent] Empty data returned for {symbol}")
                        raise ValueError("Empty DataFrame returned from yfinance")
                    
                    # Validate we have the expected columns
                    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
                    if not all(col in df.columns for col in required_columns):
                        error_msg = f"Missing required columns. Expected: {required_columns}, Got: {df.columns.tolist()}"
                        logger.error(f"[DataCollectionAgent] {error_msg}")
                        raise ValueError(error_msg)
                    
                    logger.debug("[DataCollectionAgent] Converting to OHLCVData...")
                    
                    # Convert to OHLCVData
                    ohlcv = OHLCVData(
                        symbol=symbol,
                        interval=interval,
                        timestamp=df.index.astype('int64') // 10**9,  # Convert to Unix timestamp
                        open=df['Open'].fillna(method='ffill').values.tolist(),
                        high=df['High'].fillna(method='ffill').values.tolist(),
                        low=df['Low'].fillna(method='ffill').values.tolist(),
                        close=df['Close'].fillna(method='ffill').values.tolist(),
                        volume=df['Volume'].fillna(0).values.tolist()
                    )
                    
                    logger.debug(f"[DataCollectionAgent] Caching result with key: {cache_key}")
                    
                    # Cache the result
                    self.cache[cache_key] = {
                        'timestamp': datetime.now().timestamp(),
                        'data': ohlcv
                    }
                    
                    # Clean up old cache entries if needed
                    if len(self.cache) > 1000:
                        logger.debug("[DataCollectionAgent] Cleaning up old cache entries...")
                        self._cleanup_old_cache()
                    
                    elapsed = time.time() - attempt_start_time
                    logger.info(f"[DataCollectionAgent] Successfully fetched {len(ohlcv.timestamp)} data points for {symbol} in {elapsed:.2f}s")
                    logger.debug("[DataCollectionAgent] Exiting fetch_ohlcv() - success")
                    return ohlcv
                    
                except asyncio.TimeoutError as e:
                    elapsed = time.time() - attempt_start_time
                    logger.warning(f"[DataCollectionAgent] Timeout while fetching data for {symbol} "
                                 f"(attempt {attempt + 1}/{max_retries}, elapsed: {elapsed:.2f}s)")
                    last_error = TimeoutError(f"Timeout after {timeout} seconds")
                    continue
                    
            except Exception as e:
                elapsed = time.time() - attempt_start_time
                last_error = e
                logger.warning(f"[DataCollectionAgent] Attempt {attempt + 1} failed for {symbol} after {elapsed:.2f}s: {str(e)}")
                if attempt == max_retries - 1:  # Last attempt
                    logger.error(f"[DataCollectionAgent] Failed to fetch data for {symbol} after {max_retries} attempts")
                    if last_error:
                        logger.error(f"[DataCollectionAgent] Last error: {str(last_error)}", exc_info=True)
                continue
        
        logger.debug("[DataCollectionAgent] Exiting fetch_ohlcv() - all attempts failed")
        return None
    
    def _cleanup_old_cache(self, max_cache_size: int = 1000):
        """Remove old cache entries if cache gets too large"""
        if len(self.cache) > max_cache_size:
            # Sort cache by timestamp (oldest first) and remove excess
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: x[1].get('timestamp', 0)
            )
            
            # Remove oldest entries
            for key, _ in sorted_entries[:len(self.cache) - max_cache_size]:
                del self.cache[key]
                
            logger.debug(f"Cleaned up cache. New size: {len(self.cache)}")
    
    def get_market_hours(self, date: Optional[datetime] = None) -> tuple[datetime, datetime]:
        """
        Get market open and close times for a given date
        
        Args:
            date: Date to check (default: today)
            
        Returns:
            Tuple of (market_open, market_close) datetimes in UTC
        """
        date = date or datetime.now()
        market_open = datetime(
            date.year, date.month, date.day, 9, 30, 
            tzinfo=pytz.timezone('US/Eastern')
        ).astimezone(pytz.UTC)
        market_close = datetime(
            date.year, date.month, date.day, 16, 0, 
            tzinfo=pytz.timezone('US/Eastern')
        ).astimezone(pytz.UTC)
        return market_open, market_close
    
    def is_market_open(self) -> bool:
        """Check if the market is currently open"""
        now = datetime.now(pytz.UTC)
        market_open, market_close = self.get_market_hours(now)
        return market_open <= now <= market_close and now.weekday() < 5
